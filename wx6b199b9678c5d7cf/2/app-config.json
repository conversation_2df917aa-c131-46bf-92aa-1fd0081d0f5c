{"subPackages": [{"root": "pages/cast/"}, {"root": "pages/course/"}, {"root": "pages/class/"}, {"root": "pages/article/"}, {"root": "pages/question/"}, {"root": "pages/qbank/"}, {"root": "pages/paper/"}, {"root": "pages/cate/"}, {"root": "pages/lesson/"}, {"root": "pages/teacher/"}, {"root": "pages/camp/"}, {"root": "pages/user/"}, {"root": "pages/share/"}, {"root": "pages/reseller/"}, {"root": "pages/plugin/", "plugins": {"qyssdk-plugin": {"version": "1.2.21", "provider": "wxaf8f505686ddf66a", "subpackage": "/pages/plugin/"}}}, {"root": "pages/system/"}, {"root": "pages/sale/"}, {"root": "pages/filepacket/"}, {"root": "pages/shop/"}, {"root": "pages/community/"}, {"root": "pages/word/"}, {"root": "pages/knowledge/"}], "entryPagePath": "pages/index.html", "pages": ["pages/index", "pages/landing-page", "pages/buy/index", "pages/redirect", "pages/forbidden", "pages/test", "pages/webview/index", "pages/wepage/index", "pages/cast/audio", "pages/cast/video", "pages/cast/index", "pages/cast/list", "pages/cast/room-list", "pages/cast/plugin", "pages/cast/video-nvue", "pages/course/index", "pages/course/doc", "pages/course/audio", "pages/course/video", "pages/course/comment/list", "pages/course/comment/add", "pages/course/list", "pages/course/file", "pages/course/record", "pages/class/home", "pages/class/list", "pages/class/index", "pages/article/list", "pages/article/index", "pages/question/list", "pages/question/battle", "pages/question/index", "pages/question/entry", "pages/qbank/list", "pages/qbank/chapter", "pages/qbank/notes", "pages/qbank/history", "pages/qbank/index", "pages/qbank/dayPractice/list", "pages/qbank/dayPractice/result", "pages/qbank/detail", "pages/qbank/child", "pages/qbank/rank", "pages/paper/list", "pages/paper/record", "pages/paper/rank", "pages/cate/list", "pages/lesson/list", "pages/lesson/index", "pages/teacher/index", "pages/camp/list", "pages/camp/detail", "pages/camp/calendar", "pages/camp/menu/rank-list", "pages/camp/menu/add-assistant", "pages/camp/question", "pages/camp/evaluate/list", "pages/camp/evaluate/add", "pages/camp/question-list", "pages/user/login", "pages/user/auth", "pages/user/register", "pages/user/info", "pages/user/bind-account", "pages/user/order", "pages/user/vip", "pages/user/vip/goods", "pages/user/vip/coupon", "pages/user/course", "pages/user/lesson", "pages/user/qbank", "pages/user/class", "pages/user/camp", "pages/user/point/index", "pages/user/point/task", "pages/user/point/tasks", "pages/user/point/record", "pages/user/point/rule", "pages/user/point/shop", "pages/user/point/lottery", "pages/user/purse/purse", "pages/user/purse/bill", "pages/user/setting", "pages/user/advance-setting", "pages/user/account-setting", "pages/user/kefu", "pages/user/about", "pages/user/privacy", "pages/user/password", "pages/user/password/edit", "pages/user/phone", "pages/user/coupon/list", "pages/user/coupon/list2", "pages/user/coupon/invalid-list", "pages/user/cash/index", "pages/user/cash/record", "pages/user/cash/account", "pages/user/profit/record", "pages/user/address/list", "pages/user/address/edit", "pages/user/order/index", "pages/user/order/afterSale", "pages/user/order/payment", "pages/user/login-confirm", "pages/user/relation", "pages/user/deactive", "pages/user/filepacket", "pages/user/prize", "pages/user/store/info", "pages/user/manage/goods", "pages/share/list", "pages/share/card", "pages/share/piiic", "pages/share/register", "pages/share/app", "pages/reseller/index", "pages/reseller/apply", "pages/reseller/branch", "pages/reseller/customer", "pages/reseller/customer-detail", "pages/reseller/live-list", "pages/reseller/goods-list", "pages/reseller/wepage-list", "pages/reseller/coupon-list", "pages/reseller/order-list", "pages/reseller/card", "pages/reseller/store/writeoff", "pages/reseller/store/writeoff-goods", "pages/reseller/store/writeoff-record", "pages/reseller/store/writeoff-stat", "pages/reseller/store/writeoff-user", "pages/reseller/store/live", "pages/reseller/store/live-detail", "pages/reseller/store/order", "pages/plugin/agreement/index", "pages/plugin/agreement/list", "pages/plugin/agreement/doc", "pages/plugin/info/collection", "pages/plugin/wappay/index", "pages/plugin/__plugin__/wxaf8f505686ddf66a/pages/personal-sign/doc/doc", "pages/plugin/__plugin__/wxaf8f505686ddf66a/pages/personal-auth/auth", "pages/plugin/__plugin__/wxaf8f505686ddf66a/pages/enterprise-auth/auth", "pages/plugin/__plugin__/wxaf8f505686ddf66a/pages/authority/index", "pages/plugin/__plugin__/wxaf8f505686ddf66a/pages/signature/auth", "pages/system/version", "pages/system/message", "pages/system/feedback", "pages/system/feedback-list", "pages/system/canvas", "pages/system/search", "pages/system/goods_member", "pages/system/mpwelink", "pages/system/search-list", "pages/sale/coupon", "pages/sale/coupon/list", "pages/sale/key/use", "pages/sale/tuan", "pages/sale/joinByGroup", "pages/sale/joinByKey", "pages/sale/tuan/list", "pages/sale/discount/list", "pages/sale/discount/index", "pages/sale/cart", "pages/sale/haggle/list", "pages/sale/haggle/index", "pages/sale/haggle/assist", "pages/sale/seckill/list", "pages/filepacket/index", "pages/filepacket/pointsExchange", "pages/filepacket/pdfViewer", "pages/filepacket/joinBuy", "pages/shop/index", "pages/shop/product/list", "pages/shop/product/index", "pages/shop/prescription/index", "pages/shop/prescription/patient", "pages/shop/prescription/agreement", "pages/shop/evaluate/list", "pages/shop/evaluate/add", "pages/community/index", "pages/community/group/index", "pages/community/group/create", "pages/community/topic/index", "pages/community/topic/list", "pages/community/post/index", "pages/community/post/list", "pages/community/post/create", "pages/community/user/center", "pages/word/index", "pages/word/plan/edit", "pages/word/plan/list", "pages/word/plan/report", "pages/word/plan/share", "pages/word/book", "pages/word/wrong", "pages/word/rank", "pages/word/study", "pages/word/test/test", "pages/word/test/report", "pages/word/test/index", "pages/word/test/record", "pages/word/fix", "pages/word/search", "pages/knowledge/index"], "page": {"pages/index.html": {"window": {"disableScroll": true, "navigationBarTextStyle": "black"}}, "pages/landing-page.html": {"window": {}}, "pages/buy/index.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/redirect.html": {"window": {}}, "pages/forbidden.html": {"window": {}}, "pages/test.html": {"window": {}}, "pages/webview/index.html": {"window": {}}, "pages/wepage/index.html": {"window": {}}, "pages/cast/audio.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/cast/video.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/cast/index.html": {"window": {"disableScroll": false, "navigationBarTextStyle": "black"}}, "pages/cast/list.html": {"window": {"disableScroll": true}}, "pages/cast/room-list.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/cast/plugin.html": {"window": {}}, "pages/cast/video-nvue.html": {"window": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, "pages/course/index.html": {"window": {"disableScroll": true, "disableSwipeBack": true, "navigationBarTextStyle": "black", "navigationBarTitleText": "课程详情"}}, "pages/course/doc.html": {"window": {}}, "pages/course/audio.html": {"window": {}}, "pages/course/video.html": {"window": {"navigationBarTextStyle": "black", "navigationBarTitleText": "课时详情"}}, "pages/course/comment/list.html": {"window": {}}, "pages/course/comment/add.html": {"window": {}}, "pages/course/list.html": {"window": {}}, "pages/course/file.html": {"window": {}}, "pages/course/record.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/class/home.html": {"window": {}}, "pages/class/list.html": {"window": {}}, "pages/class/index.html": {"window": {"disableScroll": true, "navigationBarTitleText": "班级详情", "navigationBarTextStyle": "black"}}, "pages/article/list.html": {"window": {}}, "pages/article/index.html": {"window": {"navigationBarTitleText": "文章详情", "navigationBarTextStyle": "black"}}, "pages/question/list.html": {"window": {}}, "pages/question/battle.html": {"window": {"disableScroll": true, "navigationBarTextStyle": "black"}}, "pages/question/index.html": {"window": {"navigationBarBackgroundColor": "#000", "disableScroll": true, "disableSwipeBack": true, "popGesture": "none", "__warning__": "无效的 page.json [\"popGesture\"]"}}, "pages/question/entry.html": {"window": {}}, "pages/qbank/list.html": {"window": {}}, "pages/qbank/chapter.html": {"window": {}}, "pages/qbank/notes.html": {"window": {}}, "pages/qbank/history.html": {"window": {}}, "pages/qbank/index.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/qbank/dayPractice/list.html": {"window": {}}, "pages/qbank/dayPractice/result.html": {"window": {}}, "pages/qbank/detail.html": {"window": {}}, "pages/qbank/child.html": {"window": {}}, "pages/qbank/rank.html": {"window": {}}, "pages/paper/list.html": {"window": {}}, "pages/paper/record.html": {"window": {}}, "pages/paper/rank.html": {"window": {"enablePullDownRefresh": false, "navigationBarTextStyle": "black"}}, "pages/cate/list.html": {"window": {}}, "pages/lesson/list.html": {"window": {}}, "pages/lesson/index.html": {"window": {}}, "pages/teacher/index.html": {"window": {}}, "pages/camp/list.html": {"window": {}}, "pages/camp/detail.html": {"window": {}}, "pages/camp/calendar.html": {"window": {}}, "pages/camp/menu/rank-list.html": {"window": {}}, "pages/camp/menu/add-assistant.html": {"window": {}}, "pages/camp/question.html": {"window": {}}, "pages/camp/evaluate/list.html": {"window": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, "pages/camp/evaluate/add.html": {"window": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, "pages/camp/question-list.html": {"window": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, "pages/user/login.html": {"window": {}}, "pages/user/auth.html": {"window": {}}, "pages/user/register.html": {"window": {}}, "pages/user/info.html": {"window": {}}, "pages/user/bind-account.html": {"window": {}}, "pages/user/order.html": {"window": {}}, "pages/user/vip.html": {"window": {}}, "pages/user/vip/goods.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/user/vip/coupon.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/user/course.html": {"window": {}}, "pages/user/lesson.html": {"window": {}}, "pages/user/qbank.html": {"window": {}}, "pages/user/class.html": {"window": {}}, "pages/user/camp.html": {"window": {}}, "pages/user/point/index.html": {"window": {}}, "pages/user/point/task.html": {"window": {}}, "pages/user/point/tasks.html": {"window": {}}, "pages/user/point/record.html": {"window": {}}, "pages/user/point/rule.html": {"window": {}}, "pages/user/point/shop.html": {"window": {}}, "pages/user/point/lottery.html": {"window": {}}, "pages/user/purse/purse.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/user/purse/bill.html": {"window": {}}, "pages/user/setting.html": {"window": {}}, "pages/user/advance-setting.html": {"window": {}}, "pages/user/account-setting.html": {"window": {}}, "pages/user/kefu.html": {"window": {}}, "pages/user/about.html": {"window": {}}, "pages/user/privacy.html": {"window": {}}, "pages/user/password.html": {"window": {}}, "pages/user/password/edit.html": {"window": {}}, "pages/user/phone.html": {"window": {}}, "pages/user/coupon/list.html": {"window": {}}, "pages/user/coupon/list2.html": {"window": {}}, "pages/user/coupon/invalid-list.html": {"window": {}}, "pages/user/cash/index.html": {"window": {}}, "pages/user/cash/record.html": {"window": {}}, "pages/user/cash/account.html": {"window": {}}, "pages/user/profit/record.html": {"window": {}}, "pages/user/address/list.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/user/address/edit.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/user/order/index.html": {"window": {}}, "pages/user/order/afterSale.html": {"window": {}}, "pages/user/order/payment.html": {"window": {}}, "pages/user/login-confirm.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/user/relation.html": {"window": {}}, "pages/user/deactive.html": {"window": {}}, "pages/user/filepacket.html": {"window": {}}, "pages/user/prize.html": {"window": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, "pages/user/store/info.html": {"window": {}}, "pages/user/manage/goods.html": {"window": {}}, "pages/share/list.html": {"window": {}}, "pages/share/card.html": {"window": {"disableScroll": true}}, "pages/share/piiic.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/share/register.html": {"window": {}}, "pages/share/app.html": {"window": {"disableScroll": true}}, "pages/reseller/index.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/reseller/apply.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/reseller/branch.html": {"window": {}}, "pages/reseller/customer.html": {"window": {}}, "pages/reseller/customer-detail.html": {"window": {}}, "pages/reseller/live-list.html": {"window": {}}, "pages/reseller/goods-list.html": {"window": {}}, "pages/reseller/wepage-list.html": {"window": {}}, "pages/reseller/coupon-list.html": {"window": {}}, "pages/reseller/order-list.html": {"window": {}}, "pages/reseller/card.html": {"window": {}}, "pages/reseller/store/writeoff.html": {"window": {}}, "pages/reseller/store/writeoff-goods.html": {"window": {}}, "pages/reseller/store/writeoff-record.html": {"window": {}}, "pages/reseller/store/writeoff-stat.html": {"window": {}}, "pages/reseller/store/writeoff-user.html": {"window": {}}, "pages/reseller/store/live.html": {"window": {}}, "pages/reseller/store/live-detail.html": {"window": {}}, "pages/reseller/store/order.html": {"window": {}}, "pages/plugin/agreement/index.html": {"window": {"navigationBarTextStyle": "black", "pageOrientation": "auto"}}, "pages/plugin/agreement/list.html": {"window": {}}, "pages/plugin/agreement/doc.html": {"window": {}}, "pages/plugin/info/collection.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/plugin/wappay/index.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/system/version.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/system/message.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/system/feedback.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/system/feedback-list.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/system/canvas.html": {"window": {}}, "pages/system/search.html": {"window": {}}, "pages/system/goods_member.html": {"window": {}}, "pages/system/mpwelink.html": {"window": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, "pages/system/search-list.html": {"window": {}}, "pages/sale/coupon.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/sale/coupon/list.html": {"window": {}}, "pages/sale/key/use.html": {"window": {}}, "pages/sale/tuan.html": {"window": {}}, "pages/sale/joinByGroup.html": {"window": {}}, "pages/sale/joinByKey.html": {"window": {}}, "pages/sale/tuan/list.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/sale/discount/list.html": {"window": {}}, "pages/sale/discount/index.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/sale/cart.html": {"window": {}}, "pages/sale/haggle/list.html": {"window": {}}, "pages/sale/haggle/index.html": {"window": {}}, "pages/sale/haggle/assist.html": {"window": {}}, "pages/sale/seckill/list.html": {"window": {}}, "pages/filepacket/index.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/filepacket/pointsExchange.html": {"window": {}}, "pages/filepacket/pdfViewer.html": {"window": {}}, "pages/filepacket/joinBuy.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/shop/index.html": {"window": {}}, "pages/shop/product/list.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/shop/product/index.html": {"window": {"navigationBarTextStyle": "black", "navigationBarTitleText": "商品详情"}}, "pages/shop/prescription/index.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/shop/prescription/patient.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/shop/prescription/agreement.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/shop/evaluate/list.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/shop/evaluate/add.html": {"window": {}}, "pages/community/index.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/community/group/index.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/community/group/create.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/community/topic/index.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/community/topic/list.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/community/post/index.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/community/post/list.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/community/post/create.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/community/user/center.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/word/index.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/word/plan/edit.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/word/plan/list.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/word/plan/report.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/word/plan/share.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/word/book.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/word/wrong.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/word/rank.html": {"window": {}}, "pages/word/study.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/word/test/test.html": {"window": {}}, "pages/word/test/report.html": {"window": {}}, "pages/word/test/index.html": {"window": {}}, "pages/word/test/record.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/word/fix.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/word/search.html": {"window": {"navigationBarTextStyle": "black"}}, "pages/knowledge/index.html": {"window": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, "pages/plugin/__plugin__/wxaf8f505686ddf66a/pages/personal-sign/doc/doc.html": {"window": {"navigationBarTitleText": "签署页面", "navigationStyle": "default", "renderer": "webview"}}, "pages/plugin/__plugin__/wxaf8f505686ddf66a/pages/personal-auth/auth.html": {"window": {"navigationBarTitleText": "个人实名认证页面", "navigationStyle": "default", "renderer": "webview"}}, "pages/plugin/__plugin__/wxaf8f505686ddf66a/pages/enterprise-auth/auth.html": {"window": {"navigationBarTitleText": "企业认证页面", "navigationStyle": "default", "renderer": "webview"}}, "pages/plugin/__plugin__/wxaf8f505686ddf66a/pages/authority/index.html": {"window": {"navigationBarTitleText": "授权页面", "navigationStyle": "default", "renderer": "webview"}}, "pages/plugin/__plugin__/wxaf8f505686ddf66a/pages/signature/auth.html": {"window": {"navigationBarTitleText": "静默签授权页面", "navigationStyle": "default", "renderer": "webview"}}}, "global": {"window": {"navigationBarTextStyle": "white", "navigationStyle": "custom", "navigationBarTitleText": "羽仪软考"}}, "networkTimeout": {"request": 30000}, "plugins": {"xk-user-info": {"version": "0.0.1", "provider": "wx0c7b3a8857aa0f2a", "subpackage": "__APP__"}, "qyssdk-plugin": {"version": "1.2.21", "provider": "wxaf8f505686ddf66a", "subpackage": "/pages/plugin/"}}, "resizable": true, "requiredBackgroundModes": ["audio"], "ext": {"host": "https://api.ixunke.cn/yuyiruankao", "httpDNSServiceId": ""}, "usingShopPlugin": true, "renderer": {"allUsed": ["webview"], "default": "webview"}, "requiredPrivateInfos": ["<PERSON><PERSON><PERSON><PERSON>"], "componentFramework": {"allUsed": ["exparser"], "default": "exparser"}}