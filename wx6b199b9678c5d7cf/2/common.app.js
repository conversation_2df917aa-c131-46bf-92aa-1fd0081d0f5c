var __wxAppData = __wxAppData || {};
var __wxAppCode__ = __wxAppCode__ || {};
var global = global || {};
var __WXML_GLOBAL__ = __WXML_GLOBAL__ || {
    entrys: {},
    defines: {},
    modules: {},
    ops: [],
    wxs_nf_init: undefined,
    total_ops: 0
};
var Component = Component || function() {};
var definePlugin = definePlugin || function() {};
var requirePlugin = requirePlugin || function() {};
var Behavior = Behavior || function() {};
var __vd_version_info__ = __vd_version_info__ || {};
var __GWX_GLOBAL__ = __GWX_GLOBAL__ || {};
if (this && this.__g === undefined) Object.defineProperty(this, "__g", {
    configurable: false,
    enumerable: false,
    writable: false,
    value: function() {
        function D(e, t) {
            if (typeof t != "undefined") e.children.push(t)
        }

        function S(e) {
            if (typeof e != "undefined") return {
                tag: "virtual",
                wxKey: e,
                children: []
            };
            return {
                tag: "virtual",
                children: []
            }
        }

        function v(e) {
            return {
                tag: "wx-" + e,
                attr: {},
                children: [],
                n: [],
                raw: {},
                generics: {}
            }
        }

        function e(e, t) {
            t && e.properities.push(t)
        }

        function t(e, t, r) {
            return typeof e[r] != "undefined" ? e[r] : t[r]
        }

        function u(e) {
            console.warn("WXMLRT_" + g + ":" + e)
        }

        function r(e, t) {
            u(t + ":-1:-1:-1: Template `" + e + "` is being called recursively, will be stop.")
        }
        var s = console.warn;
        var n = console.log;

        function o() {
            function e() {}
            e.prototype = {
                hn: function(e, t) {
                    if (typeof e == "object") {
                        var r = 0;
                        var n = false,
                            o = false;
                        for (var a in e) {
                            n = n | a === "__value__";
                            o = o | a === "__wxspec__";
                            r++;
                            if (r > 2) break
                        }
                        return r == 2 && n && o && (t || e.__wxspec__ !== "m" || this.hn(e.__value__) === "h") ? "h" : "n"
                    }
                    return "n"
                },
                nh: function(e, t) {
                    return {
                        __value__: e,
                        __wxspec__: t ? t : true
                    }
                },
                rv: function(e) {
                    return this.hn(e, true) === "n" ? e : this.rv(e.__value__)
                },
                hm: function(e) {
                    if (typeof e == "object") {
                        var t = 0;
                        var r = false,
                            n = false;
                        for (var o in e) {
                            r = r | o === "__value__";
                            n = n | o === "__wxspec__";
                            t++;
                            if (t > 2) break
                        }
                        return t == 2 && r && n && (e.__wxspec__ === "m" || this.hm(e.__value__))
                    }
                    return false
                }
            };
            return new e
        }
        var A = o();

        function T(e) {
            var t = e.split("\n " + " " + " " + " ");
            for (var r = 0; r < t.length; ++r) {
                if (0 == r) continue;
                if (")" === t[r][t[r].length - 1]) t[r] = t[r].replace(/\s\(.*\)$/, "");
                else t[r] = "at anonymous function"
            }
            return t.join("\n " + " " + " " + " ")
        }

        function a(M) {
            function m(e, t, r, n, o) {
                var a = false;
                var i = e[0][1];
                var p, u, l, f, v, c;
                switch (i) {
                    case "?:":
                        p = x(e[1], t, r, n, o, a);
                        l = M && A.hn(p) === "h";
                        f = A.rv(p) ? x(e[2], t, r, n, o, a) : x(e[3], t, r, n, o, a);
                        f = l && A.hn(f) === "n" ? A.nh(f, "c") : f;
                        return f;
                        break;
                    case "&&":
                        p = x(e[1], t, r, n, o, a);
                        l = M && A.hn(p) === "h";
                        f = A.rv(p) ? x(e[2], t, r, n, o, a) : A.rv(p);
                        f = l && A.hn(f) === "n" ? A.nh(f, "c") : f;
                        return f;
                        break;
                    case "||":
                        p = x(e[1], t, r, n, o, a);
                        l = M && A.hn(p) === "h";
                        f = A.rv(p) ? A.rv(p) : x(e[2], t, r, n, o, a);
                        f = l && A.hn(f) === "n" ? A.nh(f, "c") : f;
                        return f;
                        break;
                    case "+":
                    case "*":
                    case "/":
                    case "%":
                    case "|":
                    case "^":
                    case "&":
                    case "===":
                    case "==":
                    case "!=":
                    case "!==":
                    case ">=":
                    case "<=":
                    case ">":
                    case "<":
                    case "<<":
                    case ">>":
                        p = x(e[1], t, r, n, o, a);
                        u = x(e[2], t, r, n, o, a);
                        l = M && (A.hn(p) === "h" || A.hn(u) === "h");
                        switch (i) {
                            case "+":
                                f = A.rv(p) + A.rv(u);
                                break;
                            case "*":
                                f = A.rv(p) * A.rv(u);
                                break;
                            case "/":
                                f = A.rv(p) / A.rv(u);
                                break;
                            case "%":
                                f = A.rv(p) % A.rv(u);
                                break;
                            case "|":
                                f = A.rv(p) | A.rv(u);
                                break;
                            case "^":
                                f = A.rv(p) ^ A.rv(u);
                                break;
                            case "&":
                                f = A.rv(p) & A.rv(u);
                                break;
                            case "===":
                                f = A.rv(p) === A.rv(u);
                                break;
                            case "==":
                                f = A.rv(p) == A.rv(u);
                                break;
                            case "!=":
                                f = A.rv(p) != A.rv(u);
                                break;
                            case "!==":
                                f = A.rv(p) !== A.rv(u);
                                break;
                            case ">=":
                                f = A.rv(p) >= A.rv(u);
                                break;
                            case "<=":
                                f = A.rv(p) <= A.rv(u);
                                break;
                            case ">":
                                f = A.rv(p) > A.rv(u);
                                break;
                            case "<":
                                f = A.rv(p) < A.rv(u);
                                break;
                            case "<<":
                                f = A.rv(p) << A.rv(u);
                                break;
                            case ">>":
                                f = A.rv(p) >> A.rv(u);
                                break;
                            default:
                                break
                        }
                        return l ? A.nh(f, "c") : f;
                        break;
                    case "-":
                        p = e.length === 3 ? x(e[1], t, r, n, o, a) : 0;
                        u = e.length === 3 ? x(e[2], t, r, n, o, a) : x(e[1], t, r, n, o, a);
                        l = M && (A.hn(p) === "h" || A.hn(u) === "h");
                        f = l ? A.rv(p) - A.rv(u) : p - u;
                        return l ? A.nh(f, "c") : f;
                        break;
                    case "!":
                        p = x(e[1], t, r, n, o, a);
                        l = M && A.hn(p) == "h";
                        f = !A.rv(p);
                        return l ? A.nh(f, "c") : f;
                    case "~":
                        p = x(e[1], t, r, n, o, a);
                        l = M && A.hn(p) == "h";
                        f = ~A.rv(p);
                        return l ? A.nh(f, "c") : f;
                    default:
                        s("unrecognized op" + i)
                }
            }

            function x(e, t, r, n, o, a) {
                var i = e[0];
                var p = false;
                if (typeof a !== "undefined") o.ap = a;
                if (typeof i === "object") {
                    var u = i[0];
                    var l, f, v, c, s, y, b, d, h, _, g;
                    switch (u) {
                        case 2:
                            return m(e, t, r, n, o);
                            break;
                        case 4:
                            return x(e[1], t, r, n, o, p);
                            break;
                        case 5:
                            switch (e.length) {
                                case 2:
                                    l = x(e[1], t, r, n, o, p);
                                    return M ? [l] : [A.rv(l)];
                                    return [l];
                                    break;
                                case 1:
                                    return [];
                                    break;
                                default:
                                    l = x(e[1], t, r, n, o, p);
                                    v = x(e[2], t, r, n, o, p);
                                    l.push(M ? v : A.rv(v));
                                    return l;
                                    break
                            }
                            break;
                        case 6:
                            l = x(e[1], t, r, n, o);
                            var w = o.ap;
                            h = A.hn(l) === "h";
                            f = h ? A.rv(l) : l;
                            o.is_affected |= h;
                            if (M) {
                                if (f === null || typeof f === "undefined") {
                                    return h ? A.nh(undefined, "e") : undefined
                                }
                                v = x(e[2], t, r, n, o, p);
                                _ = A.hn(v) === "h";
                                c = _ ? A.rv(v) : v;
                                o.ap = w;
                                o.is_affected |= _;
                                if (c === null || typeof c === "undefined" || c === "__proto__" || c === "prototype" || c === "caller") {
                                    return h || _ ? A.nh(undefined, "e") : undefined
                                }
                                y = f[c];
                                if (typeof y === "function" && !w) y = undefined;
                                g = A.hn(y) === "h";
                                o.is_affected |= g;
                                return h || _ ? g ? y : A.nh(y, "e") : y
                            } else {
                                if (f === null || typeof f === "undefined") {
                                    return undefined
                                }
                                v = x(e[2], t, r, n, o, p);
                                _ = A.hn(v) === "h";
                                c = _ ? A.rv(v) : v;
                                o.ap = w;
                                o.is_affected |= _;
                                if (c === null || typeof c === "undefined" || c === "__proto__" || c === "prototype" || c === "caller") {
                                    return undefined
                                }
                                y = f[c];
                                if (typeof y === "function" && !w) y = undefined;
                                g = A.hn(y) === "h";
                                o.is_affected |= g;
                                return g ? A.rv(y) : y
                            }
                        case 7:
                            switch (e[1][0]) {
                                case 11:
                                    o.is_affected |= A.hn(n) === "h";
                                    return n;
                                case 3:
                                    b = A.rv(r);
                                    d = A.rv(t);
                                    v = e[1][1];
                                    if (n && n.f && n.f.hasOwnProperty(v)) {
                                        l = n.f;
                                        o.ap = true
                                    } else {
                                        l = b && b.hasOwnProperty(v) ? r : d && d.hasOwnProperty(v) ? t : undefined
                                    }
                                    if (M) {
                                        if (l) {
                                            h = A.hn(l) === "h";
                                            f = h ? A.rv(l) : l;
                                            y = f[v];
                                            g = A.hn(y) === "h";
                                            o.is_affected |= h || g;
                                            y = h && !g ? A.nh(y, "e") : y;
                                            return y
                                        }
                                    } else {
                                        if (l) {
                                            h = A.hn(l) === "h";
                                            f = h ? A.rv(l) : l;
                                            y = f[v];
                                            g = A.hn(y) === "h";
                                            o.is_affected |= h || g;
                                            return A.rv(y)
                                        }
                                    }
                                    return undefined
                            }
                            break;
                        case 8:
                            l = {};
                            l[e[1]] = x(e[2], t, r, n, o, p);
                            return l;
                            break;
                        case 9:
                            l = x(e[1], t, r, n, o, p);
                            v = x(e[2], t, r, n, o, p);

                            function O(e, t, r) {
                                var n, o;
                                h = A.hn(e) === "h";
                                _ = A.hn(t) === "h";
                                f = A.rv(e);
                                c = A.rv(t);
                                for (var a in c) {
                                    if (r || !f.hasOwnProperty(a)) {
                                        f[a] = M ? _ ? A.nh(c[a], "e") : c[a] : A.rv(c[a])
                                    }
                                }
                                return e
                            }
                            var s = l;
                            var j = true;
                            if (typeof e[1][0] === "object" && e[1][0][0] === 10) {
                                l = v;
                                v = s;
                                j = false
                            }
                            if (typeof e[1][0] === "object" && e[1][0][0] === 10) {
                                var P = {};
                                return O(O(P, l, j), v, j)
                            } else return O(l, v, j);
                            break;
                        case 10:
                            l = x(e[1], t, r, n, o, p);
                            l = M ? l : A.rv(l);
                            return l;
                            break;
                        case 12:
                            var P;
                            l = x(e[1], t, r, n, o);
                            if (!o.ap) {
                                return M && A.hn(l) === "h" ? A.nh(P, "f") : P
                            }
                            var w = o.ap;
                            v = x(e[2], t, r, n, o, p);
                            o.ap = w;
                            h = A.hn(l) === "h";
                            _ = N(v);
                            f = A.rv(l);
                            c = A.rv(v);
                            snap_bb = K(c, "nv_");
                            try {
                                P = typeof f === "function" ? K(f.apply(null, snap_bb)) : undefined
                            } catch (t) {
                                t.message = t.message.replace(/nv_/g, "");
                                t.stack = t.stack.substring(0, t.stack.indexOf("\n", t.stack.lastIndexOf("at nv_")));
                                t.stack = t.stack.replace(/\snv_/g, " ");
                                t.stack = T(t.stack);
                                if (n.debugInfo) {
                                    t.stack += "\n " + " " + " " + " at " + n.debugInfo[0] + ":" + n.debugInfo[1] + ":" + n.debugInfo[2];
                                    console.error(t)
                                }
                                P = undefined
                            }
                            return M && (_ || h) ? A.nh(P, "f") : P
                    }
                } else {
                    if (i === 3 || i === 1) return e[1];
                    else if (i === 11) {
                        var l = "";
                        for (var D = 1; D < e.length; D++) {
                            var S = A.rv(x(e[D], t, r, n, o, p));
                            l += typeof S === "undefined" ? "" : S
                        }
                        return l
                    }
                }
            }

            function e(e, t, r, n, o, a) {
                if (e[0] == "11182016") {
                    n.debugInfo = e[2];
                    return x(e[1], t, r, n, o, a)
                } else {
                    n.debugInfo = null;
                    return x(e, t, r, n, o, a)
                }
            }
            return e
        }
        var f = a(true);
        var c = a(false);

        function i(e, t, r, n, o, a, i, p) {
            {
                var u = {
                    is_affected: false
                };
                var l = f(t, r, n, o, u);
                if (JSON.stringify(l) != JSON.stringify(a) || u.is_affected != p) {
                    console.warn("A. " + e + " get result " + JSON.stringify(l) + ", " + u.is_affected + ", but " + JSON.stringify(a) + ", " + p + " is expected")
                }
            } {
                var u = {
                    is_affected: false
                };
                var l = c(t, r, n, o, u);
                if (JSON.stringify(l) != JSON.stringify(i) || u.is_affected != p) {
                    console.warn("B. " + e + " get result " + JSON.stringify(l) + ", " + u.is_affected + ", but " + JSON.stringify(i) + ", " + p + " is expected")
                }
            }
        }

        function y(e, t, r, n, o, a, i, p, u) {
            var l = A.hn(e) === "n";
            var f = A.rv(n);
            var v = f.hasOwnProperty(i);
            var c = f.hasOwnProperty(p);
            var s = f[i];
            var y = f[p];
            var b = Object.prototype.toString.call(A.rv(e));
            var d = b[8];
            if (d === "N" && b[10] === "l") d = "X";
            var h;
            if (l) {
                if (d === "A") {
                    var _;
                    for (var g = 0; g < e.length; g++) {
                        f[i] = e[g];
                        f[p] = l ? g : A.nh(g, "h");
                        _ = A.rv(e[g]);
                        var w = u && _ ? u === "*this" ? _ : A.rv(_[u]) : undefined;
                        h = S(w);
                        D(a, h);
                        t(r, f, h, o)
                    }
                } else if (d === "O") {
                    var g = 0;
                    var _;
                    for (var O in e) {
                        f[i] = e[O];
                        f[p] = l ? O : A.nh(O, "h");
                        _ = A.rv(e[O]);
                        var w = u && _ ? u === "*this" ? _ : A.rv(_[u]) : undefined;
                        h = S(w);
                        D(a, h);
                        t(r, f, h, o);
                        g++
                    }
                } else if (d === "S") {
                    for (var g = 0; g < e.length; g++) {
                        f[i] = e[g];
                        f[p] = l ? g : A.nh(g, "h");
                        h = S(e[g] + g);
                        D(a, h);
                        t(r, f, h, o)
                    }
                } else if (d === "N") {
                    for (var g = 0; g < e; g++) {
                        f[i] = g;
                        f[p] = l ? g : A.nh(g, "h");
                        h = S(g);
                        D(a, h);
                        t(r, f, h, o)
                    }
                } else {}
            } else {
                var j = A.rv(e);
                var _, P;
                if (d === "A") {
                    for (var g = 0; g < j.length; g++) {
                        P = j[g];
                        P = A.hn(P) === "n" ? A.nh(P, "h") : P;
                        _ = A.rv(P);
                        f[i] = P;
                        f[p] = l ? g : A.nh(g, "h");
                        var w = u && _ ? u === "*this" ? _ : A.rv(_[u]) : undefined;
                        h = S(w);
                        D(a, h);
                        t(r, f, h, o)
                    }
                } else if (d === "O") {
                    var g = 0;
                    for (var O in j) {
                        P = j[O];
                        P = A.hn(P) === "n" ? A.nh(P, "h") : P;
                        _ = A.rv(P);
                        f[i] = P;
                        f[p] = l ? O : A.nh(O, "h");
                        var w = u && _ ? u === "*this" ? _ : A.rv(_[u]) : undefined;
                        h = S(w);
                        D(a, h);
                        t(r, f, h, o);
                        g++
                    }
                } else if (d === "S") {
                    for (var g = 0; g < j.length; g++) {
                        P = A.nh(j[g], "h");
                        f[i] = P;
                        f[p] = l ? g : A.nh(g, "h");
                        h = S(e[g] + g);
                        D(a, h);
                        t(r, f, h, o)
                    }
                } else if (d === "N") {
                    for (var g = 0; g < j; g++) {
                        P = A.nh(g, "h");
                        f[i] = P;
                        f[p] = l ? g : A.nh(g, "h");
                        h = S(g);
                        D(a, h);
                        t(r, f, h, o)
                    }
                } else {}
            }
            if (v) {
                f[i] = s
            } else {
                delete f[i]
            }
            if (c) {
                f[p] = y
            } else {
                delete f[p]
            }
        }

        function N(e) {
            if (A.hn(e) == "h") return true;
            if (typeof e !== "object") return false;
            for (var t in e) {
                if (e.hasOwnProperty(t)) {
                    if (N(e[t])) return true
                }
            }
            return false
        }

        function b(e, t, r, n, o) {
            var a = false;
            var i = K(n, "", 2);
            if (o.ap && i && i.constructor === Function) {
                t = "$wxs:" + t;
                e.attr["$gdc"] = K
            }
            if (o.is_affected || N(n)) {
                e.n.push(t);
                e.raw[t] = n
            }
            e.attr[t] = i
        }

        function d(e, t, r, n, o, a) {
            a.opindex = r;
            var i = {},
                p;
            var u = c(z[r], n, o, a, i);
            b(e, t, r, u, i)
        }

        function h(e, t, r, n, o, a, i) {
            i.opindex = n;
            var p = {},
                u;
            var l = c(e[n], o, a, i, p);
            b(t, r, n, l, p)
        }

        function p(e, t, r, n) {
            n.opindex = e;
            var o = {};
            var a = c(z[e], t, r, n, o);
            return a && a.constructor === Function ? undefined : a
        }

        function l(e, t, r, n, o) {
            o.opindex = t;
            var a = {};
            var i = c(e[t], r, n, o, a);
            return i && i.constructor === Function ? undefined : i
        }

        function _(e, t, r, n, o) {
            var o = o || {};
            n.opindex = e;
            return f(z[e], t, r, n, o)
        }

        function w(e, t, r, n, o, a) {
            var a = a || {};
            o.opindex = t;
            return f(e[t], r, n, o, a)
        }

        function O(e, t, r, n, o, a, i, p, u) {
            var l = {};
            var f = _(e, r, n, o);
            y(f, t, r, n, o, a, i, p, u)
        }

        function j(e, t, r, n, o, a, i, p, u, l) {
            var f = {};
            var v = w(e, t, n, o, a);
            y(v, r, n, o, a, i, p, u, l)
        }

        function P(e, t, r, n, o, a) {
            var i = v(e);
            var p = 0;
            for (var u = 0; u < t.length; u += 2) {
                if (p + t[u + 1] < 0) {
                    i.attr[t[u]] = true
                } else {
                    d(i, t[u], p + t[u + 1], n, o, a);
                    if (p === 0) p = t[u + 1]
                }
            }
            for (var u = 0; u < r.length; u += 2) {
                if (p + r[u + 1] < 0) {
                    i.generics[r[u]] = ""
                } else {
                    var l = c(z[p + r[u + 1]], n, o, a);
                    if (l != "") l = "wx-" + l;
                    i.generics[r[u]] = l;
                    if (p === 0) p = r[u + 1]
                }
            }
            return i
        }

        function M(e, t, r, n, o, a, i) {
            var p = v(t);
            var u = 0;
            for (var l = 0; l < r.length; l += 2) {
                if (u + r[l + 1] < 0) {
                    p.attr[r[l]] = true
                } else {
                    h(e, p, r[l], u + r[l + 1], o, a, i);
                    if (u === 0) u = r[l + 1]
                }
            }
            for (var l = 0; l < n.length; l += 2) {
                if (u + n[l + 1] < 0) {
                    p.generics[n[l]] = ""
                } else {
                    var f = c(e[u + n[l + 1]], o, a, i);
                    if (f != "") f = "wx-" + f;
                    p.generics[n[l]] = f;
                    if (u === 0) u = n[l + 1]
                }
            }
            return p
        }
        var m = function() {
            if (typeof __WXML_GLOBAL__ === "undefined" || undefined === __WXML_GLOBAL__.wxs_nf_init) {
                x();
                C();
                k();
                U();
                I();
                L();
                E();
                R();
                F()
            }
            if (typeof __WXML_GLOBAL__ !== "undefined") __WXML_GLOBAL__.wxs_nf_init = true
        };
        var x = function() {
            Object.defineProperty(Object.prototype, "nv_constructor", {
                writable: true,
                value: "Object"
            });
            Object.defineProperty(Object.prototype, "nv_toString", {
                writable: true,
                value: function() {
                    return "[object Object]"
                }
            })
        };
        var C = function() {
            Object.defineProperty(Function.prototype, "nv_constructor", {
                writable: true,
                value: "Function"
            });
            Object.defineProperty(Function.prototype, "nv_length", {get: function() {
                    return this.length
                },
                set: function() {}
            });
            Object.defineProperty(Function.prototype, "nv_toString", {
                writable: true,
                value: function() {
                    return "[function Function]"
                }
            })
        };
        var k = function() {
            Object.defineProperty(Array.prototype, "nv_toString", {
                writable: true,
                value: function() {
                    return this.nv_join()
                }
            });
            Object.defineProperty(Array.prototype, "nv_join", {
                writable: true,
                value: function(e) {
                    e = undefined == e ? "," : e;
                    var t = "";
                    for (var r = 0; r < this.length; ++r) {
                        if (0 != r) t += e;
                        if (null == this[r] || undefined == this[r]) t += "";
                        else if (typeof this[r] == "function") t += this[r].nv_toString();
                        else if (typeof this[r] == "object" && this[r].nv_constructor === "Array") t += this[r].nv_join();
                        else t += this[r].toString()
                    }
                    return t
                }
            });
            Object.defineProperty(Array.prototype, "nv_constructor", {
                writable: true,
                value: "Array"
            });
            Object.defineProperty(Array.prototype, "nv_concat", {
                writable: true,
                value: Array.prototype.concat
            });
            Object.defineProperty(Array.prototype, "nv_pop", {
                writable: true,
                value: Array.prototype.pop
            });
            Object.defineProperty(Array.prototype, "nv_push", {
                writable: true,
                value: Array.prototype.push
            });
            Object.defineProperty(Array.prototype, "nv_reverse", {
                writable: true,
                value: Array.prototype.reverse
            });
            Object.defineProperty(Array.prototype, "nv_shift", {
                writable: true,
                value: Array.prototype.shift
            });
            Object.defineProperty(Array.prototype, "nv_slice", {
                writable: true,
                value: Array.prototype.slice
            });
            Object.defineProperty(Array.prototype, "nv_sort", {
                writable: true,
                value: Array.prototype.sort
            });
            Object.defineProperty(Array.prototype, "nv_splice", {
                writable: true,
                value: Array.prototype.splice
            });
            Object.defineProperty(Array.prototype, "nv_unshift", {
                writable: true,
                value: Array.prototype.unshift
            });
            Object.defineProperty(Array.prototype, "nv_indexOf", {
                writable: true,
                value: Array.prototype.indexOf
            });
            Object.defineProperty(Array.prototype, "nv_lastIndexOf", {
                writable: true,
                value: Array.prototype.lastIndexOf
            });
            Object.defineProperty(Array.prototype, "nv_every", {
                writable: true,
                value: Array.prototype.every
            });
            Object.defineProperty(Array.prototype, "nv_some", {
                writable: true,
                value: Array.prototype.some
            });
            Object.defineProperty(Array.prototype, "nv_forEach", {
                writable: true,
                value: Array.prototype.forEach
            });
            Object.defineProperty(Array.prototype, "nv_map", {
                writable: true,
                value: Array.prototype.map
            });
            Object.defineProperty(Array.prototype, "nv_filter", {
                writable: true,
                value: Array.prototype.filter
            });
            Object.defineProperty(Array.prototype, "nv_reduce", {
                writable: true,
                value: Array.prototype.reduce
            });
            Object.defineProperty(Array.prototype, "nv_reduceRight", {
                writable: true,
                value: Array.prototype.reduceRight
            });
            Object.defineProperty(Array.prototype, "nv_length", {get: function() {
                    return this.length
                },
                set: function(e) {
                    this.length = e
                }
            })
        };
        var U = function() {
            Object.defineProperty(String.prototype, "nv_constructor", {
                writable: true,
                value: "String"
            });
            Object.defineProperty(String.prototype, "nv_toString", {
                writable: true,
                value: String.prototype.toString
            });
            Object.defineProperty(String.prototype, "nv_valueOf", {
                writable: true,
                value: String.prototype.valueOf
            });
            Object.defineProperty(String.prototype, "nv_charAt", {
                writable: true,
                value: String.prototype.charAt
            });
            Object.defineProperty(String.prototype, "nv_charCodeAt", {
                writable: true,
                value: String.prototype.charCodeAt
            });
            Object.defineProperty(String.prototype, "nv_concat", {
                writable: true,
                value: String.prototype.concat
            });
            Object.defineProperty(String.prototype, "nv_indexOf", {
                writable: true,
                value: String.prototype.indexOf
            });
            Object.defineProperty(String.prototype, "nv_lastIndexOf", {
                writable: true,
                value: String.prototype.lastIndexOf
            });
            Object.defineProperty(String.prototype, "nv_localeCompare", {
                writable: true,
                value: String.prototype.localeCompare
            });
            Object.defineProperty(String.prototype, "nv_match", {
                writable: true,
                value: String.prototype.match
            });
            Object.defineProperty(String.prototype, "nv_replace", {
                writable: true,
                value: String.prototype.replace
            });
            Object.defineProperty(String.prototype, "nv_search", {
                writable: true,
                value: String.prototype.search
            });
            Object.defineProperty(String.prototype, "nv_slice", {
                writable: true,
                value: String.prototype.slice
            });
            Object.defineProperty(String.prototype, "nv_split", {
                writable: true,
                value: String.prototype.split
            });
            Object.defineProperty(String.prototype, "nv_substring", {
                writable: true,
                value: String.prototype.substring
            });
            Object.defineProperty(String.prototype, "nv_toLowerCase", {
                writable: true,
                value: String.prototype.toLowerCase
            });
            Object.defineProperty(String.prototype, "nv_toLocaleLowerCase", {
                writable: true,
                value: String.prototype.toLocaleLowerCase
            });
            Object.defineProperty(String.prototype, "nv_toUpperCase", {
                writable: true,
                value: String.prototype.toUpperCase
            });
            Object.defineProperty(String.prototype, "nv_toLocaleUpperCase", {
                writable: true,
                value: String.prototype.toLocaleUpperCase
            });
            Object.defineProperty(String.prototype, "nv_trim", {
                writable: true,
                value: String.prototype.trim
            });
            Object.defineProperty(String.prototype, "nv_length", {get: function() {
                    return this.length
                },
                set: function(e) {
                    this.length = e
                }
            })
        };
        var I = function() {
            Object.defineProperty(Boolean.prototype, "nv_constructor", {
                writable: true,
                value: "Boolean"
            });
            Object.defineProperty(Boolean.prototype, "nv_toString", {
                writable: true,
                value: Boolean.prototype.toString
            });
            Object.defineProperty(Boolean.prototype, "nv_valueOf", {
                writable: true,
                value: Boolean.prototype.valueOf
            })
        };
        var L = function() {
            Object.defineProperty(Number, "nv_MAX_VALUE", {
                writable: false,
                value: Number.MAX_VALUE
            });
            Object.defineProperty(Number, "nv_MIN_VALUE", {
                writable: false,
                value: Number.MIN_VALUE
            });
            Object.defineProperty(Number, "nv_NEGATIVE_INFINITY", {
                writable: false,
                value: Number.NEGATIVE_INFINITY
            });
            Object.defineProperty(Number, "nv_POSITIVE_INFINITY", {
                writable: false,
                value: Number.POSITIVE_INFINITY
            });
            Object.defineProperty(Number.prototype, "nv_constructor", {
                writable: true,
                value: "Number"
            });
            Object.defineProperty(Number.prototype, "nv_toString", {
                writable: true,
                value: Number.prototype.toString
            });
            Object.defineProperty(Number.prototype, "nv_toLocaleString", {
                writable: true,
                value: Number.prototype.toLocaleString
            });
            Object.defineProperty(Number.prototype, "nv_valueOf", {
                writable: true,
                value: Number.prototype.valueOf
            });
            Object.defineProperty(Number.prototype, "nv_toFixed", {
                writable: true,
                value: Number.prototype.toFixed
            });
            Object.defineProperty(Number.prototype, "nv_toExponential", {
                writable: true,
                value: Number.prototype.toExponential
            });
            Object.defineProperty(Number.prototype, "nv_toPrecision", {
                writable: true,
                value: Number.prototype.toPrecision
            })
        };
        var E = function() {
            Object.defineProperty(Math, "nv_E", {
                writable: false,
                value: Math.E
            });
            Object.defineProperty(Math, "nv_LN10", {
                writable: false,
                value: Math.LN10
            });
            Object.defineProperty(Math, "nv_LN2", {
                writable: false,
                value: Math.LN2
            });
            Object.defineProperty(Math, "nv_LOG2E", {
                writable: false,
                value: Math.LOG2E
            });
            Object.defineProperty(Math, "nv_LOG10E", {
                writable: false,
                value: Math.LOG10E
            });
            Object.defineProperty(Math, "nv_PI", {
                writable: false,
                value: Math.PI
            });
            Object.defineProperty(Math, "nv_SQRT1_2", {
                writable: false,
                value: Math.SQRT1_2
            });
            Object.defineProperty(Math, "nv_SQRT2", {
                writable: false,
                value: Math.SQRT2
            });
            Object.defineProperty(Math, "nv_abs", {
                writable: false,
                value: Math.abs
            });
            Object.defineProperty(Math, "nv_acos", {
                writable: false,
                value: Math.acos
            });
            Object.defineProperty(Math, "nv_asin", {
                writable: false,
                value: Math.asin
            });
            Object.defineProperty(Math, "nv_atan", {
                writable: false,
                value: Math.atan
            });
            Object.defineProperty(Math, "nv_atan2", {
                writable: false,
                value: Math.atan2
            });
            Object.defineProperty(Math, "nv_ceil", {
                writable: false,
                value: Math.ceil
            });
            Object.defineProperty(Math, "nv_cos", {
                writable: false,
                value: Math.cos
            });
            Object.defineProperty(Math, "nv_exp", {
                writable: false,
                value: Math.exp
            });
            Object.defineProperty(Math, "nv_floor", {
                writable: false,
                value: Math.floor
            });
            Object.defineProperty(Math, "nv_log", {
                writable: false,
                value: Math.log
            });
            Object.defineProperty(Math, "nv_max", {
                writable: false,
                value: Math.max
            });
            Object.defineProperty(Math, "nv_min", {
                writable: false,
                value: Math.min
            });
            Object.defineProperty(Math, "nv_pow", {
                writable: false,
                value: Math.pow
            });
            Object.defineProperty(Math, "nv_random", {
                writable: false,
                value: Math.random
            });
            Object.defineProperty(Math, "nv_round", {
                writable: false,
                value: Math.round
            });
            Object.defineProperty(Math, "nv_sin", {
                writable: false,
                value: Math.sin
            });
            Object.defineProperty(Math, "nv_sqrt", {
                writable: false,
                value: Math.sqrt
            });
            Object.defineProperty(Math, "nv_tan", {
                writable: false,
                value: Math.tan
            })
        };
        var R = function() {
            Object.defineProperty(Date.prototype, "nv_constructor", {
                writable: true,
                value: "Date"
            });
            Object.defineProperty(Date, "nv_parse", {
                writable: true,
                value: Date.parse
            });
            Object.defineProperty(Date, "nv_UTC", {
                writable: true,
                value: Date.UTC
            });
            Object.defineProperty(Date, "nv_now", {
                writable: true,
                value: Date.now
            });
            Object.defineProperty(Date.prototype, "nv_toString", {
                writable: true,
                value: Date.prototype.toString
            });
            Object.defineProperty(Date.prototype, "nv_toDateString", {
                writable: true,
                value: Date.prototype.toDateString
            });
            Object.defineProperty(Date.prototype, "nv_toTimeString", {
                writable: true,
                value: Date.prototype.toTimeString
            });
            Object.defineProperty(Date.prototype, "nv_toLocaleString", {
                writable: true,
                value: Date.prototype.toLocaleString
            });
            Object.defineProperty(Date.prototype, "nv_toLocaleDateString", {
                writable: true,
                value: Date.prototype.toLocaleDateString
            });
            Object.defineProperty(Date.prototype, "nv_toLocaleTimeString", {
                writable: true,
                value: Date.prototype.toLocaleTimeString
            });
            Object.defineProperty(Date.prototype, "nv_valueOf", {
                writable: true,
                value: Date.prototype.valueOf
            });
            Object.defineProperty(Date.prototype, "nv_getTime", {
                writable: true,
                value: Date.prototype.getTime
            });
            Object.defineProperty(Date.prototype, "nv_getFullYear", {
                writable: true,
                value: Date.prototype.getFullYear
            });
            Object.defineProperty(Date.prototype, "nv_getUTCFullYear", {
                writable: true,
                value: Date.prototype.getUTCFullYear
            });
            Object.defineProperty(Date.prototype, "nv_getMonth", {
                writable: true,
                value: Date.prototype.getMonth
            });
            Object.defineProperty(Date.prototype, "nv_getUTCMonth", {
                writable: true,
                value: Date.prototype.getUTCMonth
            });
            Object.defineProperty(Date.prototype, "nv_getDate", {
                writable: true,
                value: Date.prototype.getDate
            });
            Object.defineProperty(Date.prototype, "nv_getUTCDate", {
                writable: true,
                value: Date.prototype.getUTCDate
            });
            Object.defineProperty(Date.prototype, "nv_getDay", {
                writable: true,
                value: Date.prototype.getDay
            });
            Object.defineProperty(Date.prototype, "nv_getUTCDay", {
                writable: true,
                value: Date.prototype.getUTCDay
            });
            Object.defineProperty(Date.prototype, "nv_getHours", {
                writable: true,
                value: Date.prototype.getHours
            });
            Object.defineProperty(Date.prototype, "nv_getUTCHours", {
                writable: true,
                value: Date.prototype.getUTCHours
            });
            Object.defineProperty(Date.prototype, "nv_getMinutes", {
                writable: true,
                value: Date.prototype.getMinutes
            });
            Object.defineProperty(Date.prototype, "nv_getUTCMinutes", {
                writable: true,
                value: Date.prototype.getUTCMinutes
            });
            Object.defineProperty(Date.prototype, "nv_getSeconds", {
                writable: true,
                value: Date.prototype.getSeconds
            });
            Object.defineProperty(Date.prototype, "nv_getUTCSeconds", {
                writable: true,
                value: Date.prototype.getUTCSeconds
            });
            Object.defineProperty(Date.prototype, "nv_getMilliseconds", {
                writable: true,
                value: Date.prototype.getMilliseconds
            });
            Object.defineProperty(Date.prototype, "nv_getUTCMilliseconds", {
                writable: true,
                value: Date.prototype.getUTCMilliseconds
            });
            Object.defineProperty(Date.prototype, "nv_getTimezoneOffset", {
                writable: true,
                value: Date.prototype.getTimezoneOffset
            });
            Object.defineProperty(Date.prototype, "nv_setTime", {
                writable: true,
                value: Date.prototype.setTime
            });
            Object.defineProperty(Date.prototype, "nv_setMilliseconds", {
                writable: true,
                value: Date.prototype.setMilliseconds
            });
            Object.defineProperty(Date.prototype, "nv_setUTCMilliseconds", {
                writable: true,
                value: Date.prototype.setUTCMilliseconds
            });
            Object.defineProperty(Date.prototype, "nv_setSeconds", {
                writable: true,
                value: Date.prototype.setSeconds
            });
            Object.defineProperty(Date.prototype, "nv_setUTCSeconds", {
                writable: true,
                value: Date.prototype.setUTCSeconds
            });
            Object.defineProperty(Date.prototype, "nv_setMinutes", {
                writable: true,
                value: Date.prototype.setMinutes
            });
            Object.defineProperty(Date.prototype, "nv_setUTCMinutes", {
                writable: true,
                value: Date.prototype.setUTCMinutes
            });
            Object.defineProperty(Date.prototype, "nv_setHours", {
                writable: true,
                value: Date.prototype.setHours
            });
            Object.defineProperty(Date.prototype, "nv_setUTCHours", {
                writable: true,
                value: Date.prototype.setUTCHours
            });
            Object.defineProperty(Date.prototype, "nv_setDate", {
                writable: true,
                value: Date.prototype.setDate
            });
            Object.defineProperty(Date.prototype, "nv_setUTCDate", {
                writable: true,
                value: Date.prototype.setUTCDate
            });
            Object.defineProperty(Date.prototype, "nv_setMonth", {
                writable: true,
                value: Date.prototype.setMonth
            });
            Object.defineProperty(Date.prototype, "nv_setUTCMonth", {
                writable: true,
                value: Date.prototype.setUTCMonth
            });
            Object.defineProperty(Date.prototype, "nv_setFullYear", {
                writable: true,
                value: Date.prototype.setFullYear
            });
            Object.defineProperty(Date.prototype, "nv_setUTCFullYear", {
                writable: true,
                value: Date.prototype.setUTCFullYear
            });
            Object.defineProperty(Date.prototype, "nv_toUTCString", {
                writable: true,
                value: Date.prototype.toUTCString
            });
            Object.defineProperty(Date.prototype, "nv_toISOString", {
                writable: true,
                value: Date.prototype.toISOString
            });
            Object.defineProperty(Date.prototype, "nv_toJSON", {
                writable: true,
                value: Date.prototype.toJSON
            })
        };
        var F = function() {
            Object.defineProperty(RegExp.prototype, "nv_constructor", {
                writable: true,
                value: "RegExp"
            });
            Object.defineProperty(RegExp.prototype, "nv_exec", {
                writable: true,
                value: RegExp.prototype.exec
            });
            Object.defineProperty(RegExp.prototype, "nv_test", {
                writable: true,
                value: RegExp.prototype.test
            });
            Object.defineProperty(RegExp.prototype, "nv_toString", {
                writable: true,
                value: RegExp.prototype.toString
            });
            Object.defineProperty(RegExp.prototype, "nv_source", {get: function() {
                    return this.source
                },
                set: function() {}
            });
            Object.defineProperty(RegExp.prototype, "nv_global", {get: function() {
                    return this.global
                },
                set: function() {}
            });
            Object.defineProperty(RegExp.prototype, "nv_ignoreCase", {get: function() {
                    return this.ignoreCase
                },
                set: function() {}
            });
            Object.defineProperty(RegExp.prototype, "nv_multiline", {get: function() {
                    return this.multiline
                },
                set: function() {}
            });
            Object.defineProperty(RegExp.prototype, "nv_lastIndex", {get: function() {
                    return this.lastIndex
                },
                set: function(e) {
                    this.lastIndex = e
                }
            })
        };
        m();
        var J = function() {
            var e = Array.prototype.slice.call(arguments);
            e.unshift(Date);
            return new(Function.prototype.bind.apply(Date, e))
        };
        var B = function() {
            var e = Array.prototype.slice.call(arguments);
            e.unshift(RegExp);
            return new(Function.prototype.bind.apply(RegExp, e))
        };
        var Y = {};
        Y.nv_log = function() {
            var e = "WXSRT:";
            for (var t = 0; t < arguments.length; ++t) e += arguments[t] + " ";
            console.log(e)
        };
        var G = parseInt,
            X = parseFloat,
            H = isNaN,
            V = isFinite,
            $ = decodeURI,
            W = decodeURIComponent,
            Q = encodeURI,
            q = encodeURIComponent;

        function K(e, t, r) {
            e = A.rv(e);
            if (e === null || e === undefined) return e;
            if (typeof e === "string" || typeof e === "boolean" || typeof e === "number") return e;
            if (e.constructor === Object) {
                var n = {};
                for (var o in e)
                    if (Object.prototype.hasOwnProperty.call(e, o))
                        if (undefined === t) n[o.substring(3)] = K(e[o], t, r);
                        else n[t + o] = K(e[o], t, r);
                return n
            }
            if (e.constructor === Array) {
                var n = [];
                for (var a = 0; a < e.length; a++) n.push(K(e[a], t, r));
                return n
            }
            if (e.constructor === Date) {
                var n = new Date;
                n.setTime(e.getTime());
                return n
            }
            if (e.constructor === RegExp) {
                var i = "";
                if (e.global) i += "g";
                if (e.ignoreCase) i += "i";
                if (e.multiline) i += "m";
                return new RegExp(e.source, i)
            }
            if (r && typeof e === "function") {
                if (r == 1) return K(e(), undefined, 2);
                if (r == 2) return e
            }
            return null
        }
        var Z = {};
        Z.nv_stringify = function(e) {
            JSON.stringify(e);
            return JSON.stringify(K(e))
        };
        Z.nv_parse = function(e) {
            if (e === undefined) return undefined;
            var t = JSON.parse(e);
            return K(t, "nv_")
        };

        function ee(e, t, r, n) {
            e.extraAttr = {
                t_action: t,
                t_rawid: r
            };
            if (typeof n != "undefined") e.extraAttr.t_cid = n
        }

        function te() {
            if (typeof __globalThis.__webview_engine_version__ == "undefined") return 0;
            return __globalThis.__webview_engine_version__
        }

        function re(e, t, r, n, o, a) {
            var i = ne(t, r, n);
            if (i) e.push(i);
            else {
                e.push("");
                u(n + ":import:" + o + ":" + a + ": Path `" + t + "` not found from `" + n + "`.")
            }
        }

        function ne(e, t, r) {
            if (e[0] != "/") {
                var n = r.split("/");
                n.pop();
                var o = e.split("/");
                for (var a = 0; a < o.length; a++) {
                    if (o[a] == "..") n.pop();
                    else if (!o[a] || o[a] == ".") continue;
                    else n.push(o[a])
                }
                e = n.join("/")
            }
            if (r[0] == "." && e[0] == "/") e = "." + e;
            if (t[e]) return e;
            if (t[e + ".wxml"]) return e + ".wxml"
        }

        function oe(e, t, r, n) {
            if (!t) return;
            if (n[e][t]) return n[e][t];
            for (var o = r[e].i.length - 1; o >= 0; o--) {
                if (r[e].i[o] && n[r[e].i[o]][t]) return n[r[e].i[o]][t]
            }
            for (var o = r[e].ti.length - 1; o >= 0; o--) {
                var a = ne(r[e].ti[o], r, e);
                if (a && n[a][t]) return n[a][t]
            }
            var i = ae(r, e);
            for (var o = 0; o < i.length; o++) {
                if (i[o] && n[i[o]][t]) return n[i[o]][t]
            }
            for (var p = r[e].j.length - 1; p >= 0; p--)
                if (r[e].j[p]) {
                    for (var a = r[r[e].j[p]].ti.length - 1; a >= 0; a--) {
                        var u = ne(r[r[e].j[p]].ti[a], r, e);
                        if (u && n[u][t]) {
                            return n[u][t]
                        }
                    }
                }
        }

        function ae(e, t) {
            if (!t) return [];
            if ($gaic[t]) {
                return $gaic[t]
            }
            var r = [],
                n = [],
                o = 0,
                a = 0,
                i = {},
                p = {};
            n.push(t);
            p[t] = true;
            a++;
            while (o < a) {
                var u = n[o++];
                for (var l = 0; l < e[u].ic.length; l++) {
                    var f = e[u].ic[l];
                    var v = ne(f, e, u);
                    if (v && !p[v]) {
                        p[v] = true;
                        n.push(v);
                        a++
                    }
                }
                for (var l = 0; u != t && l < e[u].ti.length; l++) {
                    var c = e[u].ti[l];
                    var s = ne(c, e, u);
                    if (s && !i[s]) {
                        i[s] = true;
                        r.push(s)
                    }
                }
            }
            $gaic[t] = r;
            return r
        }
        var ie = {};

        function pe(e, t, r, n, o, a, i) {
            var p = ne(e, t, r);
            t[r].j.push(p);
            if (p) {
                if (ie[p]) {
                    u("-1:include:-1:-1: `" + e + "` is being included in a loop, will be stop.");
                    return
                }
                ie[p] = true;
                try {
                    t[p].f(n, o, a, i)
                } catch (n) {}
                ie[p] = false
            } else {
                u(r + ":include:-1:-1: Included path `" + e + "` not found from `" + r + "`.")
            }
        }

        function ue(e, t, r, n) {
            u(t + ":template:" + r + ":" + n + ": Template `" + e + "` not found.")
        }

        function le(e) {
            var t = false;
            delete e.properities;
            delete e.n;
            if (e.children) {
                do {
                    t = false;
                    var r = [];
                    for (var n = 0; n < e.children.length; n++) {
                        var o = e.children[n];
                        if (o.tag == "virtual") {
                            t = true;
                            for (var a = 0; o.children && a < o.children.length; a++) {
                                r.push(o.children[a])
                            }
                        } else {
                            r.push(o)
                        }
                    }
                    e.children = r
                } while (t);
                for (var n = 0; n < e.children.length; n++) {
                    le(e.children[n])
                }
            }
            return e
        }

        function fe(e) {
            if (e.tag == "wx-wx-scope") {
                e.tag = "virtual";
                e.wxCkey = "11";
                e["wxScopeData"] = e.attr["wx:scope-data"];
                delete e.n;
                delete e.raw;
                delete e.generics;
                delete e.attr
            }
            for (var t = 0; e.children && t < e.children.length; t++) {
                fe(e.children[t])
            }
            return e
        }
        return {
            a: D,
            b: S,
            c: v,
            d: e,
            e: t,
            f: u,
            g: r,
            h: s,
            i: n,
            j: o,
            k: A,
            l: T,
            m: a,
            n: f,
            o: c,
            p: i,
            q: y,
            r: N,
            s: b,
            t: d,
            u: h,
            v: p,
            w: l,
            x: _,
            y: w,
            z: O,
            A: j,
            B: P,
            C: M,
            D: J,
            E: B,
            F: Y,
            G: G,
            H: X,
            I: H,
            J: V,
            K: $,
            L: W,
            M: Q,
            N: q,
            O: K,
            P: Z,
            Q: ee,
            R: te,
            S: re,
            T: ne,
            U: oe,
            V: ae,
            W: ie,
            X: pe,
            Y: ue,
            Z: le,
            aa: fe
        }
    }()
});
Object.freeze(__g);
g = "";
__wxAppCode__['app.json'] = {
    "pages": ["pages/index", "pages/landing-page", "pages/buy/index", "pages/redirect", "pages/forbidden", "pages/test", "pages/webview/index", "pages/wepage/index"],
    "subPackages": [{
        "root": "pages/cast/",
        "pages": ["audio", "video", "index", "list", "room-list", "plugin", "video-nvue"]
    }, {
        "root": "pages/course/",
        "pages": ["index", "doc", "audio", "video", "comment/list", "comment/add", "list", "file", "record"]
    }, {
        "root": "pages/class/",
        "pages": ["home", "list", "index"]
    }, {
        "root": "pages/article/",
        "pages": ["list", "index"]
    }, {
        "root": "pages/question/",
        "pages": ["list", "battle", "index", "entry"]
    }, {
        "root": "pages/qbank/",
        "pages": ["list", "chapter", "notes", "history", "index", "dayPractice/list", "dayPractice/result", "detail", "child", "rank"]
    }, {
        "root": "pages/paper/",
        "pages": ["list", "record", "rank"]
    }, {
        "root": "pages/cate/",
        "pages": ["list"]
    }, {
        "root": "pages/lesson/",
        "pages": ["list", "index"]
    }, {
        "root": "pages/teacher/",
        "pages": ["index"]
    }, {
        "root": "pages/camp/",
        "pages": ["list", "detail", "calendar", "menu/rank-list", "menu/add-assistant", "question", "evaluate/list", "evaluate/add", "question-list"]
    }, {
        "root": "pages/user/",
        "pages": ["login", "auth", "register", "info", "bind-account", "order", "vip", "vip/goods", "vip/coupon", "course", "lesson", "qbank", "class", "camp", "point/index", "point/task", "point/tasks", "point/record", "point/rule", "point/shop", "point/lottery", "purse/purse", "purse/bill", "setting", "advance-setting", "account-setting", "kefu", "about", "privacy", "password", "password/edit", "phone", "coupon/list", "coupon/list2", "coupon/invalid-list", "cash/index", "cash/record", "cash/account", "profit/record", "address/list", "address/edit", "order/index", "order/afterSale", "order/payment", "login-confirm", "relation", "deactive", "filepacket", "prize", "store/info", "manage/goods"]
    }, {
        "root": "pages/share/",
        "pages": ["list", "card", "piiic", "register", "app"]
    }, {
        "root": "pages/reseller/",
        "pages": ["index", "apply", "branch", "customer", "customer-detail", "live-list", "goods-list", "wepage-list", "coupon-list", "order-list", "card", "store/writeoff", "store/writeoff-goods", "store/writeoff-record", "store/writeoff-stat", "store/writeoff-user", "store/live", "store/live-detail", "store/order"]
    }, {
        "root": "pages/plugin/",
        "pages": ["agreement/index", "agreement/list", "agreement/doc", "info/collection", "wappay/index"],
        "plugins": {
            "qyssdk-plugin": {
                "version": "1.2.21",
                "provider": "wxaf8f505686ddf66a"
            }
        }
    }, {
        "root": "pages/system/",
        "pages": ["version", "message", "feedback", "feedback-list", "canvas", "search", "goods_member", "mpwelink", "search-list"]
    }, {
        "root": "pages/sale/",
        "pages": ["coupon", "coupon/list", "key/use", "tuan", "joinByGroup", "joinByKey", "tuan/list", "discount/list", "discount/index", "cart", "haggle/list", "haggle/index", "haggle/assist", "seckill/list"]
    }, {
        "root": "pages/filepacket/",
        "pages": ["index", "pointsExchange", "pdfViewer", "joinBuy"]
    }, {
        "root": "pages/shop/",
        "pages": ["index", "product/list", "product/index", "prescription/index", "prescription/patient", "prescription/agreement", "evaluate/list", "evaluate/add"]
    }, {
        "root": "pages/community/",
        "pages": ["index", "group/index", "group/create", "topic/index", "topic/list", "post/index", "post/list", "post/create", "user/center"]
    }, {
        "root": "pages/word/",
        "pages": ["index", "plan/edit", "plan/list", "plan/report", "plan/share", "book", "wrong", "rank", "study", "test/test", "test/report", "test/index", "test/record", "fix", "search"]
    }, {
        "root": "pages/knowledge/",
        "pages": ["index"]
    }],
    "window": {
        "navigationBarTextStyle": "white",
        "navigationStyle": "custom",
        "navigationBarTitleText": "羽仪软考"
    },
    "networkTimeout": {
        "request": 30000
    },
    "resizable": true,
    "lazyCodeLoading": "requiredComponents",
    "usingShopPlugin": true,
    "plugins": {
        "xk-user-info": {
            "version": "0.0.1",
            "provider": "wx0c7b3a8857aa0f2a"
        }
    },
    "requiredBackgroundModes": ["audio"],
    "requiredPrivateInfos": ["chooseAddress"],
    "usingComponents": {
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-login": "/components/common/login",
        "xk-list-title": "/components/common/list-title",
        "mp-privacy": "/components/mp/privacy"
    },
    "__warning__": "无效的 app.json [\"usingShopPlugin\"]",
    "extEnable": true,
    "extAppid": "wx1d883ff20dfcf33b",
    "directCommit": false,
    "ext": {
        "host": "https://api.ixunke.cn/yuyiruankao",
        "httpDNSServiceId": ""
    },
    "extAppid": "wx6b199b9678c5d7cf"
};
__wxAppCode__['components/base/content.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/base/nav-bar.json'] = {
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    },
    "component": true
};
__wxAppCode__['components/base/tab-bar.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/cast/list/a.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/cast/list/b.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/cast/list/d.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/cast/list/e.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/cast/live-room-item.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/action-menu.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/action-sheet.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/add-group.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/buy.json'] = {
    "component": true,
    "usingComponents": {
        "payment": "/components/payment/payment",
        "xk-modal-pro": "/components/common/modal-pro",
        "xk-action-sheet": "/components/common/action-sheet",
        "add-group": "/components/common/add-group",
        "gift-list": "/components/common/gift-list",
        "skus-new": "/components/common/skus-new",
        "page-modal": "/components/common/page-modal",
        "collection-form": "/components/common/collection-form",
        "vip-thumb": "/components/common/product-img-vip",
        "number-box": "/components/common/number-box",
        "xk-prompt": "/components/common/prompt",
        "open-modal": "/components/common/modal-open",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/collection-form.json'] = {
    "component": true,
    "usingComponents": {
        "x-select": "/components/x-select/x-select",
        "modal-captcha": "/components/common/modal-captcha",
        "phone-area": "/components/common/phone-area",
        "html-parse": "/components/htmlParse/index",
        "image-upload": "/components/common/image-upload",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/countdown.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/coupon-item.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/custom-number.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/empty-inline.json'] = {
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    },
    "component": true
};
__wxAppCode__['components/common/empty.json'] = {
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    },
    "component": true
};
__wxAppCode__['components/common/express-modal.json'] = {
    "component": true,
    "usingComponents": {
        "timeaxis": "/components/common/timeaxis",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/gift-list.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/image-upload.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/kefu.json'] = {
    "component": true,
    "usingComponents": {
        "kefu-modal": "/components/common/kefuModal",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/kefuModal.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/keyboard.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/lesson-course-list.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/list-title.json'] = {
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    },
    "component": true
};
__wxAppCode__['components/common/load-more.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/loader.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/login.json'] = {
    "component": true,
    "usingComponents": {
        "phone-area": "/components/common/phone-area",
        "xk-login": "/components/common/login",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-user-info": "plugin://wx0c7b3a8857aa0f2a/user-info"
    }
};
__wxAppCode__['components/common/modal-captcha.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/modal-custom.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/modal-open.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/modal-pro.json'] = {
    "component": true,
    "usingComponents": {
        "collection-form": "/components/common/collection-form",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/modal-video.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/my-radio-group.json'] = {
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    },
    "component": true
};
__wxAppCode__['components/common/number-box.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/page-modal.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/pay-gift-modal.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/phone-area.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/pie-chart.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/pintuan.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/powered-by.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/price-cut.json'] = {
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    },
    "component": true
};
__wxAppCode__['components/common/product-img-vip.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/prompt.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/pulldown-refresh.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/qrcode.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/radio.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/seckill.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/skus-new.json'] = {
    "component": true,
    "usingComponents": {
        "number-box": "/components/common/number-box",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/skus.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/time-discount-new.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/time-discount.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/timeaxis.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/uni-star.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/userInfoBtn.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/common/watermark.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/community/edit-button.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/community/group-list.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/community/post-list.json'] = {
    "component": true,
    "usingComponents": {
        "modal-video": "/components/common/modal-video",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/course/camp-list.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/course/swiper.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/course/video-track.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/filepacket/add-group.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/filepacket/category-picker.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/filepacket/email-modal.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/h5/open-weapp.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/htmlParse/audio.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/htmlParse/index.json'] = {
    "component": true,
    "usingComponents": {
        "html-parser": "/components/htmlParse/parser",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/htmlParse/parser.json'] = {
    "component": true,
    "usingComponents": {
        "trees": "/components/htmlParse/trees",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/htmlParse/trees.json'] = {
    "component": true,
    "usingComponents": {
        "trees": "/components/htmlParse/trees",
        "xk-audio": "/components/htmlParse/audio",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/image/preview.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/mp/add.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/mp/privacy.json'] = {
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    },
    "component": true
};
__wxAppCode__['components/nav/camp.json'] = {
    "component": true,
    "usingComponents": {
        "xk-load-more": "/components/common/load-more",
        "node-list": "/pages/wepage/node-list",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/nav/class.json'] = {
    "component": true,
    "usingComponents": {
        "xk-load-more": "/components/common/load-more",
        "xk-pulldown-refresh": "/components/common/pulldown-refresh",
        "node-list": "/pages/wepage/node-list",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/nav/community.json'] = {
    "component": true,
    "usingComponents": {
        "post-list": "/components/community/post-list",
        "group-list": "/components/community/group-list",
        "edit-button": "/components/community/edit-button",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/nav/course.json'] = {
    "component": true,
    "usingComponents": {
        "xk-pulldown-refresh": "/components/common/pulldown-refresh",
        "powered-by": "/components/common/powered-by",
        "node-list": "/pages/wepage/node-list",
        "community": "/components/nav/community",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/nav/filepacket.json'] = {
    "component": true,
    "usingComponents": {
        "add-group": "/components/filepacket/add-group",
        "xk-load-more": "/components/common/load-more",
        "category-picker": "/components/filepacket/category-picker",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/nav/find.json'] = {
    "component": true,
    "usingComponents": {
        "xk-pulldown-refresh": "/components/common/pulldown-refresh",
        "xk-load-more": "/components/common/load-more",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/nav/qbank.json'] = {
    "component": true,
    "usingComponents": {
        "payment": "/components/payment/payment",
        "xk-qbank-icon": "/components/qbank/icon",
        "xk-qbank-chapter": "/components/qbank/chapter",
        "xk-qbank-paper": "/components/qbank/paper",
        "xk-qbank-day-statistics": "/components/qbank/dayStatistics",
        "xk-qbank-day-practice": "/components/qbank/dayPractice",
        "children-qbank": "/components/qbank/childrenQbank",
        "xk-interstitial": "/pages/wepage/components/interstitial",
        "xk-swiper": "/pages/wepage/components/swiper",
        "xk-swiper-old": "/components/course/swiper",
        "xk-image-pro": "/pages/wepage/components/image-pro",
        "xk-rich-text": "/pages/wepage/components/rich-text",
        "xk-scroll-message": "/pages/wepage/components/scroll-message",
        "xk-dividing-line": "/pages/wepage/components/dividing-line",
        "xk-sale-entry": "/pages/wepage/components/sale-entry",
        "xk-vip-entry": "/pages/wepage/components/vip-entry",
        "xk-coupon-list": "/pages/wepage/components/coupon-list",
        "xk-shop-flag": "/pages/wepage/components/shop-flag",
        "xk-official-account": "/pages/wepage/components/official-account",
        "xk-workwechat-kefu": "/pages/wepage/components/workwechat-kefu",
        "xk-mp-ad": "/pages/wepage/components/mp-ad",
        "xk-search": "/pages/wepage/components/search",
        "xk-countdown": "/pages/wepage/components/countdown",
        "arc-bar": "/components/wx-charts/arcbar",
        "powered-by": "/components/common/powered-by",
        "xk-modal-pro": "/components/common/modal-pro",
        "open-modal": "/components/common/modal-open",
        "xk-action-sheet": "/components/common/action-sheet",
        "pintuan": "/components/common/pintuan",
        "time-discount": "/components/common/time-discount",
        "price-cut": "/components/common/price-cut",
        "seckill": "/components/common/seckill",
        "add-group": "/components/common/add-group",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/nav/shop.json'] = {
    "component": true,
    "usingComponents": {
        "node-list": "/pages/wepage/node-list",
        "powered-by": "/components/common/powered-by",
        "xk-cate-shop": "/pages/wepage/components/cate-shop",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/nav/user.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/nav/word.json'] = {
    "component": true,
    "usingComponents": {
        "xk-modal-pro": "/components/common/modal-pro",
        "custom-number": "/components/common/custom-number",
        "open-modal": "/components/common/modal-open",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/payment/payment.json'] = {
    "component": true,
    "usingComponents": {
        "xk-keyboard": "/components/common/keyboard",
        "add-group": "/components/common/add-group",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/qbank/chapter.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/qbank/childrenQbank.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/qbank/dayPractice.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/qbank/dayStatistics.json'] = {
    "component": true,
    "usingComponents": {
        "arc-bar": "/components/wx-charts/arcbar",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/qbank/icon.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/qbank/paper-item.json'] = {
    "component": true,
    "usingComponents": {
        "open-modal": "/components/common/modal-open",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/qbank/paper-modal.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/qbank/paper.json'] = {
    "component": true,
    "usingComponents": {
        "paper-item": "/components/qbank/paper-item",
        "paper-modal": "/components/qbank/paper-modal",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/share/h5-tip-share.json'] = {
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    },
    "component": true
};
__wxAppCode__['components/share/share.json'] = {
    "component": true,
    "usingComponents": {
        "h5-tip-share": "/components/share/h5-tip-share",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/wx-charts/arcbar.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['components/x-select/x-select.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/buy/index.json'] = {
    "navigationBarTextStyle": "black",
    "usingComponents": {
        "xk-buy": "/components/common/buy",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/forbidden.json'] = {
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/index.json'] = {
    "disableScroll": true,
    "navigationBarTextStyle": "black",
    "usingComponents": {
        "tab-bar": "/components/base/tab-bar",
        "fixed-content": "/components/base/content",
        "kefu": "/components/common/kefu",
        "action-menu": "/components/common/action-menu",
        "xk-share": "/components/share/share",
        "add-mp": "/components/mp/add",
        "tab-course": "/components/nav/course",
        "tab-qbank": "/components/nav/qbank",
        "tab-class": "/components/nav/class",
        "tab-camp": "/components/nav/camp",
        "tab-filepacket": "/components/nav/filepacket",
        "tab-word": "/components/nav/word",
        "tab-article": "/components/nav/find",
        "tab-community": "/components/nav/community",
        "tab-shop": "/components/nav/shop",
        "tab-user": "/components/nav/user",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/landing-page.json'] = {
    "usingComponents": {
        "xk-load-more": "/components/common/load-more",
        "payment": "/components/payment/payment",
        "xk-share": "/components/share/share",
        "xk-modal-pro": "/components/common/modal-pro",
        "add-group": "/components/common/add-group",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/redirect.json'] = {
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/test.json'] = {
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/webview/index.json'] = {
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/article-list.json'] = {
    "component": true,
    "usingComponents": {
        "xk-list-title": "/pages/wepage/components/list-title",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/camp-list.json'] = {
    "component": true,
    "usingComponents": {
        "xk-list-title": "/pages/wepage/components/list-title",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/cate-course-list.json'] = {
    "component": true,
    "usingComponents": {
        "xk-course-list": "/pages/wepage/components/course-list",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/cate-live-room-list.json'] = {
    "component": true,
    "usingComponents": {
        "xk-list-title": "/pages/wepage/components/list-title",
        "xk-room-list": "/pages/wepage/components/live-room-list",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/cate-qbank-list.json'] = {
    "component": true,
    "usingComponents": {
        "xk-qbank-list": "/pages/wepage/components/qbank-list",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/cate-shop.json'] = {
    "component": true,
    "usingComponents": {
        "xk-load-more": "/components/common/load-more",
        "skus-new": "/components/common/skus-new",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/class-list.json'] = {
    "component": true,
    "usingComponents": {
        "xk-list-large": "/pages/wepage/components/class-list/a",
        "xk-list-middle": "/pages/wepage/components/class-list/b",
        "xk-list-small": "/pages/wepage/components/class-list/c",
        "xk-list-style-e": "/pages/wepage/components/class-list/e",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/class-list/a.json'] = {
    "component": true,
    "usingComponents": {
        "xk-list-title": "/pages/wepage/components/list-title",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/class-list/b.json'] = {
    "component": true,
    "usingComponents": {
        "xk-list-title": "/pages/wepage/components/list-title",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/class-list/c.json'] = {
    "component": true,
    "usingComponents": {
        "xk-list-title": "/pages/wepage/components/list-title",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/class-list/e.json'] = {
    "component": true,
    "usingComponents": {
        "xk-list-title": "/pages/wepage/components/list-title",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/countdown.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/coupon-list.json'] = {
    "component": true,
    "usingComponents": {
        "xk-list-title": "/pages/wepage/components/list-title",
        "coupon-item": "/components/common/coupon-item",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/course-list.json'] = {
    "component": true,
    "usingComponents": {
        "xk-list-large": "/pages/wepage/components/list/a",
        "xk-list-middle": "/pages/wepage/components/list/b",
        "xk-list-small": "/pages/wepage/components/list/c",
        "xk-list-no-image": "/pages/wepage/components/list/d",
        "xk-list-style-e": "/pages/wepage/components/list/e",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/dividing-line.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/filepacket-list.json'] = {
    "component": true,
    "usingComponents": {
        "xk-list-title": "/pages/wepage/components/list-title",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/goods-list.json'] = {
    "component": true,
    "usingComponents": {
        "xk-list-title": "/pages/wepage/components/list-title",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/group-list.json'] = {
    "component": true,
    "usingComponents": {
        "xk-list-title": "/pages/wepage/components/list-title",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/icon.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/image-pro.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/interstitial.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/lesson-list.json'] = {
    "component": true,
    "usingComponents": {
        "xk-list-large": "/pages/wepage/components/list/a",
        "xk-list-middle": "/pages/wepage/components/list/b",
        "xk-list-small": "/pages/wepage/components/list/c",
        "xk-list-no-image": "/pages/wepage/components/list/d",
        "xk-list-style-e": "/pages/wepage/components/list/e",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/list-title.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/list/a.json'] = {
    "component": true,
    "usingComponents": {
        "xk-list-title": "/pages/wepage/components/list-title",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/list/b.json'] = {
    "component": true,
    "usingComponents": {
        "xk-list-title": "/pages/wepage/components/list-title",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/list/c.json'] = {
    "component": true,
    "usingComponents": {
        "xk-list-title": "/pages/wepage/components/list-title",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/list/d.json'] = {
    "component": true,
    "usingComponents": {
        "xk-list-title": "/pages/wepage/components/list-title",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/list/e.json'] = {
    "component": true,
    "usingComponents": {
        "xk-list-title": "/pages/wepage/components/list-title",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/live-room-list.json'] = {
    "component": true,
    "usingComponents": {
        "xk-list-title": "/pages/wepage/components/list-title",
        "live-room-item": "/components/cast/live-room-item",
        "xk-list-large": "/components/cast/list/a",
        "xk-list-middle": "/components/cast/list/b",
        "xk-list-no-image": "/components/cast/list/d",
        "xk-list-style-e": "/components/cast/list/e",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/mp-ad.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/official-account.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/qbank-list.json'] = {
    "component": true,
    "usingComponents": {
        "xk-list-large": "/pages/wepage/components/list/a",
        "xk-list-middle": "/pages/wepage/components/list/b",
        "xk-list-small": "/pages/wepage/components/list/c",
        "xk-list-no-image": "/pages/wepage/components/list/d",
        "xk-list-style-e": "/pages/wepage/components/list/e",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/rich-text.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/sale-entry.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/scroll-message.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/search.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/shop-flag.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/swiper.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/video.json'] = {
    "component": true,
    "usingComponents": {
        "modal-video": "/components/common/modal-video",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/vip-entry.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/wepage-group.json'] = {
    "component": true,
    "usingComponents": {
        "node-list": "/pages/wepage/node-list-copy",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/components/workwechat-kefu.json'] = {
    "component": true,
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/index.json'] = {
    "usingComponents": {
        "xk-share": "/components/share/share",
        "node-list": "/pages/wepage/node-list",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/node-list-copy.json'] = {
    "component": true,
    "usingComponents": {
        "xk-interstitial": "/pages/wepage/components/interstitial",
        "xk-swiper": "/pages/wepage/components/swiper",
        "xk-swiper-old": "/components/course/swiper",
        "xk-icon": "/pages/wepage/components/icon",
        "xk-image-pro": "/pages/wepage/components/image-pro",
        "xk-video": "/pages/wepage/components/video",
        "xk-rich-text": "/pages/wepage/components/rich-text",
        "xk-search": "/pages/wepage/components/search",
        "xk-scroll-message": "/pages/wepage/components/scroll-message",
        "xk-dividing-line": "/pages/wepage/components/dividing-line",
        "xk-component-title": "/pages/wepage/components/list-title",
        "xk-course-list": "/pages/wepage/components/course-list",
        "xk-cate-course": "/pages/wepage/components/cate-course-list",
        "xk-lesson-list": "/pages/wepage/components/lesson-list",
        "xk-qbank-list": "/pages/wepage/components/qbank-list",
        "xk-cate-qbank": "/pages/wepage/components/cate-qbank-list",
        "xk-class-list": "/pages/wepage/components/class-list",
        "xk-live-room": "/pages/wepage/components/live-room-list",
        "xk-cate-live-room": "/pages/wepage/components/cate-live-room-list",
        "xk-camp-list": "/pages/wepage/components/camp-list",
        "xk-camp-list-old": "/components/course/camp-list",
        "xk-filepacket-list": "/pages/wepage/components/filepacket-list",
        "xk-sale-entry": "/pages/wepage/components/sale-entry",
        "xk-goods-list": "/pages/wepage/components/goods-list",
        "xk-article-list": "/pages/wepage/components/article-list",
        "xk-vip-entry": "/pages/wepage/components/vip-entry",
        "xk-coupon-list": "/pages/wepage/components/coupon-list",
        "xk-shop-flag": "/pages/wepage/components/shop-flag",
        "xk-official-account": "/pages/wepage/components/official-account",
        "xk-workwechat-kefu": "/pages/wepage/components/workwechat-kefu",
        "xk-mp-ad": "/pages/wepage/components/mp-ad",
        "xk-group-list": "/pages/wepage/components/group-list",
        "xk-wepage-group": "/pages/wepage/components/wepage-group",
        "xk-countdown": "/pages/wepage/components/countdown",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/wepage/node-list.json'] = {
    "component": true,
    "usingComponents": {
        "xk-interstitial": "/pages/wepage/components/interstitial",
        "xk-swiper": "/pages/wepage/components/swiper",
        "xk-swiper-old": "/components/course/swiper",
        "xk-icon": "/pages/wepage/components/icon",
        "xk-image-pro": "/pages/wepage/components/image-pro",
        "xk-video": "/pages/wepage/components/video",
        "xk-rich-text": "/pages/wepage/components/rich-text",
        "xk-search": "/pages/wepage/components/search",
        "xk-scroll-message": "/pages/wepage/components/scroll-message",
        "xk-dividing-line": "/pages/wepage/components/dividing-line",
        "xk-component-title": "/pages/wepage/components/list-title",
        "xk-course-list": "/pages/wepage/components/course-list",
        "xk-cate-course": "/pages/wepage/components/cate-course-list",
        "xk-lesson-list": "/pages/wepage/components/lesson-list",
        "xk-qbank-list": "/pages/wepage/components/qbank-list",
        "xk-cate-qbank": "/pages/wepage/components/cate-qbank-list",
        "xk-class-list": "/pages/wepage/components/class-list",
        "xk-live-room": "/pages/wepage/components/live-room-list",
        "xk-cate-live-room": "/pages/wepage/components/cate-live-room-list",
        "xk-camp-list": "/pages/wepage/components/camp-list",
        "xk-camp-list-old": "/components/course/camp-list",
        "xk-filepacket-list": "/pages/wepage/components/filepacket-list",
        "xk-sale-entry": "/pages/wepage/components/sale-entry",
        "xk-goods-list": "/pages/wepage/components/goods-list",
        "xk-article-list": "/pages/wepage/components/article-list",
        "xk-vip-entry": "/pages/wepage/components/vip-entry",
        "xk-coupon-list": "/pages/wepage/components/coupon-list",
        "xk-shop-flag": "/pages/wepage/components/shop-flag",
        "xk-official-account": "/pages/wepage/components/official-account",
        "xk-workwechat-kefu": "/pages/wepage/components/workwechat-kefu",
        "xk-mp-ad": "/pages/wepage/components/mp-ad",
        "xk-group-list": "/pages/wepage/components/group-list",
        "xk-wepage-group": "/pages/wepage/components/wepage-group",
        "xk-countdown": "/pages/wepage/components/countdown",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['project.config.json'] = {
    "miniprogramRoot": "",
    "__compileDebugInfo__": {
        "useSummer": true
    }
};
__wxAppCode__['project.private.config.json'] = {
    "description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html",
    "projectname": "%E8%BF%85%E8%AF%BE%E5%AD%A6%E5%A0%82%2B",
    "setting": {
        "compileHotReLoad": true,
        "urlCheck": false
    },
    "libVersion": "3.8.0",
    "condition": {
        "miniprogram": {
            "list": [{
                "name": "pages/user/login",
                "pathName": "pages/user/login",
                "query": "",
                "launchMode": "default",
                "scene": null
            }]
        }
    }
};;
var __WXML_DEP__ = __WXML_DEP__ || {};