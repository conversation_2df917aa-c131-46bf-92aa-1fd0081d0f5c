$gwx_XC_100 = function(_, _v, _n, _p, _s, _wp, _wl, $gwn, $gwl, $gwh, wh, $gstack, $gwrt, gra, grb, TestTest, wfor, _ca, _da, _r, _rz, _o, _oz, _1, _1z, _2, _2z, _m, _mz, nv_getDate, nv_getRegExp, nv_console, nv_parseInt, nv_parseFloat, nv_isNaN, nv_isFinite, nv_decodeURI, nv_decodeURIComponent, nv_encodeURI, nv_encodeURIComponent, $gdc, nv_JSON, _af, _gv, _ai, _grp, _gd, _gapi, $ixc, _ic, _w, _ev, _tsd) {
    return function(path, global) {
        if (typeof global === 'undefined') {
            if (typeof __GWX_GLOBAL__ === 'undefined') global = {};
            else global = __GWX_GLOBAL__;
        }
        if (typeof __WXML_GLOBAL__ === 'undefined') {
            __WXML_GLOBAL__ = {};
        }
        __WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
        var e_ = {}
        if (typeof(global.entrys) === 'undefined') global.entrys = {};
        e_ = global.entrys;
        var d_ = {}
        if (typeof(global.defines) === 'undefined') global.defines = {};
        d_ = global.defines;
        var f_ = {}
        if (typeof(global.modules) === 'undefined') global.modules = {};
        f_ = global.modules || {};
        var p_ = {}
        __WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
        __WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
        __WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
        var z = __WXML_GLOBAL__.ops_set.$gwx_XC_100 || [];

        function gz$gwx_XC_100_1() {
            if (__WXML_GLOBAL__.ops_cached.$gwx_XC_100_1) return __WXML_GLOBAL__.ops_cached.$gwx_XC_100_1
            __WXML_GLOBAL__.ops_cached.$gwx_XC_100_1 = [];
            (function(z) {
                var a = 11;

                function Z(ops) {
                    z.push(ops)
                }
                Z([3, 'data-v-849fa24a'])
                Z([3, 'no-support data-v-849fa24a'])
                Z([3, '当前页面仅支持微信浏览器打开'])
            })(__WXML_GLOBAL__.ops_cached.$gwx_XC_100_1);
            return __WXML_GLOBAL__.ops_cached.$gwx_XC_100_1
        }
        __WXML_GLOBAL__.ops_set.$gwx_XC_100 = z;
        __WXML_GLOBAL__.ops_init.$gwx_XC_100 = true;
        var x = ['./pages/landing-page.wxml'];
        d_[x[0]] = {}
        var m0 = function(e, s, r, gg) {
            var z = gz$gwx_XC_100_1()
            var tI4D = _n('view')
            _rz(z, tI4D, 'class', 0, e, s, gg)
            var eJ4D = _n('view')
            _rz(z, eJ4D, 'class', 1, e, s, gg)
            var bK4D = _oz(z, 2, e, s, gg)
            _(eJ4D, bK4D)
            _(tI4D, eJ4D)
            _(r, tI4D)
            return r
        }
        e_[x[0]] = {
            f: m0,
            j: [],
            i: [],
            ti: [],
            ic: []
        }
        if (path && e_[path]) {
            outerGlobal.__wxml_comp_version__ = 0.02
            return function(env, dd, global) {
                $gwxc = 0;
                var root = {
                    "tag": "wx-page"
                };
                root.children = [];
                g = "$gwx_XC_100";
                var main = e_[path].f
                if (typeof global === "undefined") global = {};
                global.f = $gdc(f_[path], "", 1);
                if (typeof(outerGlobal.__webview_engine_version__) != 'undefined' && outerGlobal.__webview_engine_version__ + 1e-6 >= 0.02 + 1e-6 && outerGlobal.__mergeData__) {
                    env = outerGlobal.__mergeData__(env, dd);
                }
                try {
                    main(env, {}, root, global);
                    _tsd(root)
                    if (typeof(outerGlobal.__webview_engine_version__) == 'undefined' || outerGlobal.__webview_engine_version__ + 1e-6 < 0.01 + 1e-6) {
                        return _ev(root);
                    }
                } catch (err) {
                    console.log(err)
                };
                g = "";
                return root;
            }
        }
    }
}(__g.a, __g.b, __g.c, __g.d, __g.e, __g.f, __g.g, __g.h, __g.i, __g.j, __g.k, __g.l, __g.m, __g.n, __g.o, __g.p, __g.q, __g.r, __g.s, __g.t, __g.u, __g.v, __g.w, __g.x, __g.y, __g.z, __g.A, __g.B, __g.C, __g.D, __g.E, __g.F, __g.G, __g.H, __g.I, __g.J, __g.K, __g.L, __g.M, __g.N, __g.O, __g.P, __g.Q, __g.R, __g.S, __g.T, __g.U, __g.V, __g.W, __g.X, __g.Y, __g.Z, __g.aa);
if (__vd_version_info__.delayedGwx || false) $gwx_XC_100();
if (__vd_version_info__.delayedGwx) __wxAppCode__['pages/landing-page.wxml'] = [$gwx_XC_100, './pages/landing-page.wxml'];
else __wxAppCode__['pages/landing-page.wxml'] = $gwx_XC_100('./pages/landing-page.wxml');

var noCss = typeof __vd_version_info__ !== 'undefined' && __vd_version_info__.noCss === true;
if (!noCss) {
    __wxAppCode__['pages/landing-page.wxss'] = setCssToHead([".", [1], "page-content{padding:", [0, 30], "}\n.", [1], "input-form{background-color:#fff;border-radius:", [0, 12], "}\n.", [1], "input-item{height:", [0, 46], ";padding:", [0, 30], ";position:relative}\n.", [1], "input-item .", [1], "iconfont{color:#c2c2c2;font-size:", [0, 40], ";left:", [0, 30], "}\n.", [1], "input-item .", [1], "iconfont,.", [1], "input-item .", [1], "input-wrap{position:absolute;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}\n.", [1], "input-item .", [1], "input-wrap{left:", [0, 100], ";right:", [0, 200], "}\n.", [1], "input-item wx-input{font-size:14px;height:20px;line-height:20px;width:100%}\n.", [1], "input-item .", [1], "xk-placeholder{color:#999;font-size:13px;padding-top:3px}\n.", [1], "image-code{font-size:0;position:absolute;right:", [0, 20], ";top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}\n.", [1], "image-code wx-image{height:0;width:", [0, 160], "}\n.", [1], "input-code{color:#ccc;font-size:", [0, 24], ";padding:", [0, 6], " ", [0, 12], ";position:absolute;right:", [0, 20], ";top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}\n.", [1], "input-code.", [1], "light{background:var(--theme);border-radius:", [0, 100], ";color:#fff}\n.", [1], "input-code.", [1], "normal{color:#999}\n.", [1], "input-item-tip{color:#ccc;font-size:12px;margin-top:5px;padding-left:22px;-webkit-transform:scale(.85);transform:scale(.85);-webkit-transform-origin:0 center;transform-origin:0 center}\n.", [1], "btn-submit{-webkit-align-items:center;align-items:center;background:var(--theme);border-radius:", [0, 100], ";color:#fff;display:-webkit-flex;display:flex;font-size:", [0, 32], ";-webkit-justify-content:center;justify-content:center;margin-top:", [0, 80], ";padding:", [0, 20], ";text-align:center}\n.", [1], "btn-submit.", [1], "hover{opacity:.8}\n.", [1], "btn-submit.", [1], "loading{opacity:.9}\n.", [1], "btn-submit.", [1], "loading::before{-webkit-animation:circling .4s linear infinite;animation:circling .4s linear infinite;border:2px solid;border-radius:50%;border-right:2px solid transparent;content:\x22\x22;height:.7em;margin-right:5px;width:.7em}\n.", [1], "btn-submit.", [1], "less{color:#999}\n.", [1], "btn-submit.", [1], "less,.", [1], "btn-submit.", [1], "plain{background:none;font-size:", [0, 28], ";margin-top:", [0, 40], "}\n.", [1], "btn-submit.", [1], "plain{border:1px solid #07c160;color:#07c160;letter-spacing:.6px}\n.", [1], "btn-text{color:#333;font-size:", [0, 22], ";padding:", [0, 24], " 0}\n.", [1], "btn-text .", [1], "color-text{color:#09f}\n.", [1], "text-right{float:right}\n@media screen and (min-width:768px){.", [1], "page-content{padding:15px}\n.", [1], "input-form{border-radius:6px}\n.", [1], "input-item{height:23px;padding:15px}\n.", [1], "input-item .", [1], "iconfont{font-size:20px;left:15px}\n.", [1], "input-item .", [1], "input-wrap{left:50px;right:100px}\n.", [1], "image-code wx-image{width:80px}\n.", [1], "input-code{font-size:12px;padding:3px 6px;right:10px}\n.", [1], "btn-submit,.", [1], "input-code.", [1], "light{border-radius:50px}\n.", [1], "btn-submit{font-size:18px;margin-top:40px;padding:10px}\n.", [1], "btn-submit.", [1], "less{font-size:14px;margin-top:20px}\n.", [1], "btn-text{font-size:10px;padding:12px 0}\n}.", [1], "landing-page.", [1], "data-v-849fa24a{margin:0 auto;max-width:600px;padding-bottom:env(safe-area-inset-bottom)}\n.", [1], "landing-page .", [1], "rich-text.", [1], "data-v-849fa24a{color:#333;font-size:15px;line-height:1.7;text-align:justify}\n.", [1], "landing-page .", [1], "footer-box .", [1], "footer-button .", [1], "footer-button-img.", [1], "data-v-849fa24a{display:block;height:100%;width:100%}\n.", [1], "landing-page .", [1], "footer-box .", [1], "footer-button .", [1], "footer-button-text.", [1], "data-v-849fa24a{-webkit-align-items:center;align-items:center;background-color:#ecbf40;color:#111;display:-webkit-flex;display:flex;font-size:16px;font-weight:700;height:50px;-webkit-justify-content:center;justify-content:center;letter-spacing:1px;line-height:1.2;padding:0 9px}\n.", [1], "landing-page .", [1], "footer-box .", [1], "footer-button.", [1], "hover.", [1], "data-v-849fa24a{opacity:.95}\n.", [1], "landing-page .", [1], "footer-box.", [1], "fixedBottom.", [1], "data-v-849fa24a{height:80px}\n.", [1], "landing-page .", [1], "footer-box.", [1], "fixedBottom .", [1], "footer-button.", [1], "data-v-849fa24a{bottom:0;bottom:constant(safe-area-inset-bottom);bottom:env(safe-area-inset-bottom);max-width:600px;position:fixed;right:50%;-webkit-transform:translateX(50%);transform:translateX(50%);width:100%;z-index:20}\n.", [1], "landing-page .", [1], "footer-box.", [1], "sidebar .", [1], "footer-button.", [1], "data-v-849fa24a{border-radius:5px;bottom:100px;font-size:15px;overflow:hidden;position:fixed;right:15px;width:50px;z-index:20}\n.", [1], "landing-page .", [1], "footer-box.", [1], "sidebar .", [1], "footer-button .", [1], "footer-button-text.", [1], "data-v-849fa24a{font-size:15px}\n.", [1], "product-modal.", [1], "data-v-849fa24a{background-color:rgba(0,0,0,.4);bottom:0;left:0;position:fixed;right:0;top:0;z-index:888}\n.", [1], "product-modal .", [1], "modal-panel.", [1], "data-v-849fa24a{background-color:#f3f3f3;border-top-left-radius:12px;border-top-right-radius:12px;bottom:0;left:0;overflow:hidden;padding-bottom:env(safe-area-inset-bottom);position:absolute;right:0}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "modal-title.", [1], "data-v-849fa24a{background-color:#fff;border-bottom:1px solid #f3f3f3;font-size:14px;padding:10px 5px;text-align:center}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "modal-button.", [1], "data-v-849fa24a{background-color:#fff;box-shadow:0 0 10px 2px rgba(0,0,0,.1);display:-webkit-flex;display:flex;height:44px;position:relative;z-index:10}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "modal-button .", [1], "modal-button-item.", [1], "data-v-849fa24a{color:#333;-webkit-flex:1;flex:1;font-size:15px;line-height:44px;position:relative;text-align:center}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "modal-content.", [1], "data-v-849fa24a{position:relative;transition:-webkit-transform .3s;transition:transform .3s;transition:transform .3s,-webkit-transform .3s}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "modal-content.", [1], "move.", [1], "data-v-849fa24a{-webkit-transform:translateX(-250px);transform:translateX(-250px)}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-list-box.", [1], "data-v-849fa24a{max-height:400px;min-height:180px}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-list-box .", [1], "item.", [1], "data-v-849fa24a{-webkit-align-items:center;align-items:center;background-color:#fff;border-radius:6px;display:-webkit-flex;display:flex;margin:10px;overflow:hidden;padding:10px;position:relative}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-list-box .", [1], "item .", [1], "image-wrap.", [1], "data-v-849fa24a{-webkit-flex-shrink:0;flex-shrink:0;height:60px;position:relative;width:107px}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-list-box .", [1], "item .", [1], "image-wrap .", [1], "thumb.", [1], "data-v-849fa24a{background-color:#ddd;border-radius:6px;height:100%;width:100%}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-list-box .", [1], "item .", [1], "image-wrap .", [1], "product-tag.", [1], "data-v-849fa24a{bottom:0;left:0;padding:5px;position:absolute;right:0;top:0}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-list-box .", [1], "item .", [1], "image-wrap .", [1], "product-tag-item.", [1], "data-v-849fa24a{background:#e54d42;border-radius:3px;color:#fff;display:inline-block;font-size:10px;font-weight:700;margin-bottom:5px;margin-right:5px;padding:2px 5px;vertical-align:top}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-list-box .", [1], "item .", [1], "image-wrap .", [1], "product-tag-item.", [1], "tuan.", [1], "data-v-849fa24a{background:linear-gradient(90deg,#ffde9d,#feb94a);color:#7d4e1b}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-list-box .", [1], "item .", [1], "image-wrap .", [1], "product-tag-item.", [1], "discount.", [1], "data-v-849fa24a{background:linear-gradient(90deg,#e54d42,#fe904a);color:#fff}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-list-box .", [1], "item .", [1], "main.", [1], "data-v-849fa24a{display:-webkit-flex;display:flex;-webkit-flex:1;flex:1;-webkit-flex-direction:column;flex-direction:column;-webkit-justify-content:space-between;justify-content:space-between;margin-left:10px;min-height:60px;width:0}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-list-box .", [1], "item .", [1], "main .", [1], "top.", [1], "data-v-849fa24a{vertical-align:middle}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-list-box .", [1], "item .", [1], "main .", [1], "top .", [1], "type.", [1], "data-v-849fa24a{-webkit-align-items:center;align-items:center;background:#ffe6e6;border-radius:4px;color:red;display:-webkit-inline-flex;display:inline-flex;font-size:12px;height:18px;-webkit-justify-content:center;justify-content:center;margin-right:5px;-webkit-transform:translateY(-1px);transform:translateY(-1px);width:34px}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-list-box .", [1], "item .", [1], "main .", [1], "top .", [1], "title.", [1], "data-v-849fa24a{color:#333;display:inline;font-size:14px;letter-spacing:.6px;line-height:20px}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-list-box .", [1], "item .", [1], "main .", [1], "price.", [1], "data-v-849fa24a{-webkit-align-items:flex-end;align-items:flex-end;color:#f01414;display:-webkit-flex;display:flex;margin-top:10px}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-list-box .", [1], "item .", [1], "main .", [1], "price .", [1], "unit.", [1], "data-v-849fa24a{font-size:12px}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-list-box .", [1], "item .", [1], "main .", [1], "price .", [1], "num.", [1], "data-v-849fa24a{font-size:18px;line-height:1;margin-left:2px}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-list-box .", [1], "item .", [1], "main .", [1], "price .", [1], "qi.", [1], "data-v-849fa24a{font-size:12px;margin-left:2px;-webkit-transform:scale(.8);transform:scale(.8)}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-list-box .", [1], "item .", [1], "main .", [1], "price .", [1], "marketPrice.", [1], "data-v-849fa24a{color:#999;display:inline;font-size:12px;margin-left:5px;text-decoration:line-through}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-list-box .", [1], "item .", [1], "badge.", [1], "data-v-849fa24a{bottom:2px;position:absolute;right:18px}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-list-box .", [1], "item .", [1], "badge .", [1], "iconfont.", [1], "data-v-849fa24a{color:#999;font-size:40px}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-list-box .", [1], "item .", [1], "button.", [1], "data-v-849fa24a{background:red;border-radius:4px;bottom:6px;color:#fff;font-size:12px;padding:3px 10px;position:absolute;right:6px}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-list-box .", [1], "item .", [1], "button.", [1], "hover.", [1], "data-v-849fa24a{opacity:.8}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-skus-box.", [1], "data-v-849fa24a{background-color:#fff;border-radius:6px;bottom:10px;left:100%;position:absolute;top:10px;width:240px}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-skus-box .", [1], "product-sku-list.", [1], "data-v-849fa24a{padding:10px;position:relative}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-skus-box .", [1], "product-sku-list .", [1], "product-sku-list-title.", [1], "data-v-849fa24a{color:#333;font-size:15px;font-weight:700;line-height:21px;padding:10px 15px 15px;position:relative}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-skus-box .", [1], "product-sku-list .", [1], "product-sku-item.", [1], "data-v-849fa24a{-webkit-align-items:center;align-items:center;background:#f6f6f6;border-radius:19px;color:#666;display:-webkit-inline-flex;display:inline-flex;font-size:13px;margin-bottom:10px;margin-right:10px;padding:6px 15px}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-skus-box .", [1], "product-sku-list .", [1], "product-sku-item.", [1], "hover.", [1], "data-v-849fa24a{background:#ccc}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-skus-box .", [1], "product-sku-list .", [1], "product-sku-item .", [1], "product-sku-price.", [1], "data-v-849fa24a{color:red;font-size:15px;margin-left:6px}\n.", [1], "product-modal .", [1], "modal-panel .", [1], "product-skus-box .", [1], "product-sku-list .", [1], "product-sku-item .", [1], "product-sku-price .", [1], "unit.", [1], "data-v-849fa24a{font-size:12px}\n.", [1], "pay-result-modal.", [1], "data-v-849fa24a{background-color:rgba(0,0,0,.5);bottom:0;left:0;position:fixed;right:0;top:0;z-index:1001}\n.", [1], "pay-result-modal .", [1], "pay-result-panel.", [1], "data-v-849fa24a{border-radius:10px;left:20px;overflow:hidden;position:absolute;right:20px;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}\n.", [1], "pay-result-modal .", [1], "pay-result-panel .", [1], "pay-result-top.", [1], "data-v-849fa24a{-webkit-align-items:center;align-items:center;background-color:#fbfbfb;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;-webkit-justify-content:center;justify-content:center;padding:20px 0 40px}\n.", [1], "pay-result-modal .", [1], "pay-result-panel .", [1], "pay-result-top .", [1], "pay-status.", [1], "data-v-849fa24a{-webkit-align-items:center;align-items:center;color:#111;display:-webkit-flex;display:flex}\n.", [1], "pay-result-modal .", [1], "pay-result-panel .", [1], "pay-result-top .", [1], "pay-status .", [1], "pay-status-icon.", [1], "data-v-849fa24a{background-color:#3cbf7e;border-radius:50%;height:30px;line-height:30px;margin-right:5px;text-align:center;width:30px}\n.", [1], "pay-result-modal .", [1], "pay-result-panel .", [1], "pay-result-top .", [1], "pay-status .", [1], "pay-status-icon .", [1], "iconfont.", [1], "data-v-849fa24a{color:#fff;font-size:14px}\n.", [1], "pay-result-modal .", [1], "pay-result-panel .", [1], "pay-result-top .", [1], "pay-status .", [1], "pay-status-text.", [1], "data-v-849fa24a{font-size:18px;font-weight:700}\n.", [1], "pay-result-modal .", [1], "pay-result-panel .", [1], "pay-result-top .", [1], "pay-button.", [1], "data-v-849fa24a{-webkit-align-items:center;align-items:center;border-radius:5px;color:#333;display:-webkit-flex;display:flex;height:36px;-webkit-justify-content:center;justify-content:center;margin-top:20px;width:100px}\n.", [1], "pay-result-modal .", [1], "pay-result-panel .", [1], "pay-result-top .", [1], "pay-button.", [1], "data-v-849fa24a::before{border-color:#333}\n.", [1], "pay-result-modal .", [1], "pay-result-panel .", [1], "pay-result-middle.", [1], "data-v-849fa24a{margin-top:-20px;position:relative}\n.", [1], "common-modal.", [1], "data-v-849fa24a{-webkit-align-items:center;align-items:center;background-color:rgba(0,0,0,.7);bottom:0;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;-webkit-justify-content:center;justify-content:center;left:0;position:fixed;right:0;top:0;z-index:200}\n.", [1], "common-modal .", [1], "common-modal-panel.", [1], "data-v-849fa24a{background-color:#fff;border-radius:10px;box-shadow:0 0 10px 2px rgba(0,0,0,.05);box-sizing:border-box;max-width:400px;padding:30px 40px 20px;text-align:center;width:", [0, 670], "}\n.", [1], "common-modal .", [1], "common-modal-panel .", [1], "common-modal-panel-title.", [1], "data-v-849fa24a{font-size:18px;font-weight:700;margin-bottom:10px}\n.", [1], "common-modal .", [1], "common-modal-panel .", [1], "common-modal-panel-content.", [1], "data-v-849fa24a{font-size:13px;margin-bottom:15px}\n.", [1], "common-modal .", [1], "common-modal-panel .", [1], "image-wrap.", [1], "data-v-849fa24a{height:150px;margin:0 auto 20px;position:relative;width:150px}\n.", [1], "common-modal .", [1], "common-modal-panel .", [1], "image-wrap.", [1], "data-v-849fa24a:before{-webkit-animation:weuiLoading 1s steps(12) infinite;animation:weuiLoading 1s steps(12) infinite;background:transparent url(\x22data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+PHBhdGggZmlsbD0ibm9uZSIgZD0iTTAgMGgxMDB2MTAwSDB6Ii8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTlFOUU5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAgLTMwKSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iIzk4OTY5NyIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgzMCAxMDUuOTggNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjOUI5OTlBIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDYwIDc1Ljk4IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0EzQTFBMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSg5MCA2NSA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNBQkE5QUEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoMTIwIDU4LjY2IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0IyQjJCMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgxNTAgNTQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjQkFCOEI5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDE4MCA1MCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDMkMwQzEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTE1MCA0NS45OCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDQkNCQ0IiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTEyMCA0MS4zNCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNEMkQyRDIiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTkwIDM1IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0RBREFEQSIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgtNjAgMjQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTJFMkUyIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKC0zMCAtNS45OCA2NSkiLz48L3N2Zz4\x3d\x22) no-repeat;background-size:100%;content:\x22\x22;height:34px;left:50%;margin-left:-20px;margin-top:-20px;position:absolute;top:50%;width:34px;z-index:1}\n.", [1], "common-modal .", [1], "common-modal-panel .", [1], "image-wrap wx-image.", [1], "data-v-849fa24a{background-color:#eee;height:100%;left:0;position:absolute;top:0;width:100%;z-index:2}\n.", [1], "common-modal .", [1], "common-modal-panel .", [1], "tip.", [1], "data-v-849fa24a{color:#aaa;font-size:12px}\n.", [1], "common-modal .", [1], "common-modal-panel .", [1], "input-form.", [1], "data-v-849fa24a{margin-left:-20px;margin-right:-20px;margin-top:20px;text-align:left}\n.", [1], "common-modal .", [1], "tip2.", [1], "data-v-849fa24a{color:#aaa;font-size:12px;margin-top:20px;text-decoration:underline}\n.", [1], "no-support.", [1], "data-v-849fa24a{padding:50px 0;text-align:center}\n.", [1], "no-support2.", [1], "data-v-849fa24a{background-color:#fff;bottom:0;font-size:17px;left:0;padding-top:200px;position:fixed;right:0;text-align:center;top:0;z-index:10000}\n", ], "Some selectors are not allowed in component wxss, including tag name selectors, ID selectors, and attribute selectors.(./pages/landing-page.wxss:1:14824)", {
        path: "./pages/landing-page.wxss"
    });
}