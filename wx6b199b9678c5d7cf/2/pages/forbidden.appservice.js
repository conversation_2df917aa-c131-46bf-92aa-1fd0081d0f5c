$gwx_XC_98 = function(_, _v, _n, _p, _s, _wp, _wl, $gwn, $gwl, $gwh, wh, $gstack, $gwrt, gra, grb, TestTest, wfor, _ca, _da, _r, _rz, _o, _oz, _1, _1z, _2, _2z, _m, _mz, nv_getDate, nv_getRegExp, nv_console, nv_parseInt, nv_parseFloat, nv_isNaN, nv_isFinite, nv_decodeURI, nv_decodeURIComponent, nv_encodeURI, nv_encodeURIComponent, $gdc, nv_JSON, _af, _gv, _ai, _grp, _gd, _gapi, $ixc, _ic, _w, _ev, _tsd) {
    return function(path, global) {
        if (typeof global === 'undefined') {
            if (typeof __GWX_GLOBAL__ === 'undefined') global = {};
            else global = __GWX_GLOBAL__;
        }
        if (typeof __WXML_GLOBAL__ === 'undefined') {
            __WXML_GLOBAL__ = {};
        }
        __WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
        var e_ = {}
        if (typeof(global.entrys) === 'undefined') global.entrys = {};
        e_ = global.entrys;
        var d_ = {}
        if (typeof(global.defines) === 'undefined') global.defines = {};
        d_ = global.defines;
        var f_ = {}
        if (typeof(global.modules) === 'undefined') global.modules = {};
        f_ = global.modules || {};
        var p_ = {}
        __WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
        __WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
        __WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
        var z = __WXML_GLOBAL__.ops_set.$gwx_XC_98 || [];

        function gz$gwx_XC_98_1() {
            if (__WXML_GLOBAL__.ops_cached.$gwx_XC_98_1) return __WXML_GLOBAL__.ops_cached.$gwx_XC_98_1
            __WXML_GLOBAL__.ops_cached.$gwx_XC_98_1 = [];
            (function(z) {
                var a = 11;

                function Z(ops) {
                    z.push(ops)
                }
                Z([3, '__l'])
                Z([3, '请在手机端打开小程序'])
                Z([3, 'search'])
                Z([3, '8a7ac094-1'])
            })(__WXML_GLOBAL__.ops_cached.$gwx_XC_98_1);
            return __WXML_GLOBAL__.ops_cached.$gwx_XC_98_1
        }
        __WXML_GLOBAL__.ops_set.$gwx_XC_98 = z;
        __WXML_GLOBAL__.ops_init.$gwx_XC_98 = true;
        var x = ['./pages/forbidden.wxml'];
        d_[x[0]] = {}
        var m0 = function(e, s, r, gg) {
            var z = gz$gwx_XC_98_1()
            var cBDB = _mz(z, 'xk-empty', ['bind:__l', 0, 'text', 1, 'type', 1, 'vueId', 2], [], e, s, gg)
            _(r, cBDB)
            return r
        }
        e_[x[0]] = {
            f: m0,
            j: [],
            i: [],
            ti: [],
            ic: []
        }
        if (path && e_[path]) {
            return function(env, dd, global) {
                $gwxc = 0;
                var root = {
                    "tag": "wx-page"
                };
                root.children = [];
                g = "$gwx_XC_98";
                var main = e_[path].f
                if (typeof global === "undefined") global = {};
                global.f = $gdc(f_[path], "", 1);
                try {
                    main(env, {}, root, global);
                    _tsd(root)
                } catch (err) {
                    console.log(err)
                };
                g = "";
                return root;
            }
        }
    }
}(__g.a, __g.b, __g.c, __g.d, __g.e, __g.f, __g.g, __g.h, __g.i, __g.j, __g.k, __g.l, __g.m, __g.n, __g.o, __g.p, __g.q, __g.r, __g.s, __g.t, __g.u, __g.v, __g.w, __g.x, __g.y, __g.z, __g.A, __g.B, __g.C, __g.D, __g.E, __g.F, __g.G, __g.H, __g.I, __g.J, __g.K, __g.L, __g.M, __g.N, __g.O, __g.P, __g.Q, __g.R, __g.S, __g.T, __g.U, __g.V, __g.W, __g.X, __g.Y, __g.Z, __g.aa);
if (__vd_version_info__.delayedGwx || false) $gwx_XC_98();
if (__vd_version_info__.delayedGwx) __wxAppCode__['pages/forbidden.wxml'] = [$gwx_XC_98, './pages/forbidden.wxml'];
else __wxAppCode__['pages/forbidden.wxml'] = $gwx_XC_98('./pages/forbidden.wxml');;
__wxRoute = "pages/forbidden";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/forbidden.js";
define("pages/forbidden.js", function(require, module, exports, window, document, frames, self, location, navigator, localStorage, history, Caches, screen, alert, confirm, prompt, XMLHttpRequest, WebSocket, Reporter, webkit, WeixinJSCore) {
    (global.webpackJsonp = global.webpackJsonp || []).push([
        ["pages/forbidden"], {
            "0a53": function(t, e, n) {
                "use strict";
                (function(t, e) {
                    var a = n("47a9");
                    n("8f74"), a(n("3240"));
                    var o = a(n("adf2"));
                    t.__webpack_require_UNI_MP_PLUGIN__ = n, e(o.default)
                }).call(this, n("3223").default, n("df3c").createPage)
            },
            "1b53": function(t, e, n) {
                "use strict";
                n.d(e, "b", (function() {
                    return a
                })), n.d(e, "c", (function() {
                    return o
                })), n.d(e, "a", (function() {}));
                var a = function() {
                        this.$createElement;
                        this._self._c
                    },
                    o = []
            },
            "9e27": function(t, e, n) {
                "use strict";
                (function(t, a) {
                    var o = n("47a9");
                    Object.defineProperty(e, "__esModule", {
                        value: !0
                    }), e.default = void 0;
                    var r = o(n("7eb4")),
                        u = o(n("ee10")),
                        i = n("7203"),
                        c = {
                            data: function() {
                                return {
                                    text: ""
                                }
                            },
                            onLoad: function() {},
                            onShow: function() {
                                t.hideHomeButton()
                            },
                            methods: {
                                getBrowser: function() {
                                    var t = this;
                                    return (0, u.default)(r.default.mark((function e() {
                                        var n, a, o;
                                        return r.default.wrap((function(e) {
                                            for (;;) switch (e.prev = e.next) {
                                                case 0:
                                                    return e.next = 2, t.$http({
                                                        url: "/api/config/h5browser"
                                                    });
                                                case 2:
                                                    0 == (n = e.sent).errno && (a = n.data.context.browser, o = [], 1 == a.weixin && o.push("微信"), 1 == a.dingding && o.push("钉钉"), 1 == a.other && o.push("系统浏览器"), o.length ? t.text = "仅支持在".concat(o.join("/"), "内打开") : t.text = "", (0, i.isWechat)() ? 1 == a.weixin && t.goHome() : (0, i.isDingtalk)() ? 1 == a.dingding && t.goHome() : 1 == a.other && t.goHome());
                                                case 4:
                                                case "end":
                                                    return e.stop()
                                            }
                                        }), e)
                                    })))()
                                },
                                copyLink: function() {
                                    var t = this,
                                        e = location.href,
                                        n = e.substr(0, e.indexOf("/pages"));
                                    a.setClipboardData({
                                        data: n,
                                        success: function() {
                                            t.$api.toast("已复制")
                                        }
                                    })
                                },
                                goHome: function() {
                                    a.reLaunch({
                                        url: "/pages/index"
                                    })
                                }
                            }
                        };
                    e.default = c
                }).call(this, n("3223").default, n("df3c").default)
            },
            adf2: function(t, e, n) {
                "use strict";
                n.r(e);
                var a = n("1b53"),
                    o = n("c403");
                for (var r in o)["default"].indexOf(r) < 0 && function(t) {
                    n.d(e, t, (function() {
                        return o[t]
                    }))
                }(r);
                var u = n("828b"),
                    i = Object(u.a)(o.default, a.b, a.c, !1, null, null, null, !1, a.a, void 0);
                e.default = i.exports
            },
            c403: function(t, e, n) {
                "use strict";
                n.r(e);
                var a = n("9e27"),
                    o = n.n(a);
                for (var r in a)["default"].indexOf(r) < 0 && function(t) {
                    n.d(e, t, (function() {
                        return a[t]
                    }))
                }(r);
                e.default = o.a
            }
        },
        [
            ["0a53", "common/runtime", "common/vendor"]
        ]
    ]);
}, {
    isPage: true,
    isComponent: true,
    currentFile: 'pages/forbidden.js'
});
require("pages/forbidden.js");