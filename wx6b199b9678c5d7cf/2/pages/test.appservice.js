$gwx_XC_102 = function(_, _v, _n, _p, _s, _wp, _wl, $gwn, $gwl, $gwh, wh, $gstack, $gwrt, gra, grb, TestTest, wfor, _ca, _da, _r, _rz, _o, _oz, _1, _1z, _2, _2z, _m, _mz, nv_getDate, nv_getRegExp, nv_console, nv_parseInt, nv_parseFloat, nv_isNaN, nv_isFinite, nv_decodeURI, nv_decodeURIComponent, nv_encodeURI, nv_encodeURIComponent, $gdc, nv_JSON, _af, _gv, _ai, _grp, _gd, _gapi, $ixc, _ic, _w, _ev, _tsd) {
    return function(path, global) {
        if (typeof global === 'undefined') {
            if (typeof __GWX_GLOBAL__ === 'undefined') global = {};
            else global = __GWX_GLOBAL__;
        }
        if (typeof __WXML_GLOBAL__ === 'undefined') {
            __WXML_GLOBAL__ = {};
        }
        __WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
        var e_ = {}
        if (typeof(global.entrys) === 'undefined') global.entrys = {};
        e_ = global.entrys;
        var d_ = {}
        if (typeof(global.defines) === 'undefined') global.defines = {};
        d_ = global.defines;
        var f_ = {}
        if (typeof(global.modules) === 'undefined') global.modules = {};
        f_ = global.modules || {};
        var p_ = {}
        __WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
        __WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
        __WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
        var z = __WXML_GLOBAL__.ops_set.$gwx_XC_102 || [];

        function gz$gwx_XC_102_1() {
            if (__WXML_GLOBAL__.ops_cached.$gwx_XC_102_1) return __WXML_GLOBAL__.ops_cached.$gwx_XC_102_1
            __WXML_GLOBAL__.ops_cached.$gwx_XC_102_1 = [];
            (function(z) {
                var a = 11;

                function Z(ops) {
                    z.push(ops)
                }
                Z([
                    [7],
                    [3, 'show']
                ])
            })(__WXML_GLOBAL__.ops_cached.$gwx_XC_102_1);
            return __WXML_GLOBAL__.ops_cached.$gwx_XC_102_1
        }
        __WXML_GLOBAL__.ops_set.$gwx_XC_102 = z;
        __WXML_GLOBAL__.ops_init.$gwx_XC_102 = true;
        var x = ['./pages/test.wxml'];
        d_[x[0]] = {}
        var m0 = function(e, s, r, gg) {
            var z = gz$gwx_XC_102_1()
            var aFFB = _v()
            _(r, aFFB)
            if (_oz(z, 0, e, s, gg)) {
                aFFB.wxVkey = 1
            }
            aFFB.wxXCkey = 1
            return r
        }
        e_[x[0]] = {
            f: m0,
            j: [],
            i: [],
            ti: [],
            ic: []
        }
        if (path && e_[path]) {
            return function(env, dd, global) {
                $gwxc = 0;
                var root = {
                    "tag": "wx-page"
                };
                root.children = [];
                g = "$gwx_XC_102";
                var main = e_[path].f
                if (typeof global === "undefined") global = {};
                global.f = $gdc(f_[path], "", 1);
                try {
                    main(env, {}, root, global);
                    _tsd(root)
                } catch (err) {
                    console.log(err)
                };
                g = "";
                return root;
            }
        }
    }
}(__g.a, __g.b, __g.c, __g.d, __g.e, __g.f, __g.g, __g.h, __g.i, __g.j, __g.k, __g.l, __g.m, __g.n, __g.o, __g.p, __g.q, __g.r, __g.s, __g.t, __g.u, __g.v, __g.w, __g.x, __g.y, __g.z, __g.A, __g.B, __g.C, __g.D, __g.E, __g.F, __g.G, __g.H, __g.I, __g.J, __g.K, __g.L, __g.M, __g.N, __g.O, __g.P, __g.Q, __g.R, __g.S, __g.T, __g.U, __g.V, __g.W, __g.X, __g.Y, __g.Z, __g.aa);
if (__vd_version_info__.delayedGwx || false) $gwx_XC_102();
if (__vd_version_info__.delayedGwx) __wxAppCode__['pages/test.wxml'] = [$gwx_XC_102, './pages/test.wxml'];
else __wxAppCode__['pages/test.wxml'] = $gwx_XC_102('./pages/test.wxml');;
__wxRoute = "pages/test";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/test.js";
define("pages/test.js", function(require, module, exports, window, document, frames, self, location, navigator, localStorage, history, Caches, screen, alert, confirm, prompt, XMLHttpRequest, WebSocket, Reporter, webkit, WeixinJSCore) {
    (global.webpackJsonp = global.webpackJsonp || []).push([
        ["pages/test"], {
            "6a49": function(n, t, e) {
                "use strict";
                e.d(t, "b", (function() {
                    return f
                })), e.d(t, "c", (function() {
                    return a
                })), e.d(t, "a", (function() {}));
                var f = function() {
                        this.$createElement;
                        this._self._c
                    },
                    a = []
            },
            "7f3f": function(n, t, e) {
                "use strict";
                e.r(t);
                var f = e("6a49"),
                    a = e("9df2");
                for (var u in a)["default"].indexOf(u) < 0 && function(n) {
                    e.d(t, n, (function() {
                        return a[n]
                    }))
                }(u);
                var c = e("828b"),
                    o = Object(c.a)(a.default, f.b, f.c, !1, null, "66fe4e9a", null, !1, f.a, void 0);
                t.default = o.exports
            },
            "9a34": function(n, t, e) {
                "use strict";
                (function(n, t) {
                    var f = e("47a9");
                    e("8f74"), f(e("3240"));
                    var a = f(e("7f3f"));
                    n.__webpack_require_UNI_MP_PLUGIN__ = e, t(a.default)
                }).call(this, e("3223").default, e("df3c").createPage)
            },
            "9df2": function(n, t, e) {
                "use strict";
                e.r(t);
                var f = e("a19f"),
                    a = e.n(f);
                for (var u in f)["default"].indexOf(u) < 0 && function(n) {
                    e.d(t, n, (function() {
                        return f[n]
                    }))
                }(u);
                t.default = a.a
            },
            a19f: function(n, t, e) {
                "use strict";
                (function(n) {
                    Object.defineProperty(t, "__esModule", {
                        value: !0
                    }), t.default = void 0;
                    var e = {
                        data: function() {
                            return {
                                show: !1
                            }
                        },
                        onReady: function() {
                            n.getDeviceInfo({
                                success: function(n) {
                                    console.log(n)
                                }
                            })
                        }
                    };
                    t.default = e
                }).call(this, e("df3c").default)
            }
        },
        [
            ["9a34", "common/runtime", "common/vendor"]
        ]
    ]);
}, {
    isPage: true,
    isComponent: true,
    currentFile: 'pages/test.js'
});
require("pages/test.js");