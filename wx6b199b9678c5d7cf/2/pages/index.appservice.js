$gwx_XC_99 = function(_, _v, _n, _p, _s, _wp, _wl, $gwn, $gwl, $gwh, wh, $gstack, $gwrt, gra, grb, TestTest, wfor, _ca, _da, _r, _rz, _o, _oz, _1, _1z, _2, _2z, _m, _mz, nv_getDate, nv_getRegExp, nv_console, nv_parseInt, nv_parseFloat, nv_isNaN, nv_isFinite, nv_decodeURI, nv_decodeURIComponent, nv_encodeURI, nv_encodeURIComponent, $gdc, nv_JSON, _af, _gv, _ai, _grp, _gd, _gapi, $ixc, _ic, _w, _ev, _tsd) {
    return function(path, global) {
        if (typeof global === 'undefined') {
            if (typeof __GWX_GLOBAL__ === 'undefined') global = {};
            else global = __GWX_GLOBAL__;
        }
        if (typeof __WXML_GLOBAL__ === 'undefined') {
            __WXML_GLOBAL__ = {};
        }
        __WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
        var e_ = {}
        if (typeof(global.entrys) === 'undefined') global.entrys = {};
        e_ = global.entrys;
        var d_ = {}
        if (typeof(global.defines) === 'undefined') global.defines = {};
        d_ = global.defines;
        var f_ = {}
        if (typeof(global.modules) === 'undefined') global.modules = {};
        f_ = global.modules || {};
        var p_ = {}
        __WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
        __WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
        __WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
        var z = __WXML_GLOBAL__.ops_set.$gwx_XC_99 || [];

        function gz$gwx_XC_99_1() {
            if (__WXML_GLOBAL__.ops_cached.$gwx_XC_99_1) return __WXML_GLOBAL__.ops_cached.$gwx_XC_99_1
            __WXML_GLOBAL__.ops_cached.$gwx_XC_99_1 = [];
            (function(z) {
                var a = 11;

                function Z(ops) {
                    z.push(ops)
                }
                Z([
                    [7],
                    [3, 'platformReady']
                ])
                Z([
                    [7],
                    [3, 'pageReady']
                ])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'g0']
                ])
                Z([3, '__l'])
                Z([
                    [2, '||'],
                    [
                        [2, '||'],
                        [
                            [2, '||'],
                            [
                                [2, '=='],
                                [
                                    [7],
                                    [3, 'pageType']
                                ],
                                [1, 'course']
                            ],
                            [
                                [2, '=='],
                                [
                                    [7],
                                    [3, 'pageType']
                                ],
                                [1, 'word']
                            ]
                        ],
                        [
                            [2, '=='],
                            [
                                [7],
                                [3, 'pageType']
                            ],
                            [1, 'find']
                        ]
                    ],
                    [
                        [2, '=='],
                        [
                            [7],
                            [3, 'pageType']
                        ],
                        [1, 'shop']
                    ]
                ])
                Z([
                    [2, '?:'],
                    [
                        [2, '=='],
                        [
                            [7],
                            [3, 'pageType']
                        ],
                        [1, 'filepacket']
                    ],
                    [
                        [7],
                        [3, 'fileColor']
                    ],
                    [1, '']
                ])
                Z([
                    [2, '==='],
                    [
                        [6],
                        [
                            [6],
                            [
                                [7],
                                [3, 'tabBarList']
                            ],
                            [
                                [7],
                                [3, 'activeTabIndex']
                            ]
                        ],
                        [3, 'lightNavBar']
                    ],
                    [1, true]
                ])
                Z([
                    [7],
                    [3, 'navBarOpacity']
                ])
                Z([
                    [2, '!='],
                    [
                        [7],
                        [3, 'pageType']
                    ],
                    [1, 'find']
                ])
                Z([
                    [2, '||'],
                    [
                        [2, '||'],
                        [
                            [2, '=='],
                            [
                                [7],
                                [3, 'pageType']
                            ],
                            [1, 'shop']
                        ],
                        [
                            [2, '=='],
                            [
                                [7],
                                [3, 'pageType']
                            ],
                            [1, 'camp']
                        ]
                    ],
                    [
                        [2, '=='],
                        [
                            [7],
                            [3, 'pageType']
                        ],
                        [1, 'filepacket']
                    ]
                ])
                Z([
                    [2, '||'],
                    [
                        [6],
                        [
                            [6],
                            [
                                [7],
                                [3, 'tabBarList']
                            ],
                            [
                                [7],
                                [3, 'activeTabIndex']
                            ]
                        ],
                        [3, 'title']
                    ],
                    [
                        [6],
                        [
                            [6],
                            [
                                [7],
                                [3, 'tabBarList']
                            ],
                            [
                                [7],
                                [3, 'activeTabIndex']
                            ]
                        ],
                        [3, 'text']
                    ]
                ])
                Z([3, '18efaafd-1'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [5],
                            [
                                [5],
                                [1, 'custom']
                            ],
                            [1, 'left']
                        ],
                        [1, 'bottom']
                    ]
                ])
                Z([3, 'custom'])
                Z([
                    [2, '=='],
                    [
                        [7],
                        [3, 'pageType']
                    ],
                    [1, 'course']
                ])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'adminConfig']
                    ],
                    [3, 'pointSwitch']
                ])
                Z([3, '__e'])
                Z([3, 'action point no-transition'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, 'tap']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [
                                                    [5],
                                                    [1, 'goPoint']
                                                ],
                                                [
                                                    [4],
                                                    [
                                                        [5],
                                                        [1, '$event']
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([
                    [7],
                    [3, 'newPointNum']
                ])
                Z([
                    [2, '=='],
                    [
                        [7],
                        [3, 'pageType']
                    ],
                    [1, 'word']
                ])
                Z([
                    [2, '=='],
                    [
                        [7],
                        [3, 'pageType']
                    ],
                    [1, 'find']
                ])
                Z([
                    [2, '=='],
                    [
                        [7],
                        [3, 'pageType']
                    ],
                    [1, 'shop']
                ])
                Z([
                    [7],
                    [3, 'useCart']
                ])
                Z(z[16])
                Z([3, 'shop-nav-box-cart'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, 'tap']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [
                                                    [5],
                                                    [1, 'goCart']
                                                ],
                                                [
                                                    [4],
                                                    [
                                                        [5],
                                                        [1, '$event']
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([
                    [7],
                    [3, 'cartGoodsNum']
                ])
                Z([3, 'left'])
                Z([
                    [2, '&&'],
                    [
                        [2, '=='],
                        [
                            [7],
                            [3, 'pageType']
                        ],
                        [1, 'qbank']
                    ],
                    [
                        [6],
                        [
                            [7],
                            [3, 'adminConfig']
                        ],
                        [3, 'questionSearchSwitch']
                    ]
                ])
                Z([3, 'bottom'])
                Z([
                    [2, '||'],
                    [
                        [2, '=='],
                        [
                            [7],
                            [3, 'pageType']
                        ],
                        [1, 'course']
                    ],
                    [
                        [2, '=='],
                        [
                            [7],
                            [3, 'pageType']
                        ],
                        [1, 'home']
                    ]
                ])
                Z(z[3])
                Z([
                    [7],
                    [3, 'addMpLeft']
                ])
                Z([
                    [2, '+'],
                    [
                        [2, '+'],
                        [1, '18efaafd-2'],
                        [1, ',']
                    ],
                    [1, '18efaafd-1']
                ])
                Z([
                    [7],
                    [3, 'activeTabIndex']
                ])
                Z(z[3])
                Z(z[16])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^changeActiveTab']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'changeActiveTab']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([
                    [7],
                    [3, 'tabBarData']
                ])
                Z([3, '18efaafd-3'])
                Z(z[3])
                Z([3, '18efaafd-4'])
                Z([
                    [4],
                    [
                        [5],
                        [1, 'default']
                    ]
                ])
                Z([3, 'index'])
                Z([3, 'tab'])
                Z([
                    [7],
                    [3, 'tabBarList']
                ])
                Z(z[44])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'tab']
                    ],
                    [3, 'startLoad']
                ])
                Z([
                    [2, '=='],
                    [
                        [6],
                        [
                            [7],
                            [3, 'tab']
                        ],
                        [3, 'type']
                    ],
                    [1, 'home']
                ])
                Z([
                    [2, '=='],
                    [
                        [7],
                        [3, 'activeTabIndex']
                    ],
                    [
                        [7],
                        [3, 'index']
                    ]
                ])
                Z([
                    [7],
                    [3, 'adminConfig']
                ])
                Z(z[3])
                Z([3, 'vue-ref-in-for'])
                Z([3, 'home'])
                Z([1, true])
                Z([
                    [7],
                    [3, 'homeLayout']
                ])
                Z([
                    [2, '+'],
                    [
                        [2, '+'],
                        [
                            [2, '+'],
                            [1, '18efaafd-5-'],
                            [
                                [7],
                                [3, 'index']
                            ]
                        ],
                        [1, ',']
                    ],
                    [1, '18efaafd-4']
                ])
                Z([
                    [7],
                    [3, 'homeWepage']
                ])
                Z([
                    [2, '=='],
                    [
                        [6],
                        [
                            [7],
                            [3, 'tab']
                        ],
                        [3, 'type']
                    ],
                    [1, 'course']
                ])
                Z(z[50])
                Z(z[51])
                Z(z[3])
                Z(z[53])
                Z([3, 'course'])
                Z([
                    [7],
                    [3, 'courseLayout']
                ])
                Z([
                    [2, '+'],
                    [
                        [2, '+'],
                        [
                            [2, '+'],
                            [1, '18efaafd-6-'],
                            [
                                [7],
                                [3, 'index']
                            ]
                        ],
                        [1, ',']
                    ],
                    [1, '18efaafd-4']
                ])
                Z(z[58])
                Z([
                    [2, '=='],
                    [
                        [6],
                        [
                            [7],
                            [3, 'tab']
                        ],
                        [3, 'type']
                    ],
                    [1, 'qbank']
                ])
                Z(z[50])
                Z(z[51])
                Z(z[3])
                Z(z[16])
                Z(z[16])
                Z(z[53])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [5],
                            [
                                [4],
                                [
                                    [5],
                                    [
                                        [5],
                                        [1, '^scroll']
                                    ],
                                    [
                                        [4],
                                        [
                                            [5],
                                            [
                                                [4],
                                                [
                                                    [5],
                                                    [1, 'qbankPageScroll']
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^loaded']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'setQbankShareLink']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'qbank'])
                Z([
                    [7],
                    [3, 'qbankLayout']
                ])
                Z([
                    [7],
                    [3, 'multipleQbank']
                ])
                Z([
                    [7],
                    [3, 'urlQbankId']
                ])
                Z([
                    [2, '+'],
                    [
                        [2, '+'],
                        [
                            [2, '+'],
                            [1, '18efaafd-7-'],
                            [
                                [7],
                                [3, 'index']
                            ]
                        ],
                        [1, ',']
                    ],
                    [1, '18efaafd-4']
                ])
                Z([
                    [2, '=='],
                    [
                        [6],
                        [
                            [7],
                            [3, 'tab']
                        ],
                        [3, 'type']
                    ],
                    [1, 'class']
                ])
                Z(z[51])
                Z(z[3])
                Z(z[53])
                Z([3, 'class'])
                Z([
                    [7],
                    [3, 'classLayout']
                ])
                Z([
                    [2, '+'],
                    [
                        [2, '+'],
                        [
                            [2, '+'],
                            [1, '18efaafd-8-'],
                            [
                                [7],
                                [3, 'index']
                            ]
                        ],
                        [1, ',']
                    ],
                    [1, '18efaafd-4']
                ])
                Z([
                    [2, '=='],
                    [
                        [6],
                        [
                            [7],
                            [3, 'tab']
                        ],
                        [3, 'type']
                    ],
                    [1, 'word']
                ])
                Z(z[3])
                Z(z[53])
                Z([3, 'word'])
                Z([
                    [2, '+'],
                    [
                        [2, '+'],
                        [
                            [2, '+'],
                            [1, '18efaafd-9-'],
                            [
                                [7],
                                [3, 'index']
                            ]
                        ],
                        [1, ',']
                    ],
                    [1, '18efaafd-4']
                ])
                Z([
                    [2, '=='],
                    [
                        [6],
                        [
                            [7],
                            [3, 'tab']
                        ],
                        [3, 'type']
                    ],
                    [1, 'camp']
                ])
                Z(z[16])
                Z([3, 'tab-page-scroll'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, 'scrolltolower']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [
                                                    [5],
                                                    [1, 'onCampReachBottom']
                                                ],
                                                [
                                                    [4],
                                                    [
                                                        [5],
                                                        [1, '$event']
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z(z[55])
                Z(z[3])
                Z(z[53])
                Z([3, 'camp'])
                Z([
                    [7],
                    [3, 'campLayout']
                ])
                Z([
                    [2, '+'],
                    [
                        [2, '+'],
                        [
                            [2, '+'],
                            [1, '18efaafd-10-'],
                            [
                                [7],
                                [3, 'index']
                            ]
                        ],
                        [1, ',']
                    ],
                    [1, '18efaafd-4']
                ])
                Z([
                    [2, '=='],
                    [
                        [6],
                        [
                            [7],
                            [3, 'tab']
                        ],
                        [3, 'type']
                    ],
                    [1, 'filepacket']
                ])
                Z(z[16])
                Z([3, 'tab-page-scroll filepacket'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, 'scrolltolower']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [
                                                    [5],
                                                    [1, 'onFileReachBottom']
                                                ],
                                                [
                                                    [4],
                                                    [
                                                        [5],
                                                        [1, '$event']
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z(z[55])
                Z([
                    [2, '+'],
                    [
                        [2, '+'],
                        [1, 'background:'],
                        [
                            [7],
                            [3, 'fileColor']
                        ]
                    ],
                    [1, ';']
                ])
                Z(z[51])
                Z([
                    [7],
                    [3, 'fileBgImg']
                ])
                Z([
                    [7],
                    [3, 'fileBgImgLink']
                ])
                Z(z[3])
                Z(z[53])
                Z([3, 'filepacket'])
                Z([
                    [2, '+'],
                    [
                        [2, '+'],
                        [
                            [2, '+'],
                            [1, '18efaafd-11-'],
                            [
                                [7],
                                [3, 'index']
                            ]
                        ],
                        [1, ',']
                    ],
                    [1, '18efaafd-4']
                ])
                Z([
                    [2, '=='],
                    [
                        [6],
                        [
                            [7],
                            [3, 'tab']
                        ],
                        [3, 'type']
                    ],
                    [1, 'find']
                ])
                Z([
                    [2, '=='],
                    [
                        [7],
                        [3, 'findTabActiveNavType']
                    ],
                    [1, 'community']
                ])
                Z(z[16])
                Z(z[16])
                Z(z[95])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [5],
                            [
                                [4],
                                [
                                    [5],
                                    [
                                        [5],
                                        [1, 'scrolltolower']
                                    ],
                                    [
                                        [4],
                                        [
                                            [5],
                                            [
                                                [4],
                                                [
                                                    [5],
                                                    [
                                                        [5],
                                                        [1, 'onCommunityReachBottom']
                                                    ],
                                                    [
                                                        [4],
                                                        [
                                                            [5],
                                                            [1, '$event']
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, 'scroll']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [
                                                    [5],
                                                    [1, 'onCommunityScroll']
                                                ],
                                                [
                                                    [4],
                                                    [
                                                        [5],
                                                        [1, '$event']
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z(z[55])
                Z(z[3])
                Z(z[53])
                Z([3, 'community'])
                Z([
                    [2, '+'],
                    [
                        [2, '+'],
                        [
                            [2, '+'],
                            [1, '18efaafd-12-'],
                            [
                                [7],
                                [3, 'index']
                            ]
                        ],
                        [1, ',']
                    ],
                    [1, '18efaafd-4']
                ])
                Z([
                    [2, '=='],
                    [
                        [7],
                        [3, 'findTabActiveNavType']
                    ],
                    [1, 'article']
                ])
                Z(z[3])
                Z(z[53])
                Z([3, 'article'])
                Z([
                    [2, '+'],
                    [
                        [2, '+'],
                        [
                            [2, '+'],
                            [1, '18efaafd-13-'],
                            [
                                [7],
                                [3, 'index']
                            ]
                        ],
                        [1, ',']
                    ],
                    [1, '18efaafd-4']
                ])
                Z([
                    [2, '=='],
                    [
                        [6],
                        [
                            [7],
                            [3, 'tab']
                        ],
                        [3, 'type']
                    ],
                    [1, 'shop']
                ])
                Z(z[51])
                Z(z[3])
                Z(z[53])
                Z([3, 'shop'])
                Z([
                    [7],
                    [3, 'shopLayout']
                ])
                Z([
                    [2, '+'],
                    [
                        [2, '+'],
                        [
                            [2, '+'],
                            [1, '18efaafd-14-'],
                            [
                                [7],
                                [3, 'index']
                            ]
                        ],
                        [1, ',']
                    ],
                    [1, '18efaafd-4']
                ])
                Z([
                    [2, '=='],
                    [
                        [6],
                        [
                            [7],
                            [3, 'tab']
                        ],
                        [3, 'type']
                    ],
                    [1, 'user']
                ])
                Z(z[51])
                Z(z[3])
                Z(z[16])
                Z(z[53])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^scroll']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'userPageScroll']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'user'])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'tab']
                    ],
                    [3, 'userIconsUrl']
                ])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'tab']
                    ],
                    [3, 'modules']
                ])
                Z([
                    [2, '||'],
                    [
                        [6],
                        [
                            [7],
                            [3, 'tab']
                        ],
                        [3, 'pageStyle']
                    ],
                    [1, 'list']
                ])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'tab']
                    ],
                    [3, 'vipSlogan']
                ])
                Z([
                    [2, '+'],
                    [
                        [2, '+'],
                        [
                            [2, '+'],
                            [1, '18efaafd-15-'],
                            [
                                [7],
                                [3, 'index']
                            ]
                        ],
                        [1, ',']
                    ],
                    [1, '18efaafd-4']
                ])
                Z(z[3])
                Z([3, '18efaafd-16'])
                Z([
                    [7],
                    [3, 'showFoldList']
                ])
                Z([
                    [7],
                    [3, 'isFail']
                ])
                Z(z[3])
                Z(z[16])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^clickButton']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'initPage']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'request'])
                Z([3, '18efaafd-17'])
                Z(z[14])
                Z(z[3])
                Z(z[16])
                Z([3, 'vue-ref'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^selectItem']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'selectActionItem']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'actionMenu'])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'a0']
                ])
                Z(z[54])
                Z([3, '18efaafd-18'])
                Z(z[3])
                Z(z[163])
                Z([3, 'shareMenu'])
                Z([
                    [7],
                    [3, 'shareMenuPushData']
                ])
                Z([
                    [7],
                    [3, 'shareInfo']
                ])
                Z([3, '18efaafd-19'])
                Z(z[3])
                Z(z[16])
                Z(z[163])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^success']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'loginSuccess']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'login'])
                Z([3, '18efaafd-20'])
                Z(z[3])
                Z([3, 'get'])
                Z([3, '18efaafd-21'])
            })(__WXML_GLOBAL__.ops_cached.$gwx_XC_99_1);
            return __WXML_GLOBAL__.ops_cached.$gwx_XC_99_1
        }
        __WXML_GLOBAL__.ops_set.$gwx_XC_99 = z;
        __WXML_GLOBAL__.ops_init.$gwx_XC_99 = true;
        var x = ['./pages/index.wxml'];
        d_[x[0]] = {}
        var m0 = function(e, s, r, gg) {
            var z = gz$gwx_XC_99_1()
            var oDDB = _v()
            _(r, oDDB)
            if (_oz(z, 0, e, s, gg)) {
                oDDB.wxVkey = 1
                var cEDB = _n('view')
                var oFDB = _v()
                _(cEDB, oFDB)
                if (_oz(z, 1, e, s, gg)) {
                    oFDB.wxVkey = 1
                    var aHDB = _v()
                    _(oFDB, aHDB)
                    if (_oz(z, 2, e, s, gg)) {
                        aHDB.wxVkey = 1
                        var eJDB = _mz(z, 'nav-bar', ['bind:__l', 3, 'custom', 1, 'forceBgColor', 2, 'light', 3, 'opacity', 4, 'retainCapsule', 5, 'showShareIcon', 6, 'title', 7, 'vueId', 8, 'vueSlots', 9], [], e, s, gg)
                        var bKDB = _n('view')
                        _rz(z, bKDB, 'slot', 13, e, s, gg)
                        var oLDB = _v()
                        _(bKDB, oLDB)
                        if (_oz(z, 14, e, s, gg)) {
                            oLDB.wxVkey = 1
                            var xMDB = _v()
                            _(oLDB, xMDB)
                            if (_oz(z, 15, e, s, gg)) {
                                xMDB.wxVkey = 1
                                var oNDB = _mz(z, 'view', ['bindtap', 16, 'class', 1, 'data-event-opts', 2], [], e, s, gg)
                                var fODB = _v()
                                _(oNDB, fODB)
                                if (_oz(z, 19, e, s, gg)) {
                                    fODB.wxVkey = 1
                                }
                                fODB.wxXCkey = 1
                                _(xMDB, oNDB)
                            }
                            xMDB.wxXCkey = 1
                        } else {
                            oLDB.wxVkey = 2
                            var cPDB = _v()
                            _(oLDB, cPDB)
                            if (_oz(z, 20, e, s, gg)) {
                                cPDB.wxVkey = 1
                            } else {
                                cPDB.wxVkey = 2
                                var hQDB = _v()
                                _(cPDB, hQDB)
                                if (_oz(z, 21, e, s, gg)) {
                                    hQDB.wxVkey = 1
                                } else {
                                    hQDB.wxVkey = 2
                                    var oRDB = _v()
                                    _(hQDB, oRDB)
                                    if (_oz(z, 22, e, s, gg)) {
                                        oRDB.wxVkey = 1
                                        var cSDB = _v()
                                        _(oRDB, cSDB)
                                        if (_oz(z, 23, e, s, gg)) {
                                            cSDB.wxVkey = 1
                                            var oTDB = _mz(z, 'view', ['bindtap', 24, 'class', 1, 'data-event-opts', 2], [], e, s, gg)
                                            var lUDB = _v()
                                            _(oTDB, lUDB)
                                            if (_oz(z, 27, e, s, gg)) {
                                                lUDB.wxVkey = 1
                                            }
                                            lUDB.wxXCkey = 1
                                            _(cSDB, oTDB)
                                        }
                                        cSDB.wxXCkey = 1
                                    }
                                    oRDB.wxXCkey = 1
                                }
                                hQDB.wxXCkey = 1
                            }
                            cPDB.wxXCkey = 1
                        }
                        oLDB.wxXCkey = 1
                        _(eJDB, bKDB)
                        var aVDB = _n('view')
                        _rz(z, aVDB, 'slot', 28, e, s, gg)
                        var tWDB = _v()
                        _(aVDB, tWDB)
                        if (_oz(z, 29, e, s, gg)) {
                            tWDB.wxVkey = 1
                        }
                        tWDB.wxXCkey = 1
                        _(eJDB, aVDB)
                        var eXDB = _n('view')
                        _rz(z, eXDB, 'slot', 30, e, s, gg)
                        var bYDB = _v()
                        _(eXDB, bYDB)
                        if (_oz(z, 31, e, s, gg)) {
                            bYDB.wxVkey = 1
                            var oZDB = _mz(z, 'add-mp', ['bind:__l', 32, 'left', 1, 'vueId', 2], [], e, s, gg)
                            _(bYDB, oZDB)
                        }
                        bYDB.wxXCkey = 1
                        bYDB.wxXCkey = 3
                        _(eJDB, eXDB)
                        _(aHDB, eJDB)
                        var x1DB = _mz(z, 'tab-bar', ['active', 35, 'bind:__l', 1, 'bind:changeActiveTab', 2, 'data-event-opts', 3, 'tabBar', 4, 'vueId', 5], [], e, s, gg)
                        _(aHDB, x1DB)
                        var o2DB = _mz(z, 'fixed-content', ['bind:__l', 41, 'vueId', 1, 'vueSlots', 2], [], e, s, gg)
                        var f3DB = _v()
                        _(o2DB, f3DB)
                        var c4DB = function(o6DB, h5DB, c7DB, gg) {
                            var l9DB = _v()
                            _(c7DB, l9DB)
                            if (_oz(z, 48, o6DB, h5DB, gg)) {
                                l9DB.wxVkey = 1
                                var a0DB = _v()
                                _(l9DB, a0DB)
                                if (_oz(z, 49, o6DB, h5DB, gg)) {
                                    a0DB.wxVkey = 1
                                    var tAEB = _mz(z, 'tab-course', ['active', 50, 'adminConfig', 1, 'bind:__l', 2, 'class', 3, 'data-ref', 4, 'isHome', 5, 'layout', 6, 'vueId', 7, 'wepage', 8], [], o6DB, h5DB, gg)
                                    _(a0DB, tAEB)
                                } else {
                                    a0DB.wxVkey = 2
                                    var eBEB = _v()
                                    _(a0DB, eBEB)
                                    if (_oz(z, 59, o6DB, h5DB, gg)) {
                                        eBEB.wxVkey = 1
                                        var bCEB = _mz(z, 'tab-course', ['active', 60, 'adminConfig', 1, 'bind:__l', 2, 'class', 3, 'data-ref', 4, 'layout', 5, 'vueId', 6, 'wepage', 7], [], o6DB, h5DB, gg)
                                        _(eBEB, bCEB)
                                    } else {
                                        eBEB.wxVkey = 2
                                        var oDEB = _v()
                                        _(eBEB, oDEB)
                                        if (_oz(z, 68, o6DB, h5DB, gg)) {
                                            oDEB.wxVkey = 1
                                            var xEEB = _mz(z, 'tab-qbank', ['active', 69, 'adminConfig', 1, 'bind:__l', 2, 'bind:loaded', 3, 'bind:scroll', 4, 'class', 5, 'data-event-opts', 6, 'data-ref', 7, 'layout', 8, 'multipleQbank', 9, 'urlQbankId', 10, 'vueId', 11], [], o6DB, h5DB, gg)
                                            _(oDEB, xEEB)
                                        } else {
                                            oDEB.wxVkey = 2
                                            var oFEB = _v()
                                            _(oDEB, oFEB)
                                            if (_oz(z, 81, o6DB, h5DB, gg)) {
                                                oFEB.wxVkey = 1
                                                var fGEB = _mz(z, 'tab-class', ['adminConfig', 82, 'bind:__l', 1, 'class', 2, 'data-ref', 3, 'layout', 4, 'vueId', 5], [], o6DB, h5DB, gg)
                                                _(oFEB, fGEB)
                                            } else {
                                                oFEB.wxVkey = 2
                                                var cHEB = _v()
                                                _(oFEB, cHEB)
                                                if (_oz(z, 88, o6DB, h5DB, gg)) {
                                                    cHEB.wxVkey = 1
                                                    var hIEB = _mz(z, 'tab-word', ['bind:__l', 89, 'class', 1, 'data-ref', 2, 'vueId', 3], [], o6DB, h5DB, gg)
                                                    _(cHEB, hIEB)
                                                } else {
                                                    cHEB.wxVkey = 2
                                                    var oJEB = _v()
                                                    _(cHEB, oJEB)
                                                    if (_oz(z, 93, o6DB, h5DB, gg)) {
                                                        oJEB.wxVkey = 1
                                                        var cKEB = _mz(z, 'scroll-view', ['bindscrolltolower', 94, 'class', 1, 'data-event-opts', 2, 'scrollY', 3], [], o6DB, h5DB, gg)
                                                        var oLEB = _mz(z, 'tab-camp', ['bind:__l', 98, 'class', 1, 'data-ref', 2, 'layout', 3, 'vueId', 4], [], o6DB, h5DB, gg)
                                                        _(cKEB, oLEB)
                                                        _(oJEB, cKEB)
                                                    } else {
                                                        oJEB.wxVkey = 2
                                                        var lMEB = _v()
                                                        _(oJEB, lMEB)
                                                        if (_oz(z, 103, o6DB, h5DB, gg)) {
                                                            lMEB.wxVkey = 1
                                                            var aNEB = _mz(z, 'scroll-view', ['bindscrolltolower', 104, 'class', 1, 'data-event-opts', 2, 'scrollY', 3, 'style', 4], [], o6DB, h5DB, gg)
                                                            var tOEB = _mz(z, 'tab-filepacket', ['adminConfig', 109, 'bgImg', 1, 'bgImgLink', 2, 'bind:__l', 3, 'class', 4, 'data-ref', 5, 'vueId', 6], [], o6DB, h5DB, gg)
                                                            _(aNEB, tOEB)
                                                            _(lMEB, aNEB)
                                                        } else {
                                                            lMEB.wxVkey = 2
                                                            var ePEB = _v()
                                                            _(lMEB, ePEB)
                                                            if (_oz(z, 116, o6DB, h5DB, gg)) {
                                                                ePEB.wxVkey = 1
                                                                var bQEB = _v()
                                                                _(ePEB, bQEB)
                                                                if (_oz(z, 117, o6DB, h5DB, gg)) {
                                                                    bQEB.wxVkey = 1
                                                                    var oREB = _mz(z, 'scroll-view', ['bindscroll', 118, 'bindscrolltolower', 1, 'class', 2, 'data-event-opts', 3, 'scrollY', 4], [], o6DB, h5DB, gg)
                                                                    var xSEB = _mz(z, 'tab-community', ['bind:__l', 123, 'class', 1, 'data-ref', 2, 'vueId', 3], [], o6DB, h5DB, gg)
                                                                    _(oREB, xSEB)
                                                                    _(bQEB, oREB)
                                                                } else {
                                                                    bQEB.wxVkey = 2
                                                                    var oTEB = _v()
                                                                    _(bQEB, oTEB)
                                                                    if (_oz(z, 127, o6DB, h5DB, gg)) {
                                                                        oTEB.wxVkey = 1
                                                                        var fUEB = _mz(z, 'tab-article', ['bind:__l', 128, 'class', 1, 'data-ref', 2, 'vueId', 3], [], o6DB, h5DB, gg)
                                                                        _(oTEB, fUEB)
                                                                    }
                                                                    oTEB.wxXCkey = 1
                                                                    oTEB.wxXCkey = 3
                                                                }
                                                                bQEB.wxXCkey = 1
                                                                bQEB.wxXCkey = 3
                                                                bQEB.wxXCkey = 3
                                                            } else {
                                                                ePEB.wxVkey = 2
                                                                var cVEB = _v()
                                                                _(ePEB, cVEB)
                                                                if (_oz(z, 132, o6DB, h5DB, gg)) {
                                                                    cVEB.wxVkey = 1
                                                                    var hWEB = _mz(z, 'tab-shop', ['adminConfig', 133, 'bind:__l', 1, 'class', 2, 'data-ref', 3, 'layout', 4, 'vueId', 5], [], o6DB, h5DB, gg)
                                                                    _(cVEB, hWEB)
                                                                } else {
                                                                    cVEB.wxVkey = 2
                                                                    var oXEB = _v()
                                                                    _(cVEB, oXEB)
                                                                    if (_oz(z, 139, o6DB, h5DB, gg)) {
                                                                        oXEB.wxVkey = 1
                                                                        var cYEB = _mz(z, 'tab-user', ['adminConfig', 140, 'bind:__l', 1, 'bind:scroll', 2, 'class', 3, 'data-event-opts', 4, 'data-ref', 5, 'userIconsUrl', 6, 'userModules', 7, 'userTheme', 8, 'vipSlogan', 9, 'vueId', 10], [], o6DB, h5DB, gg)
                                                                        _(oXEB, cYEB)
                                                                    }
                                                                    oXEB.wxXCkey = 1
                                                                    oXEB.wxXCkey = 3
                                                                }
                                                                cVEB.wxXCkey = 1
                                                                cVEB.wxXCkey = 3
                                                                cVEB.wxXCkey = 3
                                                            }
                                                            ePEB.wxXCkey = 1
                                                            ePEB.wxXCkey = 3
                                                            ePEB.wxXCkey = 3
                                                        }
                                                        lMEB.wxXCkey = 1
                                                        lMEB.wxXCkey = 3
                                                        lMEB.wxXCkey = 3
                                                    }
                                                    oJEB.wxXCkey = 1
                                                    oJEB.wxXCkey = 3
                                                    oJEB.wxXCkey = 3
                                                }
                                                cHEB.wxXCkey = 1
                                                cHEB.wxXCkey = 3
                                                cHEB.wxXCkey = 3
                                            }
                                            oFEB.wxXCkey = 1
                                            oFEB.wxXCkey = 3
                                            oFEB.wxXCkey = 3
                                        }
                                        oDEB.wxXCkey = 1
                                        oDEB.wxXCkey = 3
                                        oDEB.wxXCkey = 3
                                    }
                                    eBEB.wxXCkey = 1
                                    eBEB.wxXCkey = 3
                                    eBEB.wxXCkey = 3
                                }
                                a0DB.wxXCkey = 1
                                a0DB.wxXCkey = 3
                                a0DB.wxXCkey = 3
                            }
                            l9DB.wxXCkey = 1
                            l9DB.wxXCkey = 3
                            return c7DB
                        }
                        f3DB.wxXCkey = 4
                        _2z(z, 46, c4DB, e, s, gg, f3DB, 'tab', 'index', 'index')
                        _(aHDB, o2DB)
                        var oZEB = _mz(z, 'kefu', ['bind:__l', 151, 'vueId', 1], [], e, s, gg)
                        _(aHDB, oZEB)
                        var tIDB = _v()
                        _(aHDB, tIDB)
                        if (_oz(z, 153, e, s, gg)) {
                            tIDB.wxVkey = 1
                        }
                        tIDB.wxXCkey = 1
                    }
                    aHDB.wxXCkey = 1
                    aHDB.wxXCkey = 3
                } else {
                    oFDB.wxVkey = 2
                    var l1EB = _v()
                    _(oFDB, l1EB)
                    if (_oz(z, 154, e, s, gg)) {
                        l1EB.wxVkey = 1
                        var a2EB = _mz(z, 'xk-empty', ['bind:__l', 155, 'bind:clickButton', 1, 'data-event-opts', 2, 'type', 3, 'vueId', 4], [], e, s, gg)
                        _(l1EB, a2EB)
                    }
                    l1EB.wxXCkey = 1
                    l1EB.wxXCkey = 3
                }
                var lGDB = _v()
                _(cEDB, lGDB)
                if (_oz(z, 160, e, s, gg)) {
                    lGDB.wxVkey = 1
                    var t3EB = _mz(z, 'action-menu', ['bind:__l', 161, 'bind:selectItem', 1, 'class', 2, 'data-event-opts', 3, 'data-ref', 4, 'position', 5, 'type', 6, 'vueId', 7], [], e, s, gg)
                    _(lGDB, t3EB)
                }
                var e4EB = _mz(z, 'xk-share', ['bind:__l', 169, 'class', 1, 'data-ref', 2, 'pushData', 3, 'shareInfo', 4, 'vueId', 5], [], e, s, gg)
                _(cEDB, e4EB)
                var b5EB = _mz(z, 'xk-login', ['bind:__l', 175, 'bind:success', 1, 'class', 2, 'data-event-opts', 3, 'data-ref', 4, 'vueId', 5], [], e, s, gg)
                _(cEDB, b5EB)
                var o6EB = _mz(z, 'mp-privacy', ['bind:__l', 181, 'emitType', 1, 'vueId', 2], [], e, s, gg)
                _(cEDB, o6EB)
                oFDB.wxXCkey = 1
                oFDB.wxXCkey = 3
                oFDB.wxXCkey = 3
                lGDB.wxXCkey = 1
                lGDB.wxXCkey = 3
                _(oDDB, cEDB)
            }
            oDDB.wxXCkey = 1
            oDDB.wxXCkey = 3
            return r
        }
        e_[x[0]] = {
            f: m0,
            j: [],
            i: [],
            ti: [],
            ic: []
        }
        if (path && e_[path]) {
            return function(env, dd, global) {
                $gwxc = 0;
                var root = {
                    "tag": "wx-page"
                };
                root.children = [];
                g = "$gwx_XC_99";
                var main = e_[path].f
                if (typeof global === "undefined") global = {};
                global.f = $gdc(f_[path], "", 1);
                try {
                    main(env, {}, root, global);
                    _tsd(root)
                } catch (err) {
                    console.log(err)
                };
                g = "";
                return root;
            }
        }
    }
}(__g.a, __g.b, __g.c, __g.d, __g.e, __g.f, __g.g, __g.h, __g.i, __g.j, __g.k, __g.l, __g.m, __g.n, __g.o, __g.p, __g.q, __g.r, __g.s, __g.t, __g.u, __g.v, __g.w, __g.x, __g.y, __g.z, __g.A, __g.B, __g.C, __g.D, __g.E, __g.F, __g.G, __g.H, __g.I, __g.J, __g.K, __g.L, __g.M, __g.N, __g.O, __g.P, __g.Q, __g.R, __g.S, __g.T, __g.U, __g.V, __g.W, __g.X, __g.Y, __g.Z, __g.aa);
if (__vd_version_info__.delayedGwx || false) $gwx_XC_99();
if (__vd_version_info__.delayedGwx) __wxAppCode__['pages/index.wxml'] = [$gwx_XC_99, './pages/index.wxml'];
else __wxAppCode__['pages/index.wxml'] = $gwx_XC_99('./pages/index.wxml');;
__wxRoute = "pages/index";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/index.js";
define("pages/index.js", function(require, module, exports, window, document, frames, self, location, navigator, localStorage, history, Caches, screen, alert, confirm, prompt, XMLHttpRequest, WebSocket, Reporter, webkit, WeixinJSCore) {
    (global.webpackJsonp = global.webpackJsonp || []).push([
        ["pages/index"], {
            "09b0": function(t, e, n) {},
            "14fc": function(t, e, n) {
                "use strict";
                n.r(e);
                var a = n("5805"),
                    i = n.n(a);
                for (var o in a)["default"].indexOf(o) < 0 && function(t) {
                    n.d(e, t, (function() {
                        return a[t]
                    }))
                }(o);
                e.default = i.a
            },
            "54db": function(t, e, n) {
                "use strict";
                (function(t, e) {
                    var a = n("47a9");
                    n("8f74"), a(n("3240"));
                    var i = a(n("b235"));
                    t.__webpack_require_UNI_MP_PLUGIN__ = n, e(i.default)
                }).call(this, n("3223").default, n("df3c").createPage)
            },
            5805: function(t, e, n) {
                "use strict";
                (function(t, a) {
                    var i = n("47a9");
                    Object.defineProperty(e, "__esModule", {
                        value: !0
                    }), e.default = void 0;
                    var o = i(n("7eb4")),
                        s = i(n("ee10")),
                        r = n("2eff"),
                        c = i(n("e6fb")),
                        u = i(n("f462")),
                        f = n("62a4"),
                        h = n("7203"),
                        l = (n("0afd"), {
                            components: {
                                tabBar: function() {
                                    n.e("components/base/tab-bar").then(function() {
                                        return resolve(n("bd10"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                fixedContent: function() {
                                    Promise.all([n.e("common/vendor"), n.e("components/base/content")]).then(function() {
                                        return resolve(n("579b"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                kefu: function() {
                                    n.e("components/common/kefu").then(function() {
                                        return resolve(n("b11c"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                actionMenu: function() {
                                    n.e("components/common/action-menu").then(function() {
                                        return resolve(n("bacf"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                xkShare: function() {
                                    Promise.all([n.e("common/vendor"), n.e("components/share/share")]).then(function() {
                                        return resolve(n("d066"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                addMp: function() {
                                    n.e("components/mp/add").then(function() {
                                        return resolve(n("dc25"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                tabCourse: function() {
                                    n.e("components/nav/course").then(function() {
                                        return resolve(n("10ac"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                tabQbank: function() {
                                    Promise.all([n.e("common/vendor"), n.e("components/nav/qbank")]).then(function() {
                                        return resolve(n("3a2d"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                tabClass: function() {
                                    Promise.all([n.e("common/vendor"), n.e("components/nav/class")]).then(function() {
                                        return resolve(n("20d4"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                tabCamp: function() {
                                    n.e("components/nav/camp").then(function() {
                                        return resolve(n("07c4"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                tabFilepacket: function() {
                                    Promise.all([n.e("common/vendor"), n.e("components/nav/filepacket")]).then(function() {
                                        return resolve(n("985d"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                tabWord: function() {
                                    Promise.all([n.e("common/vendor"), n.e("components/nav/word")]).then(function() {
                                        return resolve(n("bf45"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                tabArticle: function() {
                                    Promise.all([n.e("common/vendor"), n.e("components/nav/find")]).then(function() {
                                        return resolve(n("a6ea"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                tabCommunity: function() {
                                    n.e("components/nav/community").then(function() {
                                        return resolve(n("fc91"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                tabShop: function() {
                                    n.e("components/nav/shop").then(function() {
                                        return resolve(n("d9b3"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                tabUser: function() {
                                    Promise.all([n.e("common/vendor"), n.e("components/nav/user")]).then(function() {
                                        return resolve(n("7d01"))
                                    }.bind(null, n)).catch(n.oe)
                                }
                            },
                            data: function() {
                                return {
                                    navBarHeight: this.$statusBar + this.$customBar,
                                    custom: this.$custom,
                                    adminConfig: {},
                                    platformReady: !1,
                                    pageReady: !1,
                                    isFail: !1,
                                    urlQbankId: "",
                                    tabType: "",
                                    tabBarData: {},
                                    tabBarList: [],
                                    foldList: [],
                                    showFoldList: !1,
                                    homeLayout: [],
                                    courseLayout: [],
                                    homeWepage: {},
                                    qbankLayout: [],
                                    classLayout: [],
                                    shopLayout: [],
                                    campLayout: [],
                                    activeTabIndex: 0,
                                    userNavBarOpacity: 0,
                                    qbankNavBarOpacity: 0,
                                    shareLink: "/pages/index",
                                    inviterId: 0,
                                    shareMenuPushData: [],
                                    findTabActiveNav: 0,
                                    findTabNavArr: [],
                                    fileColor: "#FFF2E3",
                                    fileBgImg: "",
                                    fileBgImgLink: ""
                                }
                            },
                            computed: {
                                titleAlign: function() {
                                    return "baixiaodan" == this.Customer ? "left" : "center"
                                },
                                useCart: function() {
                                    return this.$store.state.useCart
                                },
                                pageType: function() {
                                    return this.tabBarList[this.activeTabIndex] && this.tabBarList[this.activeTabIndex].type ? this.tabBarList[this.activeTabIndex].type : ""
                                },
                                multipleQbank: function() {
                                    var t = this.tabBarList.find((function(t) {
                                        return "qbank" == t.type
                                    }));
                                    return t && t.multipleQbank || {}
                                },
                                findTabActiveNavType: function() {
                                    try {
                                        return this.findTabNavArr[this.findTabActiveNav].type
                                    } catch (t) {
                                        return ""
                                    }
                                },
                                navBarOpacity: function() {
                                    return "user" == this.pageType ? this.userNavBarOpacity : "qbank" == this.pageType ? this.qbankNavBarOpacity : 1
                                },
                                addMpLeft: function() {
                                    return this.custom ? Math.round(this.custom.left + this.custom.width / 4) : 300
                                },
                                shareInfo: function() {
                                    if (!this.tabBarList[this.activeTabIndex]) return {};
                                    var t = this.tabBarList[this.activeTabIndex].shareImage;
                                    return t = t ? this.formatImageUrl(t, 750, 600, 1) : "", {
                                        title: this.tabBarList[this.activeTabIndex].shareTitle || "分享标题请在后台设置",
                                        summary: this.tabBarList[this.activeTabIndex].shareSummary || "分享摘要请在后台设置",
                                        link: this.shareLink,
                                        imageUrl: t
                                    }
                                },
                                newPointNum: function() {
                                    return this.$store.state.newPointNum || 0
                                },
                                cartGoodsNum: function() {
                                    return this.$store.state.cartGoods.length
                                }
                            },
                            onLoad: function(e) {
                                console.log("page onload"), this.options = e, this.inviterId = e.inviterId || 0, e.wordSetId && t.setStorageSync("options.wordSetId", e.wordSetId), this.initPage()
                            },
                            onShow: function() {
                                var e = this;
                                if ((0, r._getAdminConfig)().then((function(n) {
                                        var a = !1;
                                        if (!n.showMpInPc) {
                                            var i = t.getSystemInfoSync().platform;
                                            "mac" != i && "windows" != i || (a = !0)
                                        }
                                        a ? (e.platformReady = !1, t.reLaunch({
                                            url: "/pages/forbidden"
                                        })) : e.platformReady = !0
                                    })), (0, f.getToken)()) {
                                    var n = this.$refs.login;
                                    n && n.showDialog && n.close()
                                }
                                "user" != this.tabType && (this.$store.dispatch("getNewNotice"), this.$store.dispatch("getNewPoint")), this.pageType && this.emitTabShow(this.pageType)
                            },
                            onReady: function() {
                                setTimeout((function() {
                                    t.setNavigationBarColor({
                                        frontColor: "#ffffff",
                                        backgroundColor: "#000000"
                                    })
                                }), 100)
                            },
                            onShareAppMessage: function() {
                                return {
                                    title: this.shareInfo.title,
                                    path: this.shareInfo.link,
                                    imageUrl: this.shareInfo.imageUrl
                                }
                            },
                            onShareTimeline: function() {
                                return {
                                    title: this.shareInfo.title,
                                    query: this.shareInfo.link.split("?")[1] || "",
                                    imageUrl: this.shareInfo.imageUrl
                                }
                            },
                            methods: {
                                addEventListener: function(t, e) {
                                    if ("switchTab" == t) {
                                        var n = e.tabType || "";
                                        if (n) {
                                            var a = this.tabBarList.findIndex((function(t) {
                                                return t.type == n
                                            }));
                                            if (a > -1) this.changeActiveTab(a);
                                            else {
                                                var i = this.foldList.find((function(t) {
                                                    return t.type == n
                                                }));
                                                i ? this.goFoldTab(i.type) : this.changeActiveTab(0)
                                            }
                                        } else this.changeActiveTab(0)
                                    }
                                },
                                initPage: function() {
                                    var t = this.options;
                                    this.tabType = t.tabType, this.urlQbankId = t.qbankId || "", this.getAppConfig()
                                },
                                getAppConfig: function() {
                                    var t = this;
                                    return (0, s.default)(o.default.mark((function e() {
                                        var n, a, i, s, c, u;
                                        return o.default.wrap((function(e) {
                                            for (;;) switch (e.prev = e.next) {
                                                case 0:
                                                    return t.$api.showLoading(), e.next = 3, (0, r._getAppConfig)();
                                                case 3:
                                                    if (n = e.sent, t.$api.hideLoading(), 0 !== n.errno) {
                                                        e.next = 45;
                                                        break
                                                    }
                                                    if (n = n.data, a = n.tabBar.list, i = [], a.forEach((function(e) {
                                                            e.startLoad = 0;
                                                            var n = e.platform;
                                                            switch (n ? n.mp && i.push(e) : i.push(e), e.type) {
                                                                case "find":
                                                                    var a = e.pageList || ["article"],
                                                                        o = [];
                                                                    if (a.forEach((function(t) {
                                                                            "article" == t ? o.push({
                                                                                title: "资讯",
                                                                                type: "article"
                                                                            }) : "community" == t && o.push({
                                                                                title: "社区",
                                                                                type: "community"
                                                                            })
                                                                        })), t.findTabNavArr = o, "find" == t.tabType && t.options.subType) {
                                                                        var s = o.findIndex((function(e) {
                                                                            return e.type == t.options.subType
                                                                        }));
                                                                        s > -1 && (t.findTabActiveNav = s)
                                                                    }
                                                                    break;
                                                                case "filepacket":
                                                                    e.lightNavBar = !0, e.title = " ", e.fileColor && (t.fileColor = e.fileColor), t.fileBgImg = e.bgImg || "", t.fileBgImgLink = e.bgImgLink || "";
                                                                case "qbank":
                                                                case "user":
                                                                case "shop":
                                                                    e.lightNavBar = !0
                                                            }
                                                        })), t.foldList = i.filter((function(t) {
                                                            return t.fold
                                                        })), t.foldList.length && ((s = i.filter((function(t) {
                                                            return !t.fold
                                                        }))).push({
                                                            iconClass: "addition",
                                                            text: "更多",
                                                            title: "更多功能",
                                                            type: "more"
                                                        }), i = s), n.tabBar.list = i, t.tabBarData = n.tabBar, 0 != i.length) {
                                                        e.next = 17;
                                                        break
                                                    }
                                                    return t.pageReady = !0, e.abrupt("return", t.$api.alert("未设置可显示的底部导航"));
                                                case 17:
                                                    if (!t.tabType) {
                                                        e.next = 26;
                                                        break
                                                    }
                                                    if (!((c = i.findIndex((function(e) {
                                                            return e.type == t.tabType
                                                        }))) > -1)) {
                                                        e.next = 23;
                                                        break
                                                    }
                                                    t.activeTabIndex = c, e.next = 26;
                                                    break;
                                                case 23:
                                                    if (!["qbank", "class", "find", "camp", "shop", "word", "filepacket"].includes(t.tabType)) {
                                                        e.next = 26;
                                                        break
                                                    }
                                                    return t.goFoldTab(t.tabType, "reLaunch"), e.abrupt("return");
                                                case 26:
                                                    return i[t.activeTabIndex].startLoad = 1, t.tabBarList = i, t.setShareLink(), u = [], n.courseLayout.forEach((function(t) {
                                                        "coupon-list" == t.type && (u = u.concat(t.idArr))
                                                    })), t.couponIds = u, t.getCouponList(), t.homeLayout = n.homeLayout || [], t.courseLayout = n.courseLayout || [], t.homeWepage = n.wepage || {}, t.qbankLayout = n.qbankLayout || [], t.classLayout = n.classLayout || [], t.shopLayout = n.shopLayout || [], t.campLayout = n.campLayout || [], t.pageReady = !0, e.next = 43, (0, r._getAdminConfig)();
                                                case 43:
                                                    t.adminConfig = e.sent, t.$nextTick((function() {
                                                        if (t.emitTabShow(t.pageType), t.$refs.qbank) {
                                                            var e = t.$refs.qbank[0];
                                                            e && e.recordInviterId && t.inviterId && t.urlQbankId && e.recordInviterId(t.inviterId, t.urlQbankId)
                                                        }
                                                    }));
                                                case 45:
                                                case "end":
                                                    return e.stop()
                                            }
                                        }), e)
                                    })))()
                                },
                                closeFoldList: function() {
                                    this.showFoldList = !1
                                },
                                goFoldTab: function(t, e) {
                                    if (this.closeFoldList(), "qbank" == t) {
                                        var n = {};
                                        return this.options.qbankId && (n.id = this.options.qbankId), void this.$api.openWin({
                                            type: e,
                                            url: "/pages/qbank/index",
                                            params: n
                                        })
                                    }
                                    "class" != t ? "word" != t ? "find" != t ? "camp" != t ? "filepacket" != t ? "shop" != t || this.$api.openWin({
                                        type: e,
                                        url: "/pages/shop/index"
                                    }) : this.$api.openWin({
                                        type: e,
                                        url: "/pages/filepacket/index"
                                    }) : this.$api.openWin({
                                        type: e,
                                        url: "/pages/camp/list"
                                    }) : this.$api.openWin({
                                        type: e,
                                        url: "/pages/article/list"
                                    }) : this.$api.openWin({
                                        type: e,
                                        url: "/pages/word/index"
                                    }) : this.$api.openWin({
                                        type: e,
                                        url: "/pages/class/home"
                                    })
                                },
                                changeActiveTab: function(t) {
                                    var e = this;
                                    this.$store.commit("setDataPro", {
                                        mpAdShow: !1
                                    }), setTimeout((function() {
                                        e.$store.commit("setDataPro", {
                                            mpAdShow: !0
                                        })
                                    }), 100), this.closeFoldList();
                                    var n = this.tabBarList[t].type;
                                    "more" != n ? ("user" == n ? a.hideShareMenu() : a.showShareMenu({
                                        menus: ["shareAppMessage", "shareTimeline"]
                                    }), this.activeTabIndex = t, 0 == this.tabBarList[t].startLoad && this.$set(this.tabBarList[t], "startLoad", 1), this.setShareLink(), this.$nextTick((function() {
                                        e.emitTabShow(n)
                                    }))) : this.showFoldList = !this.showFoldList
                                },
                                setShareLink: function() {
                                    var t = this;
                                    this.$nextTick((function() {
                                        t.shareLink = "/pages/index?tabType=" + t.pageType, "find" == t.pageType && t.findTabActiveNavType && (t.shareLink += "&subType=".concat(t.findTabActiveNavType)), t.$store.state.userInfo && t.$store.state.userInfo.resellerId && (t.shareLink += "&resellerId=".concat(t.$store.state.userInfo.resellerId)), "user" == t.pageType && (t.shareLink = "/pages/index"), console.log("shareLink: ".concat(t.shareLink))
                                    }))
                                },
                                setQbankShareLink: function(t) {
                                    t.id && (this.shareLink = "/pages/index?tabType=qbank&qbankId=" + t.id, this.$store.state.userInfo && this.$store.state.userInfo.resellerId && (this.shareLink += "&resellerId=".concat(this.$store.state.userInfo.resellerId)))
                                },
                                emitTabShow: function(t) {
                                    if ("qbank" == t) {
                                        if (this.$refs.qbank) {
                                            var e = this.$refs.qbank[0];
                                            e && e.onQbankShow && e.onQbankShow()
                                        }
                                    } else if ("user" == t) {
                                        if (this.$refs.user) {
                                            var n = this.$refs.user[0];
                                            n && n.onUserShow && n.onUserShow(this.options)
                                        }
                                    } else if ("find" == t) {
                                        if (this.$refs.community) {
                                            var a = this.$refs.community[0];
                                            a && a.onMyselfShow && a.onMyselfShow()
                                        }
                                    } else if ("word" == t && this.$refs.word) {
                                        var i = this.$refs.word[0];
                                        i && i.onMyselfShow && i.onMyselfShow()
                                    }
                                },
                                onRequestFail: function() {
                                    this.pageReady = !1, this.isFail = !0
                                },
                                onCampReachBottom: function() {
                                    if (this.$refs.camp) {
                                        var t = this.$refs.camp[0];
                                        t && t.onMyselfReachBottom && t.onMyselfReachBottom()
                                    }
                                },
                                onFileReachBottom: function() {
                                    if (this.$refs.filepacket) {
                                        var t = this.$refs.filepacket[0];
                                        t && t.onMyselfReachBottom && t.onMyselfReachBottom()
                                    }
                                },
                                onCommunityReachBottom: function() {
                                    if (this.$refs.community) {
                                        var t = this.$refs.community[0];
                                        t && t.onMyselfReachBottom && t.onMyselfReachBottom()
                                    }
                                },
                                onCommunityScroll: function(t) {
                                    if (this.$refs.community) {
                                        var e = this.$refs.community[0];
                                        e && e.onMyselfScroll && e.onMyselfScroll(t.detail.scrollTop)
                                    }
                                },
                                userPageScroll: function(t) {
                                    this.userNavBarOpacity != t && (this.userNavBarOpacity = t)
                                },
                                qbankPageScroll: function(t) {
                                    this.qbankNavBarOpacity != t && (this.qbankNavBarOpacity = t)
                                },
                                goCourseCate: function() {
                                    this.$api.openWin({
                                        url: "/pages/cate/list",
                                        params: {
                                            type: "course"
                                        }
                                    })
                                },
                                goCourseSearch: function() {
                                    this.$api.openWin({
                                        url: "/pages/system/search?type=course"
                                    })
                                },
                                goWordSearch: function() {
                                    this.$api.openWin({
                                        url: "/pages/word/search"
                                    })
                                },
                                goShopSearch: function() {
                                    this.$api.openWin({
                                        url: "/pages/system/search?type=shop"
                                    })
                                },
                                goCart: function() {
                                    (0, f.getToken)() ? t.navigateTo({
                                        url: "/pages/sale/cart"
                                    }): this.login()
                                },
                                goPoint: function() {
                                    (0, f.getToken)() ? this.$api.openWin({
                                        url: "/pages/user/point/task"
                                    }): this.login()
                                },
                                changeFindSubType: function(t) {
                                    this.findTabActiveNav = t, this.setShareLink()
                                },
                                openMoreAction: function() {
                                    this.$refs.actionMenu.open()
                                },
                                selectActionItem: function(e) {
                                    "share" == e ? this.sharePage() : "learn" == e ? this.goLastLearn() : "scan" == e ? (0, h.scanCode)() : "about" == e && t.navigateTo({
                                        url: "/pages/user/about"
                                    })
                                },
                                sharePage: function() {
                                    this.$refs.shareMenu.open()
                                },
                                goLastLearn: function() {
                                    t.navigateTo({
                                        url: "/pages/course/record"
                                    })
                                },
                                goQuestionSearch: function() {
                                    this.$refs.qbank[0].goQuestionSearch()
                                },
                                getCouponList: function() {
                                    var t = this;
                                    "course" == this.pageType && (this.$readLocalConfig || this.couponIds && this.couponIds.length && (0, c.default)({
                                        method: "GET",
                                        path: "/api/coupon/get_coupon_list",
                                        params: {
                                            getTakeState: 1,
                                            ids: JSON.stringify(this.couponIds)
                                        },
                                        checkToken: 1
                                    }).then((function(e) {
                                        if (0 === e.errno) {
                                            var n = e.data;
                                            t.courseLayout.forEach((function(t) {
                                                if ("coupon-list" == t.type) {
                                                    var e = n.filter((function(e) {
                                                        return t.idArr.indexOf(e.id) > -1
                                                    }));
                                                    e.length && (t.data = e)
                                                }
                                            }))
                                        } else e.errno
                                    })))
                                },
                                login: function() {
                                    var t = this;
                                    (0, u.default)({
                                        component: !0,
                                        openComponent: function(e) {
                                            t.$refs.login.show()
                                        },
                                        success: function() {
                                            t.loginSuccess()
                                        }
                                    })
                                },
                                goLogin: function() {
                                    this.login()
                                },
                                loginSuccess: function() {
                                    var t = this;
                                    this.getCouponList(), this.tabBarList.forEach((function(e) {
                                        var n = e.type;
                                        if ("find" == n && (n = "community"), t.$refs[n]) {
                                            var a = t.$refs[n][0];
                                            a && a.loginSuccess && a.loginSuccess()
                                        }
                                    }))
                                },
                                formatImageUrl: function(t, e, n, a) {
                                    return this.$api.formatImageUrl(t, e, n, a)
                                }
                            }
                        });
                    e.default = l
                }).call(this, n("df3c").default, n("3223").default)
            },
            9087: function(t, e, n) {
                "use strict";
                var a = n("09b0");
                n.n(a).a
            },
            b235: function(t, e, n) {
                "use strict";
                n.r(e);
                var a = n("d917"),
                    i = n("14fc");
                for (var o in i)["default"].indexOf(o) < 0 && function(t) {
                    n.d(e, t, (function() {
                        return i[t]
                    }))
                }(o);
                n("9087");
                var s = n("828b"),
                    r = Object(s.a)(i.default, a.b, a.c, !1, null, null, null, !1, a.a, void 0);
                e.default = r.exports
            },
            d917: function(t, e, n) {
                "use strict";
                n.d(e, "b", (function() {
                    return a
                })), n.d(e, "c", (function() {
                    return i
                })), n.d(e, "a", (function() {}));
                var a = function() {
                        var t = this,
                            e = (t.$createElement, t._self._c, t.platformReady && t.pageReady ? t.tabBarList.length : null),
                            n = t.platformReady && t.pageReady && e && "course" != t.pageType && "word" != t.pageType && "find" == t.pageType ? t.findTabNavArr.length : null,
                            a = t.platformReady && "course" == t.pageType ? {
                                top: t.navBarHeight + "px",
                                right: "5px"
                            } : null;
                        t.$mp.data = Object.assign({}, {
                            $root: {
                                g0: e,
                                g1: n,
                                a0: a
                            }
                        })
                    },
                    i = []
            }
        },
        [
            ["54db", "common/runtime", "common/vendor"]
        ]
    ]);
}, {
    isPage: true,
    isComponent: true,
    currentFile: 'pages/index.js'
});
require("pages/index.js");