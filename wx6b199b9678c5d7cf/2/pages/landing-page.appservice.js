$gwx_XC_100 = function(_, _v, _n, _p, _s, _wp, _wl, $gwn, $gwl, $gwh, wh, $gstack, $gwrt, gra, grb, TestTest, wfor, _ca, _da, _r, _rz, _o, _oz, _1, _1z, _2, _2z, _m, _mz, nv_getDate, nv_getRegExp, nv_console, nv_parseInt, nv_parseFloat, nv_isNaN, nv_isFinite, nv_decodeURI, nv_decodeURIComponent, nv_encodeURI, nv_encodeURIComponent, $gdc, nv_JSON, _af, _gv, _ai, _grp, _gd, _gapi, $ixc, _ic, _w, _ev, _tsd) {
    return function(path, global) {
        if (typeof global === 'undefined') {
            if (typeof __GWX_GLOBAL__ === 'undefined') global = {};
            else global = __GWX_GLOBAL__;
        }
        if (typeof __WXML_GLOBAL__ === 'undefined') {
            __WXML_GLOBAL__ = {};
        }
        __WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
        var e_ = {}
        if (typeof(global.entrys) === 'undefined') global.entrys = {};
        e_ = global.entrys;
        var d_ = {}
        if (typeof(global.defines) === 'undefined') global.defines = {};
        d_ = global.defines;
        var f_ = {}
        if (typeof(global.modules) === 'undefined') global.modules = {};
        f_ = global.modules || {};
        var p_ = {}
        __WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
        __WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
        __WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
        var z = __WXML_GLOBAL__.ops_set.$gwx_XC_100 || [];

        function gz$gwx_XC_100_1() {
            if (__WXML_GLOBAL__.ops_cached.$gwx_XC_100_1) return __WXML_GLOBAL__.ops_cached.$gwx_XC_100_1
            __WXML_GLOBAL__.ops_cached.$gwx_XC_100_1 = [];
            (function(z) {
                var a = 11;

                function Z(ops) {
                    z.push(ops)
                }
            })(__WXML_GLOBAL__.ops_cached.$gwx_XC_100_1);
            return __WXML_GLOBAL__.ops_cached.$gwx_XC_100_1
        }
        __WXML_GLOBAL__.ops_set.$gwx_XC_100 = z;
        __WXML_GLOBAL__.ops_init.$gwx_XC_100 = true;
        var x = ['./pages/landing-page.wxml'];
        d_[x[0]] = {}
        var m0 = function(e, s, r, gg) {
            var z = gz$gwx_XC_100_1()
            return r
        }
        e_[x[0]] = {
            f: m0,
            j: [],
            i: [],
            ti: [],
            ic: []
        }
        if (path && e_[path]) {
            return function(env, dd, global) {
                $gwxc = 0;
                var root = {
                    "tag": "wx-page"
                };
                root.children = [];
                g = "$gwx_XC_100";
                var main = e_[path].f
                if (typeof global === "undefined") global = {};
                global.f = $gdc(f_[path], "", 1);
                try {
                    main(env, {}, root, global);
                    _tsd(root)
                } catch (err) {
                    console.log(err)
                };
                g = "";
                return root;
            }
        }
    }
}(__g.a, __g.b, __g.c, __g.d, __g.e, __g.f, __g.g, __g.h, __g.i, __g.j, __g.k, __g.l, __g.m, __g.n, __g.o, __g.p, __g.q, __g.r, __g.s, __g.t, __g.u, __g.v, __g.w, __g.x, __g.y, __g.z, __g.A, __g.B, __g.C, __g.D, __g.E, __g.F, __g.G, __g.H, __g.I, __g.J, __g.K, __g.L, __g.M, __g.N, __g.O, __g.P, __g.Q, __g.R, __g.S, __g.T, __g.U, __g.V, __g.W, __g.X, __g.Y, __g.Z, __g.aa);
if (__vd_version_info__.delayedGwx || false) $gwx_XC_100();
if (__vd_version_info__.delayedGwx) __wxAppCode__['pages/landing-page.wxml'] = [$gwx_XC_100, './pages/landing-page.wxml'];
else __wxAppCode__['pages/landing-page.wxml'] = $gwx_XC_100('./pages/landing-page.wxml');;
__wxRoute = "pages/landing-page";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/landing-page.js";
define("pages/landing-page.js", function(require, module, exports, window, document, frames, self, location, navigator, localStorage, history, Caches, screen, alert, confirm, prompt, XMLHttpRequest, WebSocket, Reporter, webkit, WeixinJSCore) {
    (global.webpackJsonp = global.webpackJsonp || []).push([
        ["pages/landing-page"], {
            "08d1": function(t, e, n) {
                "use strict";
                n.r(e);
                var o = n("c4db"),
                    i = n("29c5");
                for (var a in i)["default"].indexOf(a) < 0 && function(t) {
                    n.d(e, t, (function() {
                        return i[t]
                    }))
                }(a);
                n("2ccc"), n("5f6e");
                var s = n("828b"),
                    r = Object(s.a)(i.default, o.b, o.c, !1, null, "849fa24a", null, !1, o.a, void 0);
                e.default = r.exports
            },
            "29c5": function(t, e, n) {
                "use strict";
                n.r(e);
                var o = n("800c"),
                    i = n.n(o);
                for (var a in o)["default"].indexOf(a) < 0 && function(t) {
                    n.d(e, t, (function() {
                        return o[t]
                    }))
                }(a);
                e.default = i.a
            },
            "2a19": function(t, e, n) {},
            "2ccc": function(t, e, n) {
                "use strict";
                var o = n("2a19");
                n.n(o).a
            },
            "5f6e": function(t, e, n) {
                "use strict";
                var o = n("7590");
                n.n(o).a
            },
            7590: function(t, e, n) {},
            "800c": function(t, e, n) {
                "use strict";
                (function(t) {
                    var o = n("47a9");
                    Object.defineProperty(e, "__esModule", {
                        value: !0
                    }), e.default = void 0;
                    var i = o(n("7eb4")),
                        a = o(n("ee10")),
                        s = o(n("7ca3")),
                        r = n("2386"),
                        c = n("62a4"),
                        u = n("0938"),
                        d = o(n("f462"));

                    function h(t, e) {
                        var n = Object.keys(t);
                        if (Object.getOwnPropertySymbols) {
                            var o = Object.getOwnPropertySymbols(t);
                            e && (o = o.filter((function(e) {
                                return Object.getOwnPropertyDescriptor(t, e).enumerable
                            }))), n.push.apply(n, o)
                        }
                        return n
                    }

                    function p(t) {
                        for (var e = 1; e < arguments.length; e++) {
                            var n = null != arguments[e] ? arguments[e] : {};
                            e % 2 ? h(Object(n), !0).forEach((function(e) {
                                (0, s.default)(t, e, n[e])
                            })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(n)) : h(Object(n)).forEach((function(e) {
                                Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(n, e))
                            }))
                        }
                        return t
                    }
                    var l = {
                        components: {
                            xkLoadMore: function() {
                                n.e("components/common/load-more").then(function() {
                                    return resolve(n("3dc2"))
                                }.bind(null, n)).catch(n.oe)
                            },
                            payment: function() {
                                Promise.all([n.e("common/vendor"), n.e("components/payment/payment")]).then(function() {
                                    return resolve(n("5d4b"))
                                }.bind(null, n)).catch(n.oe)
                            },
                            xkShare: function() {
                                Promise.all([n.e("common/vendor"), n.e("components/share/share")]).then(function() {
                                    return resolve(n("d066"))
                                }.bind(null, n)).catch(n.oe)
                            },
                            xkModalPro: function() {
                                n.e("components/common/modal-pro").then(function() {
                                    return resolve(n("76ee"))
                                }.bind(null, n)).catch(n.oe)
                            },
                            addGroup: function() {
                                n.e("components/common/add-group").then(function() {
                                    return resolve(n("e22f"))
                                }.bind(null, n)).catch(n.oe)
                            }
                        },
                        data: function() {
                            return {
                                pageId: 0,
                                dataHasBeenLoaded: !1,
                                page: {},
                                pageContent: "",
                                products: [],
                                showProductList: !1,
                                product: {},
                                showQrcode: !1,
                                showPhoneModal: !1,
                                showAssistant: !1,
                                showPayResultModal: !1,
                                showGroup: !1,
                                showSkus: !1,
                                phone: "",
                                captcha: "",
                                verifyCode: "",
                                sending: !1,
                                time: 45,
                                imageCodeUrl: "",
                                showUserPhone: !0
                            }
                        },
                        watch: {
                            phone: function() {
                                if (/^1[3-9][0-9]{9}$/.test(this.phone)) {
                                    var t = (new Date).getTime();
                                    this.imageCodeUrl = "".concat(this.HOST, "/api/v1/captcha/mobile?phone=").concat(this.phone, "&t=").concat(t)
                                } else this.imageCodeUrl = ""
                            },
                            imageCodeUrl: function() {
                                this.captcha = ""
                            }
                        },
                        onLoad: function(t) {
                            this.pageId = t.id || 1, this.shareUserId = t.fromUser || 0, this.autoAction = t.action, this.getDetail()
                        },
                        onShow: function() {},
                        computed: {
                            shareInfo: function() {
                                return this.page.id ? {
                                    title: this.page.shareTitle || this.page.title,
                                    summary: this.page.shareSummary || this.page.description || "点击查看",
                                    link: "/pages/landing-page?id=" + this.page.id,
                                    imageUrl: this.formatImageUrl(this.page.sharePicture, 750, 600, 1)
                                } : {}
                            },
                            userPhone: function() {
                                return this.$store.state.userInfo.phone
                            },
                            productId: function() {
                                return this.product.productId
                            },
                            productType: function() {
                                return this.product.productType
                            },
                            skuId: function() {
                                if (!this.product.skus || !this.product.skus.length) return 0;
                                try {
                                    return this.product.skus[this.product.activeSku].id
                                } catch (t) {
                                    return 0
                                }
                            },
                            timeDiscountId: function() {
                                return this.product && this.product.discountId ? this.product.discountId : 0
                            },
                            allowPayType: function() {
                                if (this.$store.state.adminConfig.payThreeUrl) return ["pay"];
                                try {
                                    return this.product.optional.allowPayType || []
                                } catch (t) {
                                    return ["pay", "key", "password"]
                                }
                            },
                            qrcodeUrl: function() {
                                var t;
                                switch (this.productType) {
                                    case "questions":
                                        t = "pages/index?tabType=qbank%26qbankId=".concat(this.productId);
                                        break;
                                    case "course":
                                        t = "pages/course/index?id=".concat(this.productId);
                                        break;
                                    case "resource":
                                        t = "pages/class/index?id=".concat(this.productId);
                                        break;
                                    default:
                                        t = "pages/index"
                                }
                                return "".concat(this.HOST, "/api/applet/code?path=").concat(t)
                            },
                            productTypeForPlugin: function() {
                                switch (this.productType) {
                                    case "course":
                                        return "course";
                                    case "questions":
                                        return "qBank";
                                    case "resource":
                                        return "resource"
                                }
                            }
                        },
                        filters: {
                            formatPrice: function(t) {
                                return t ? t.toFixed(2) : ""
                            },
                            dateFormat: function(t, e) {
                                return (0, u.dateFormat)(t, e || "yyyy-MM-dd")
                            },
                            formatGoodsType: function(t) {
                                switch (t) {
                                    case "resource":
                                        return "班级";
                                    case "questions":
                                        return "题库";
                                    case "course":
                                        return "课程";
                                    default:
                                        return ""
                                }
                            }
                        },
                        methods: {
                            addEventListener: function(t, e) {
                                "landingpage-paymented" == t && this.paySuccess({
                                    tradeNum: e.tradeNum
                                })
                            },
                            getDetail: function() {
                                var t = this;
                                this.$api.showLoading(), this.$http({
                                    url: "/api/landing_page/get_detail",
                                    data: {
                                        id: this.pageId
                                    }
                                }).then((function(e) {
                                    if (t.$api.hideLoading(), 0 === e.errno) {
                                        if (t.dataHasBeenLoaded = !0, t.page = e.data, t.page.title, t.pageContent = t.page.content, t.page.goods) {
                                            var n = [];
                                            t.page.goods.forEach((function(t) {
                                                if (t.activity && t.activity.discount) {
                                                    var e = +new Date,
                                                        o = t.activity.discount.startTime,
                                                        i = t.activity.discount.endTime;
                                                    if (e > o && e < i) {
                                                        t.discountId = t.activity.discount.id;
                                                        var a = t.activity.discount.discount,
                                                            s = t.activity.discount.reduce,
                                                            r = t.price;
                                                        t.marketPrice = t.price, a ? (t.discountTag = "限时折扣", r = r * a / 10) : s && (t.discountTag = "限时立减", r = Math.max(r - s, 0)), t.price = r;
                                                        var c = t.skus || t.courseSku;
                                                        c.forEach((function(t) {
                                                            a ? t.price = t.price * a / 10 : s && (t.price = Math.max(t.price - s, 0))
                                                        })), t.skus = c
                                                    }
                                                }
                                                var u = p(p({}, t), {}, {
                                                    productId: t.goodsId,
                                                    productType: t.goodsType,
                                                    isHave: t.isHave || t.isAuthorized,
                                                    skus: t.skus || t.courseSku || [],
                                                    activeSku: 0
                                                });
                                                "qBank" == u.productType && (u.productType = "questions"), n.push(u)
                                            })), t.products = n
                                        }
                                        if (t.dealAssistant(), t.shareUserId && t.recordShare(t.shareUserId), "getClue" == t.page.conversionType && t.page.isHave && t.getClue(), t.autoAction && t[t.autoAction]) {
                                            t[t.autoAction]();
                                            var o = document.title,
                                                i = location.href,
                                                a = {
                                                    title: o,
                                                    url: i = (i = i.replace("?action=", "?action2=")).replace("&action=", "&action2=")
                                                };
                                            top.window.history.replaceState(a, o, i)
                                        }
                                    } else 100143 == e.errno && t.getDetail()
                                }))
                            },
                            dealAssistant: function() {
                                var e = t.getStorageSync("assistantIndex-".concat(this.page.id));
                                if (!this.page.assistantArr.length) return this.page.assistant = {}, !0;
                                if (this.page.assistantArr[e]) return this.page.assistant = this.page.assistantArr[e], !0;
                                var n = this.page.assistantArr,
                                    o = Math.floor(Math.random() * n.length);
                                this.page.assistant = n[o], t.setStorageSync("assistantIndex-".concat(this.page.id), o)
                            },
                            login: function() {
                                var t = this,
                                    e = window.location.href;
                                e.indexOf("?") > -1 ? e += "&action=handleBtn" : e += "?action=handleBtn", (0, d.default)({
                                    returnUrl: e,
                                    component: !0,
                                    openComponent: function() {
                                        t.$refs.login.show()
                                    }
                                })
                            },
                            loginSuccess: function() {
                                this.getDetail()
                            },
                            recordShare: function(t) {
                                t != this.$store.state.userInfo.id && this.$http({
                                    method: "POST",
                                    url: "/api/landing_page/record",
                                    data: {
                                        landingpageId: this.pageId,
                                        type: "share",
                                        shareUserId: t
                                    },
                                    token: 1
                                }).then((function(t) {
                                    t.errno
                                }))
                            },
                            recordPay: function(t) {
                                this.$http({
                                    method: "POST",
                                    url: "/api/landing_page/record",
                                    data: {
                                        landingpageId: this.pageId,
                                        type: "payment",
                                        tradeNum: t
                                    },
                                    token: 1
                                }).then((function(t) {
                                    t.errno
                                }))
                            },
                            recordCollect: function(t) {
                                this.$http({
                                    method: "POST",
                                    url: "/api/landing_page/record",
                                    data: {
                                        landingpageId: this.pageId,
                                        type: "collect",
                                        collectDataId: t
                                    },
                                    token: 1
                                }).then((function(t) {
                                    t.errno
                                }))
                            },
                            recordPhone: function() {
                                var e, n = this;
                                if (11 != (e = this.userPhone && this.showUserPhone ? this.userPhone : this.phone).length) return this.$api.toast("请正确输入手机号");
                                this.$http({
                                    method: "POST",
                                    url: "/api/landing_page/record",
                                    data: {
                                        landingpageId: this.pageId,
                                        type: "phone",
                                        phone: e
                                    },
                                    token: 1
                                }).then((function(e) {
                                    0 === e.errno && (t.setStorageSync("landingpage-phone-".concat(n.pageId), 1), n.$api.toast("提交成功"), n.showPhoneModal = !1)
                                }))
                            },
                            handleBtn: function() {
                                var t = this;
                                (0, c.getToken)() ? "payOpen" == this.page.conversionType ? this.openProductModal() : "getClue" == this.page.conversionType && (1 != this.page.getClueTimeSwitch || this.page.isHave ? this.getClue() : (this.product = {
                                    productId: this.page.id,
                                    productType: "landingpage",
                                    optional: {
                                        allowPayType: ["pay"]
                                    }
                                }, this.$nextTick((function() {
                                    t.$refs.payment.open({
                                        success: function(e) {
                                            t.page.isHave = !0, t.getClue(), e.tradeNum && t.recordPay(e.tradeNum)
                                        }
                                    })
                                })))): this.login()
                            },
                            openProductModal: function() {
                                this.showProductList = !0
                            },
                            closeProductModal: function() {
                                this.showProductList = !1, this.showSkus = !1
                            },
                            onProductModalTouchStart: function(t) {
                                this.showSkus && (this.startx = t.touches[0].pageX, this.starty = t.touches[0].pageY)
                            },
                            onProductModalTouchEnd: function(t) {
                                var e, n;
                                this.showSkus && (e = t.changedTouches[0].pageX, n = t.changedTouches[0].pageY, 4 == function(t, e, n, o) {
                                    var i = n - t,
                                        a = o - e,
                                        s = 0;
                                    if (Math.abs(i) < 2 && Math.abs(a) < 2) return s;
                                    var r = function(t, e) {
                                        return 180 * Math.atan2(e, t) / Math.PI
                                    }(i, a);
                                    return r >= -135 && r <= -45 ? s = 1 : r > 45 && r < 135 ? s = 2 : r >= 135 && r <= 180 || r >= -180 && r < -135 ? s = 3 : r >= -45 && r <= 45 && (s = 4), s
                                }(this.startx, this.starty, e, n) && (this.showSkus = !1))
                            },
                            selectProduct: function(t) {
                                var e = this;
                                return (0, a.default)(i.default.mark((function n() {
                                    var o;
                                    return i.default.wrap((function(n) {
                                        for (;;) switch (n.prev = n.next) {
                                            case 0:
                                                if (!e.showSkus) {
                                                    n.next = 3;
                                                    break
                                                }
                                                return e.showSkus = !1, n.abrupt("return");
                                            case 3:
                                                if (e.product = t, !t.isHave) {
                                                    n.next = 7;
                                                    break
                                                }
                                                return e.paySuccess(), n.abrupt("return");
                                            case 7:
                                                if (o = !1, 1 != e.page.goodsTipSwitch) {
                                                    n.next = 12;
                                                    break
                                                }
                                                return n.next = 11, e.getInfoCollection();
                                            case 11:
                                                o = n.sent;
                                            case 12:
                                                if (!o) {
                                                    n.next = 14;
                                                    break
                                                }
                                                return n.abrupt("return");
                                            case 14:
                                                e.product.price && (e.product.skus.length > 1 ? e.showSkus = !0 : e.buyProduct());
                                            case 15:
                                            case "end":
                                                return n.stop()
                                        }
                                    }), n)
                                })))()
                            },
                            selectSku: function(t) {
                                this.product.activeSku = t, this.buyProduct()
                            },
                            buyProduct: function() {
                                if (this.checkNeedAddress()) {
                                    var t = {
                                        type: this.productType,
                                        id: this.productId,
                                        landingpageId: this.page.id
                                    };
                                    return this.skuId && (t.sku = this.skuId), void this.$api.openWin({
                                        url: "/pages/buy/index",
                                        params: t
                                    })
                                }
                                this.$refs.payment.open()
                            },
                            getInfoCollection: function() {
                                var t = this;
                                return new Promise((function(e, n) {
                                    t.$api.showLoading(), t.$http({
                                        url: "/api/v1/info_collection/need_sign_info_collection",
                                        data: {
                                            goodsId: t.productId,
                                            goodsType: t.productTypeForPlugin
                                        },
                                        token: 1
                                    }).then((function(n) {
                                        t.$api.hideLoading(), 0 === n.errno && n.data.infocollectionId && "beforePurchase" == n.data.anchor ? (e(!0), t.$refs.modalPro.open({
                                            type: "info_collection",
                                            data: n.data,
                                            skip: function() {
                                                e(!0)
                                            }
                                        })) : e(!1)
                                    }))
                                }))
                            },
                            checkNeedAddress: function() {
                                if ("course" == this.productType) {
                                    var t = this.product.skus,
                                        e = this.product.activeSku || 0;
                                    if (t[e] && 1 == t[e].needDelivery) return !0
                                }
                                return !!this.product.marketStrategy && ("string" == typeof this.product.marketStrategy && (this.product.marketStrategy = JSON.parse(this.product.marketStrategy)), this.product.marketStrategy.gift && this.product.marketStrategy.gift.length)
                            },
                            paySuccess: function() {
                                var t = this,
                                    e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
                                this.closeProductModal();
                                var n = this.products.findIndex((function(e) {
                                    return e.productId == t.productId && e.productType == t.productType
                                }));
                                this.$set(this.products[n], "isHave", !0);
                                var o = !1;
                                1 == this.page.goodsTipSwitch && this.product.optional.hook && (o = !0), this.showGroup = o, this.showPayResultModal = !0, e.tradeNum && this.recordPay(e.tradeNum)
                            },
                            closePayResultModal: function() {
                                this.showPayResultModal = !1, this.showGroup = !1
                            },
                            goProduct: function() {
                                this.closePayResultModal(), "questions" == this.productType ? this.$api.openWin({
                                    url: "/pages/qbank/index",
                                    params: {
                                        id: this.productId
                                    }
                                }) : "course" == this.productType ? this.$api.openWin({
                                    url: "/pages/course/index",
                                    params: {
                                        id: this.productId
                                    }
                                }) : "resource" == this.productType && this.$api.openWin({
                                    url: "/pages/class/index",
                                    params: {
                                        id: this.productId
                                    }
                                })
                            },
                            getClue: function() {
                                switch (this.hasSubmitPhone = 1 == t.getStorageSync("landingpage-phone-".concat(this.pageId)), this.page.clueType) {
                                    case "phone":
                                        this.hasSubmitPhone ? this.$api.toast("您已提交过") : this.showPhoneModal = !0;
                                        break;
                                    case "bindCollect":
                                        this.getClueByInfo();
                                        break;
                                    case "addAssistant":
                                        this.showAssistant = !0
                                }
                            },
                            getClueByInfo: function() {
                                var t = this;
                                this.page.infoCollectionId && (this.$api.showLoading(), this.$http({
                                    url: "/api/v1/info_collection/info_collection",
                                    data: {
                                        id: this.page.infoCollectionId
                                    }
                                }).then((function(e) {
                                    t.$api.hideLoading(), 0 === e.errno && (e.data.skip = 0, t.$refs.modalPro.open({
                                        type: "info_collection",
                                        data: {
                                            infoCollection: e.data,
                                            allowUpdate: 1
                                        },
                                        success: function(e) {
                                            console.log(e), e && e.id && t.recordCollect(e.id)
                                        }
                                    }))
                                })))
                            },
                            copyAssistantWeixin: function() {
                                var e = this;
                                t.setClipboardData({
                                    data: this.page.assistant.weixin,
                                    success: function() {
                                        e.$api.toast("已复制")
                                    }
                                })
                            },
                            getImageCodeKey: function() {
                                var t = (new Date).getTime();
                                this.imageCodeUrl = "".concat(this.HOST, "/api/v1/captcha/mobile?phone=").concat(this.phone, "&t=").concat(t)
                            },
                            getVerifyCode: function() {
                                var e = this,
                                    n = this.phone;
                                if (/^1[3-9][0-9]{9}$/.test(n)) {
                                    if (!this.captcha) return this.$api.toast("请输入图片验证码");
                                    this.sending = !0;
                                    var o = setInterval((function() {
                                            e.time < 1 && (clearInterval(o), e.time = 45, e.sending = !1), e.time = e.time - 1
                                        }), 1e3),
                                        i = {
                                            phone: n,
                                            captcha: this.captcha
                                        };
                                    i.nonce = r.AES.encrypt(t.getAccountInfoSync().miniProgram.appId + (new Date).getTime(), "ixunke").toString(), t.request({
                                        url: this.HOST + "/api/v1/captcha/sms",
                                        data: i,
                                        success: function(n) {
                                            0 == n.data.errno ? (t.showToast({
                                                title: "短信已发送，请注意查收",
                                                icon: "none"
                                            }), e.$store.commit("setDataPro", {
                                                smsSessionKey: n.data.data.sessionId
                                            })) : (e.sending = !1, t.showToast({
                                                title: n.data.errmsg,
                                                icon: "none"
                                            }))
                                        }
                                    })
                                } else console.log(22), t.showToast({
                                    title: "手机号填写错误",
                                    icon: "none"
                                })
                            },
                            submitPhone: function() {
                                var e = this;
                                if (!this.phone) return t.showToast({
                                    title: "请输入手机号",
                                    icon: "none"
                                });
                                if (!/^1[3-9][0-9]{9}$/.test(this.phone)) return this.$api.toast("手机号格式错误");
                                if (!this.verifyCode) return t.showToast({
                                    title: "请输入验证码",
                                    icon: "none"
                                });
                                var n = {
                                    phone: this.phone,
                                    sessionKey: this.smsSessionKey,
                                    verifyCode: this.verifyCode
                                };
                                this.$api.showLoading(), this.$http({
                                    addPlatform: 1,
                                    token: 1,
                                    url: "/api/user/update_phone",
                                    method: "POST",
                                    data: n
                                }).then((function(t) {
                                    e.$api.hideLoading(), 0 == t.errno ? (t.data.needLogin, e.recordPhone(e.phone), e.phone = "", e.verifyCode = "", e.$store.commit("setDataPro", {
                                        smsSessionKey: ""
                                    }), e.time = 45) : e.$api.toast(t.errmsg)
                                }))
                            },
                            formatImageUrl: function(t, e, n, o) {
                                return this.$api.formatImageUrl(t, e, n, o)
                            }
                        }
                    };
                    e.default = l
                }).call(this, n("df3c").default)
            },
            a7af: function(t, e, n) {
                "use strict";
                (function(t, e) {
                    var o = n("47a9");
                    n("8f74"), o(n("3240"));
                    var i = o(n("08d1"));
                    t.__webpack_require_UNI_MP_PLUGIN__ = n, e(i.default)
                }).call(this, n("3223").default, n("df3c").createPage)
            },
            c4db: function(t, e, n) {
                "use strict";
                n.d(e, "b", (function() {
                    return o
                })), n.d(e, "c", (function() {
                    return i
                })), n.d(e, "a", (function() {}));
                var o = function() {
                        this.$createElement;
                        this._self._c
                    },
                    i = []
            }
        },
        [
            ["a7af", "common/runtime", "common/vendor"]
        ]
    ]);
}, {
    isPage: true,
    isComponent: true,
    currentFile: 'pages/landing-page.js'
});
require("pages/landing-page.js");