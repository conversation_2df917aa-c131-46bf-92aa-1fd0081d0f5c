$gwx_XC_101 = function(_, _v, _n, _p, _s, _wp, _wl, $gwn, $gwl, $gwh, wh, $gstack, $gwrt, gra, grb, TestTest, wfor, _ca, _da, _r, _rz, _o, _oz, _1, _1z, _2, _2z, _m, _mz, nv_getDate, nv_getRegExp, nv_console, nv_parseInt, nv_parseFloat, nv_isNaN, nv_isFinite, nv_decodeURI, nv_decodeURIComponent, nv_encodeURI, nv_encodeURIComponent, $gdc, nv_JSON, _af, _gv, _ai, _grp, _gd, _gapi, $ixc, _ic, _w, _ev, _tsd) {
    return function(path, global) {
        if (typeof global === 'undefined') {
            if (typeof __GWX_GLOBAL__ === 'undefined') global = {};
            else global = __GWX_GLOBAL__;
        }
        if (typeof __WXML_GLOBAL__ === 'undefined') {
            __WXML_GLOBAL__ = {};
        }
        __WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
        var e_ = {}
        if (typeof(global.entrys) === 'undefined') global.entrys = {};
        e_ = global.entrys;
        var d_ = {}
        if (typeof(global.defines) === 'undefined') global.defines = {};
        d_ = global.defines;
        var f_ = {}
        if (typeof(global.modules) === 'undefined') global.modules = {};
        f_ = global.modules || {};
        var p_ = {}
        __WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
        __WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
        __WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
        var z = __WXML_GLOBAL__.ops_set.$gwx_XC_101 || [];

        function gz$gwx_XC_101_1() {
            if (__WXML_GLOBAL__.ops_cached.$gwx_XC_101_1) return __WXML_GLOBAL__.ops_cached.$gwx_XC_101_1
            __WXML_GLOBAL__.ops_cached.$gwx_XC_101_1 = [];
            (function(z) {
                var a = 11;

                function Z(ops) {
                    z.push(ops)
                }
                Z([
                    [7],
                    [3, 'errorLink']
                ])
                Z([3, '__l'])
                Z([3, '__e'])
                Z([3, '去首页'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^clickButton']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'goHome']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, '当前访问地址无效，去首页看看吧'])
                Z([3, 'empty2'])
                Z([3, '74725451-1'])
                Z([
                    [7],
                    [3, 'showLoginEmptyComponent']
                ])
                Z(z[1])
                Z(z[2])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^clickButton']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'goLogin']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'login'])
                Z([3, '74725451-2'])
                Z(z[1])
                Z(z[2])
                Z([3, 'vue-ref'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^success']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'loginSuccess']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z(z[12])
                Z([3, '74725451-3'])
            })(__WXML_GLOBAL__.ops_cached.$gwx_XC_101_1);
            return __WXML_GLOBAL__.ops_cached.$gwx_XC_101_1
        }
        __WXML_GLOBAL__.ops_set.$gwx_XC_101 = z;
        __WXML_GLOBAL__.ops_init.$gwx_XC_101 = true;
        var x = ['./pages/redirect.wxml'];
        d_[x[0]] = {}
        var m0 = function(e, s, r, gg) {
            var z = gz$gwx_XC_101_1()
            var f9EB = _n('view')
            var c0EB = _v()
            _(f9EB, c0EB)
            if (_oz(z, 0, e, s, gg)) {
                c0EB.wxVkey = 1
                var oBFB = _mz(z, 'xk-empty', ['bind:__l', 1, 'bind:clickButton', 1, 'button', 2, 'data-event-opts', 3, 'text', 4, 'type', 5, 'vueId', 6], [], e, s, gg)
                _(c0EB, oBFB)
            }
            var hAFB = _v()
            _(f9EB, hAFB)
            if (_oz(z, 8, e, s, gg)) {
                hAFB.wxVkey = 1
                var cCFB = _mz(z, 'xk-empty', ['bind:__l', 9, 'bind:clickButton', 1, 'data-event-opts', 2, 'type', 3, 'vueId', 4], [], e, s, gg)
                _(hAFB, cCFB)
            }
            var oDFB = _mz(z, 'xk-login', ['bind:__l', 14, 'bind:success', 1, 'class', 2, 'data-event-opts', 3, 'data-ref', 4, 'vueId', 5], [], e, s, gg)
            _(f9EB, oDFB)
            c0EB.wxXCkey = 1
            c0EB.wxXCkey = 3
            hAFB.wxXCkey = 1
            hAFB.wxXCkey = 3
            _(r, f9EB)
            return r
        }
        e_[x[0]] = {
            f: m0,
            j: [],
            i: [],
            ti: [],
            ic: []
        }
        if (path && e_[path]) {
            return function(env, dd, global) {
                $gwxc = 0;
                var root = {
                    "tag": "wx-page"
                };
                root.children = [];
                g = "$gwx_XC_101";
                var main = e_[path].f
                if (typeof global === "undefined") global = {};
                global.f = $gdc(f_[path], "", 1);
                try {
                    main(env, {}, root, global);
                    _tsd(root)
                } catch (err) {
                    console.log(err)
                };
                g = "";
                return root;
            }
        }
    }
}(__g.a, __g.b, __g.c, __g.d, __g.e, __g.f, __g.g, __g.h, __g.i, __g.j, __g.k, __g.l, __g.m, __g.n, __g.o, __g.p, __g.q, __g.r, __g.s, __g.t, __g.u, __g.v, __g.w, __g.x, __g.y, __g.z, __g.A, __g.B, __g.C, __g.D, __g.E, __g.F, __g.G, __g.H, __g.I, __g.J, __g.K, __g.L, __g.M, __g.N, __g.O, __g.P, __g.Q, __g.R, __g.S, __g.T, __g.U, __g.V, __g.W, __g.X, __g.Y, __g.Z, __g.aa);
if (__vd_version_info__.delayedGwx || false) $gwx_XC_101();
if (__vd_version_info__.delayedGwx) __wxAppCode__['pages/redirect.wxml'] = [$gwx_XC_101, './pages/redirect.wxml'];
else __wxAppCode__['pages/redirect.wxml'] = $gwx_XC_101('./pages/redirect.wxml');;
__wxRoute = "pages/redirect";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/redirect.js";
define("pages/redirect.js", function(require, module, exports, window, document, frames, self, location, navigator, localStorage, history, Caches, screen, alert, confirm, prompt, XMLHttpRequest, WebSocket, Reporter, webkit, WeixinJSCore) {
    (global.webpackJsonp = global.webpackJsonp || []).push([
        ["pages/redirect"], {
            "2a21": function(e, t, i) {
                "use strict";
                i.r(t);
                var n = i("6f2f"),
                    o = i.n(n);
                for (var r in n)["default"].indexOf(r) < 0 && function(e) {
                    i.d(t, e, (function() {
                        return n[e]
                    }))
                }(r);
                t.default = o.a
            },
            "33c6": function(e, t, i) {
                "use strict";
                i.r(t);
                var n = i("6e95"),
                    o = i("2a21");
                for (var r in o)["default"].indexOf(r) < 0 && function(e) {
                    i.d(t, e, (function() {
                        return o[e]
                    }))
                }(r);
                var s = i("828b"),
                    a = Object(s.a)(o.default, n.b, n.c, !1, null, null, null, !1, n.a, void 0);
                t.default = a.exports
            },
            "68ae": function(e, t, i) {
                "use strict";
                (function(e, t) {
                    var n = i("47a9");
                    i("8f74"), n(i("3240"));
                    var o = n(i("33c6"));
                    e.__webpack_require_UNI_MP_PLUGIN__ = i, t(o.default)
                }).call(this, i("3223").default, i("df3c").createPage)
            },
            "6e95": function(e, t, i) {
                "use strict";
                i.d(t, "b", (function() {
                    return n
                })), i.d(t, "c", (function() {
                    return o
                })), i.d(t, "a", (function() {}));
                var n = function() {
                        this.$createElement;
                        this._self._c
                    },
                    o = []
            },
            "6f2f": function(e, t, i) {
                "use strict";
                (function(e) {
                    var n = i("47a9");
                    Object.defineProperty(t, "__esModule", {
                        value: !0
                    }), t.default = void 0;
                    var o = i("62a4"),
                        r = n(i("f462")),
                        s = {
                            data: function() {
                                return {
                                    errorLink: !1,
                                    showLoginEmptyComponent: !1,
                                    options: {},
                                    returnUrl: ""
                                }
                            },
                            onLoad: function(e) {
                                console.log(e), this.options = e
                            },
                            onReady: function() {
                                if (this.options.q) {
                                    var e = decodeURIComponent(this.options.q);
                                    console.log(e);
                                    var t = e.match(/[?&]([^=&#]+)=([^&#]*)/g),
                                        i = {};
                                    if (t)
                                        for (var n in t) {
                                            var o = t[n].split("="),
                                                r = o[0].substr(1),
                                                s = o[1];
                                            i[r] = s
                                        }
                                    this.options = Object.assign(i, {
                                        url: decodeURIComponent(i.url)
                                    })
                                }
                                this.options.type ? this.redirect() : this.errorLink = !0
                            },
                            methods: {
                                redirect: function() {
                                    var e = this.options;
                                    switch (e.type) {
                                        case "home":
                                            this.goHome();
                                            break;
                                        case "course":
                                            this.$api.openWin({
                                                type: "redirect",
                                                url: "/pages/course/index",
                                                params: {
                                                    id: e.id
                                                }
                                            });
                                            break;
                                        case "course-list":
                                            this.$api.openWin({
                                                type: "redirect",
                                                url: "/pages/course/list",
                                                params: {
                                                    cid: e.cid
                                                }
                                            });
                                            break;
                                        case "qbank":
                                            this.$api.openWin({
                                                type: "redirect",
                                                url: "/pages/index",
                                                params: {
                                                    tabType: "qbank",
                                                    qbankId: e.id
                                                }
                                            });
                                            break;
                                        case "qbank-list":
                                            this.$api.openWin({
                                                type: "redirect",
                                                url: "/pages/qbank/list",
                                                params: {
                                                    cid: e.cid
                                                }
                                            });
                                            break;
                                        case "class":
                                            this.$api.openWin({
                                                type: "redirect",
                                                url: "/pages/class/index",
                                                params: {
                                                    id: e.id
                                                }
                                            });
                                            break;
                                        case "class-list":
                                            this.$api.openWin({
                                                type: "redirect",
                                                url: "/pages/index",
                                                params: {
                                                    tabType: "class"
                                                }
                                            });
                                            break;
                                        case "article":
                                            this.$api.openWin({
                                                type: "redirect",
                                                url: "/pages/article/index",
                                                params: {
                                                    id: e.id
                                                }
                                            });
                                            break;
                                        case "article-list":
                                            this.$api.openWin({
                                                type: "redirect",
                                                url: "/pages/index",
                                                params: {
                                                    tabType: "find"
                                                }
                                            });
                                        case "custom":
                                            e.url ? 1 == e.login ? (0, o.getToken)() ? this.goCustomLink() : (this.showLoginEmptyComponent = !0, this.goLogin()) : this.goCustomLink() : this.errorLink = !0;
                                            break;
                                        case "getOfficialOpenId":
                                            this.errorLink = !1;
                                            break;
                                        default:
                                            this.errorLink = !0
                                    }
                                },
                                goHome: function() {
                                    e.reLaunch({
                                        url: "/pages/index"
                                    })
                                },
                                goCustomLink: function() {
                                    var e = this;
                                    if (this.options.url) {
                                        this.options.url = decodeURIComponent(this.options.url);
                                        var t = {};
                                        try {
                                            for (var i = this.options.url.split("?")[1].split("&"), n = 0; n < i.length; n++) t[i[n].split("=")[0]] = i[n].split("=")[1]
                                        } catch (e) {
                                            console.log(e), t = {}
                                        }
                                        console.log(t), t.resellerId && this.$http({
                                            url: "/api/reseller/save_customer_inviter",
                                            data: {
                                                inviterId: t.resellerId
                                            },
                                            token: 1
                                        }).then((function(i) {
                                            100143 === i.errno && e.$store.commit("setResellerId", t.resellerId)
                                        })), this.$api.openWin({
                                            type: "redirect",
                                            url: "/".concat(this.options.url)
                                        })
                                    }
                                },
                                goLogin: function() {
                                    var e = this;
                                    (0, r.default)({
                                        component: !0,
                                        openComponent: function() {
                                            e.$refs.login.show()
                                        },
                                        success: function() {
                                            e.loginSuccess()
                                        }
                                    })
                                },
                                loginSuccess: function() {
                                    this.goCustomLink()
                                }
                            }
                        };
                    t.default = s
                }).call(this, i("df3c").default)
            }
        },
        [
            ["68ae", "common/runtime", "common/vendor"]
        ]
    ]);
}, {
    isPage: true,
    isComponent: true,
    currentFile: 'pages/redirect.js'
});
require("pages/redirect.js");