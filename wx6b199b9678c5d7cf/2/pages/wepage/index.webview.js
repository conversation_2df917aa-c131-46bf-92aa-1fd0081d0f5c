$gwx_XC_146 = function(_, _v, _n, _p, _s, _wp, _wl, $gwn, $gwl, $gwh, wh, $gstack, $gwrt, gra, grb, TestTest, wfor, _ca, _da, _r, _rz, _o, _oz, _1, _1z, _2, _2z, _m, _mz, nv_getDate, nv_getRegExp, nv_console, nv_parseInt, nv_parseFloat, nv_isNaN, nv_isFinite, nv_decodeURI, nv_decodeURIComponent, nv_encodeURI, nv_encodeURIComponent, $gdc, nv_JSON, _af, _gv, _ai, _grp, _gd, _gapi, $ixc, _ic, _w, _ev, _tsd) {
    return function(path, global) {
        if (typeof global === 'undefined') {
            if (typeof __GWX_GLOBAL__ === 'undefined') global = {};
            else global = __GWX_GLOBAL__;
        }
        if (typeof __WXML_GLOBAL__ === 'undefined') {
            __WXML_GLOBAL__ = {};
        }
        __WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
        var e_ = {}
        if (typeof(global.entrys) === 'undefined') global.entrys = {};
        e_ = global.entrys;
        var d_ = {}
        if (typeof(global.defines) === 'undefined') global.defines = {};
        d_ = global.defines;
        var f_ = {}
        if (typeof(global.modules) === 'undefined') global.modules = {};
        f_ = global.modules || {};
        var p_ = {}
        __WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
        __WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
        __WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
        var z = __WXML_GLOBAL__.ops_set.$gwx_XC_146 || [];

        function gz$gwx_XC_146_1() {
            if (__WXML_GLOBAL__.ops_cached.$gwx_XC_146_1) return __WXML_GLOBAL__.ops_cached.$gwx_XC_146_1
            __WXML_GLOBAL__.ops_cached.$gwx_XC_146_1 = [];
            (function(z) {
                var a = 11;

                function Z(ops) {
                    z.push(ops)
                }
                Z([3, 'data-v-a6b4f6bc'])
                Z([
                    [2, '+'],
                    [1, 'background:'],
                    [
                        [6],
                        [
                            [7],
                            [3, 'page']
                        ],
                        [3, 'background']
                    ]
                ])
                Z(z[0])
                Z([
                    [7],
                    [3, 'systemNavBarFrontColor']
                ])
                Z([3, 'wepage data-v-a6b4f6bc'])
                Z([
                    [7],
                    [3, 'navBarBackColor']
                ])
                Z([3, '__l'])
                Z([3, '__e'])
                Z([
                    [2, '=='],
                    [
                        [7],
                        [3, 'navBarBackColor']
                    ],
                    [1, '#fff']
                ])
                Z(z[0])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^onRightActionClick']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'sharePage']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([
                    [7],
                    [3, 'navBarFrontColor']
                ])
                Z([3, 'xk-share'])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'page']
                    ],
                    [3, 'navBarTitle']
                ])
                Z([3, '6a223ee6-1'])
                Z(z[6])
                Z(z[0])
                Z([
                    [7],
                    [3, 'nodes']
                ])
                Z([3, '6a223ee6-2'])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'page']
                    ],
                    [3, 'id']
                ])
                Z(z[6])
                Z([3, 'data-v-a6b4f6bc vue-ref'])
                Z([3, 'shareMenu'])
                Z([
                    [7],
                    [3, 'shareInfo']
                ])
                Z([3, '6a223ee6-3'])
                Z(z[6])
                Z(z[7])
                Z(z[21])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^success']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'loginSuccess']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'login'])
                Z([3, '6a223ee6-4'])
            })(__WXML_GLOBAL__.ops_cached.$gwx_XC_146_1);
            return __WXML_GLOBAL__.ops_cached.$gwx_XC_146_1
        }
        __WXML_GLOBAL__.ops_set.$gwx_XC_146 = z;
        __WXML_GLOBAL__.ops_init.$gwx_XC_146 = true;
        var x = ['./pages/wepage/index.wxml'];
        d_[x[0]] = {}
        var m0 = function(e, s, r, gg) {
            var z = gz$gwx_XC_146_1()
            var oN7E = _mz(z, 'page-meta', ['class', 0, 'pageStyle', 1], [], e, s, gg)
            var cO7E = _mz(z, 'navigation-bar', ['class', 2, 'frontColor', 1], [], e, s, gg)
            _(oN7E, cO7E)
            _(r, oN7E)
            var oP7E = _n('view')
            _rz(z, oP7E, 'class', 4, e, s, gg)
            var lQ7E = _mz(z, 'nav-bar', ['bgColor', 5, 'bind:__l', 1, 'bind:onRightActionClick', 2, 'bottomLine', 3, 'class', 4, 'data-event-opts', 5, 'frontColor', 6, 'rightIconClass', 7, 'title', 8, 'vueId', 9], [], e, s, gg)
            _(oP7E, lQ7E)
            var aR7E = _mz(z, 'node-list', ['bind:__l', 15, 'class', 1, 'nodes', 2, 'vueId', 3, 'wepageId', 4], [], e, s, gg)
            _(oP7E, aR7E)
            var tS7E = _mz(z, 'xk-share', ['bind:__l', 20, 'class', 1, 'data-ref', 2, 'shareInfo', 3, 'vueId', 4], [], e, s, gg)
            _(oP7E, tS7E)
            var eT7E = _mz(z, 'xk-login', ['bind:__l', 25, 'bind:success', 1, 'class', 2, 'data-event-opts', 3, 'data-ref', 4, 'vueId', 5], [], e, s, gg)
            _(oP7E, eT7E)
            _(r, oP7E)
            return r
        }
        e_[x[0]] = {
            f: m0,
            j: [],
            i: [],
            ti: [],
            ic: []
        }
        if (path && e_[path]) {
            outerGlobal.__wxml_comp_version__ = 0.02
            return function(env, dd, global) {
                $gwxc = 0;
                var root = {
                    "tag": "wx-page"
                };
                root.children = [];
                g = "$gwx_XC_146";
                var main = e_[path].f
                if (typeof global === "undefined") global = {};
                global.f = $gdc(f_[path], "", 1);
                if (typeof(outerGlobal.__webview_engine_version__) != 'undefined' && outerGlobal.__webview_engine_version__ + 1e-6 >= 0.02 + 1e-6 && outerGlobal.__mergeData__) {
                    env = outerGlobal.__mergeData__(env, dd);
                }
                try {
                    main(env, {}, root, global);
                    _tsd(root)
                    if (typeof(outerGlobal.__webview_engine_version__) == 'undefined' || outerGlobal.__webview_engine_version__ + 1e-6 < 0.01 + 1e-6) {
                        return _ev(root);
                    }
                } catch (err) {
                    console.log(err)
                };
                g = "";
                return root;
            }
        }
    }
}(__g.a, __g.b, __g.c, __g.d, __g.e, __g.f, __g.g, __g.h, __g.i, __g.j, __g.k, __g.l, __g.m, __g.n, __g.o, __g.p, __g.q, __g.r, __g.s, __g.t, __g.u, __g.v, __g.w, __g.x, __g.y, __g.z, __g.A, __g.B, __g.C, __g.D, __g.E, __g.F, __g.G, __g.H, __g.I, __g.J, __g.K, __g.L, __g.M, __g.N, __g.O, __g.P, __g.Q, __g.R, __g.S, __g.T, __g.U, __g.V, __g.W, __g.X, __g.Y, __g.Z, __g.aa);
if (__vd_version_info__.delayedGwx || false) $gwx_XC_146();
if (__vd_version_info__.delayedGwx) __wxAppCode__['pages/wepage/index.wxml'] = [$gwx_XC_146, './pages/wepage/index.wxml'];
else __wxAppCode__['pages/wepage/index.wxml'] = $gwx_XC_146('./pages/wepage/index.wxml');

var noCss = typeof __vd_version_info__ !== 'undefined' && __vd_version_info__.noCss === true;
if (!noCss) {
    __wxAppCode__['pages/wepage/index.wxss'] = setCssToHead(["body{min-height:100%}\n.", [1], "wepage.", [1], "data-v-a6b4f6bc{padding-bottom:50px}\n", ], "Some selectors are not allowed in component wxss, including tag name selectors, ID selectors, and attribute selectors.(./pages/wepage/index.wxss:1:1)", {
        path: "./pages/wepage/index.wxss"
    });
}