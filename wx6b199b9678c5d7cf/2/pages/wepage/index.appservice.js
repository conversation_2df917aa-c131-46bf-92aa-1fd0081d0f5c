$gwx_XC_146 = function(_, _v, _n, _p, _s, _wp, _wl, $gwn, $gwl, $gwh, wh, $gstack, $gwrt, gra, grb, TestTest, wfor, _ca, _da, _r, _rz, _o, _oz, _1, _1z, _2, _2z, _m, _mz, nv_getDate, nv_getRegExp, nv_console, nv_parseInt, nv_parseFloat, nv_isNaN, nv_isFinite, nv_decodeURI, nv_decodeURIComponent, nv_encodeURI, nv_encodeURIComponent, $gdc, nv_JSON, _af, _gv, _ai, _grp, _gd, _gapi, $ixc, _ic, _w, _ev, _tsd) {
    return function(path, global) {
        if (typeof global === 'undefined') {
            if (typeof __GWX_GLOBAL__ === 'undefined') global = {};
            else global = __GWX_GLOBAL__;
        }
        if (typeof __WXML_GLOBAL__ === 'undefined') {
            __WXML_GLOBAL__ = {};
        }
        __WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
        var e_ = {}
        if (typeof(global.entrys) === 'undefined') global.entrys = {};
        e_ = global.entrys;
        var d_ = {}
        if (typeof(global.defines) === 'undefined') global.defines = {};
        d_ = global.defines;
        var f_ = {}
        if (typeof(global.modules) === 'undefined') global.modules = {};
        f_ = global.modules || {};
        var p_ = {}
        __WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
        __WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
        __WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
        var z = __WXML_GLOBAL__.ops_set.$gwx_XC_146 || [];

        function gz$gwx_XC_146_1() {
            if (__WXML_GLOBAL__.ops_cached.$gwx_XC_146_1) return __WXML_GLOBAL__.ops_cached.$gwx_XC_146_1
            __WXML_GLOBAL__.ops_cached.$gwx_XC_146_1 = [];
            (function(z) {
                var a = 11;

                function Z(ops) {
                    z.push(ops)
                }
                Z([3, 'wepage data-v-a6b4f6bc'])
                Z([
                    [7],
                    [3, 'navBarBackColor']
                ])
                Z([3, '__l'])
                Z([3, '__e'])
                Z([
                    [2, '=='],
                    [
                        [7],
                        [3, 'navBarBackColor']
                    ],
                    [1, '#fff']
                ])
                Z([3, 'data-v-a6b4f6bc'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^onRightActionClick']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'sharePage']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([
                    [7],
                    [3, 'navBarFrontColor']
                ])
                Z([3, 'xk-share'])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'page']
                    ],
                    [3, 'navBarTitle']
                ])
                Z([3, '6a223ee6-1'])
                Z(z[2])
                Z(z[5])
                Z([
                    [7],
                    [3, 'nodes']
                ])
                Z([3, '6a223ee6-2'])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'page']
                    ],
                    [3, 'id']
                ])
                Z(z[2])
                Z([3, 'data-v-a6b4f6bc vue-ref'])
                Z([3, 'shareMenu'])
                Z([
                    [7],
                    [3, 'shareInfo']
                ])
                Z([3, '6a223ee6-3'])
                Z(z[2])
                Z(z[3])
                Z(z[17])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^success']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'loginSuccess']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'login'])
                Z([3, '6a223ee6-4'])
            })(__WXML_GLOBAL__.ops_cached.$gwx_XC_146_1);
            return __WXML_GLOBAL__.ops_cached.$gwx_XC_146_1
        }
        __WXML_GLOBAL__.ops_set.$gwx_XC_146 = z;
        __WXML_GLOBAL__.ops_init.$gwx_XC_146 = true;
        var x = ['./pages/wepage/index.wxml'];
        d_[x[0]] = {}
        var m0 = function(e, s, r, gg) {
            var z = gz$gwx_XC_146_1()
            var tEVB = _n('view')
            _rz(z, tEVB, 'class', 0, e, s, gg)
            var eFVB = _mz(z, 'nav-bar', ['bgColor', 1, 'bind:__l', 1, 'bind:onRightActionClick', 2, 'bottomLine', 3, 'class', 4, 'data-event-opts', 5, 'frontColor', 6, 'rightIconClass', 7, 'title', 8, 'vueId', 9], [], e, s, gg)
            _(tEVB, eFVB)
            var bGVB = _mz(z, 'node-list', ['bind:__l', 11, 'class', 1, 'nodes', 2, 'vueId', 3, 'wepageId', 4], [], e, s, gg)
            _(tEVB, bGVB)
            var oHVB = _mz(z, 'xk-share', ['bind:__l', 16, 'class', 1, 'data-ref', 2, 'shareInfo', 3, 'vueId', 4], [], e, s, gg)
            _(tEVB, oHVB)
            var xIVB = _mz(z, 'xk-login', ['bind:__l', 21, 'bind:success', 1, 'class', 2, 'data-event-opts', 3, 'data-ref', 4, 'vueId', 5], [], e, s, gg)
            _(tEVB, xIVB)
            _(r, tEVB)
            return r
        }
        e_[x[0]] = {
            f: m0,
            j: [],
            i: [],
            ti: [],
            ic: []
        }
        if (path && e_[path]) {
            return function(env, dd, global) {
                $gwxc = 0;
                var root = {
                    "tag": "wx-page"
                };
                root.children = [];
                g = "$gwx_XC_146";
                var main = e_[path].f
                if (typeof global === "undefined") global = {};
                global.f = $gdc(f_[path], "", 1);
                try {
                    main(env, {}, root, global);
                    _tsd(root)
                } catch (err) {
                    console.log(err)
                };
                g = "";
                return root;
            }
        }
    }
}(__g.a, __g.b, __g.c, __g.d, __g.e, __g.f, __g.g, __g.h, __g.i, __g.j, __g.k, __g.l, __g.m, __g.n, __g.o, __g.p, __g.q, __g.r, __g.s, __g.t, __g.u, __g.v, __g.w, __g.x, __g.y, __g.z, __g.A, __g.B, __g.C, __g.D, __g.E, __g.F, __g.G, __g.H, __g.I, __g.J, __g.K, __g.L, __g.M, __g.N, __g.O, __g.P, __g.Q, __g.R, __g.S, __g.T, __g.U, __g.V, __g.W, __g.X, __g.Y, __g.Z, __g.aa);
if (__vd_version_info__.delayedGwx || false) $gwx_XC_146();
if (__vd_version_info__.delayedGwx) __wxAppCode__['pages/wepage/index.wxml'] = [$gwx_XC_146, './pages/wepage/index.wxml'];
else __wxAppCode__['pages/wepage/index.wxml'] = $gwx_XC_146('./pages/wepage/index.wxml');;
__wxRoute = "pages/wepage/index";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/wepage/index.js";
define("pages/wepage/index.js", function(require, module, exports, window, document, frames, self, location, navigator, localStorage, history, Caches, screen, alert, confirm, prompt, XMLHttpRequest, WebSocket, Reporter, webkit, WeixinJSCore) {
    (global.webpackJsonp = global.webpackJsonp || []).push([
        ["pages/wepage/index"], {
            "0e9d": function(e, n, t) {
                "use strict";
                t.d(n, "b", (function() {
                    return a
                })), t.d(n, "c", (function() {
                    return i
                })), t.d(n, "a", (function() {}));
                var a = function() {
                        this.$createElement;
                        this._self._c
                    },
                    i = []
            },
            2025: function(e, n, t) {
                "use strict";
                var a = t("8c08");
                t.n(a).a
            },
            "315e": function(e, n, t) {
                "use strict";
                var a = t("5acb");
                t.n(a).a
            },
            "5acb": function(e, n, t) {},
            "6a67": function(e, n, t) {
                "use strict";
                t.r(n);
                var a = t("0e9d"),
                    i = t("9857");
                for (var o in i)["default"].indexOf(o) < 0 && function(e) {
                    t.d(n, e, (function() {
                        return i[e]
                    }))
                }(o);
                t("315e"), t("2025");
                var r = t("828b"),
                    s = Object(r.a)(i.default, a.b, a.c, !1, null, "a6b4f6bc", null, !1, a.a, void 0);
                n.default = s.exports
            },
            "78b0": function(e, n, t) {
                "use strict";
                (function(e) {
                    var a = t("47a9");
                    Object.defineProperty(n, "__esModule", {
                        value: !0
                    }), n.default = void 0;
                    var i = a(t("f462")),
                        o = {
                            components: {
                                xkShare: function() {
                                    Promise.all([t.e("common/vendor"), t.e("components/share/share")]).then(function() {
                                        return resolve(t("d066"))
                                    }.bind(null, t)).catch(t.oe)
                                },
                                nodeList: function() {
                                    t.e("pages/wepage/node-list").then(function() {
                                        return resolve(t("4f5e"))
                                    }.bind(null, t)).catch(t.oe)
                                }
                            },
                            data: function() {
                                return {
                                    page: {},
                                    nodes: []
                                }
                            },
                            computed: {
                                navBarTitle: function() {
                                    return this.page.navBarTitle
                                },
                                navBarBackColor: function() {
                                    return "theme" == this.page.navBarStyle ? "" : "#fff"
                                },
                                navBarFrontColor: function() {
                                    return "theme" == this.page.navBarStyle ? "white" : "black"
                                },
                                systemNavBarFrontColor: function() {
                                    return "theme" == this.page.navBarStyle ? "#ffffff" : "#000000"
                                },
                                shareInfo: function() {
                                    return this.page.id ? {
                                        title: this.page.shareTitle || this.page.navBarTitle || "微页面",
                                        summary: this.page.shareDesc || "点击查看",
                                        link: "/pages/wepage/index?id=" + this.page.id,
                                        imageUrl: this.formatImageUrl(this.page.shareImage, 750, 600, 1)
                                    } : {}
                                }
                            },
                            onLoad: function(e) {
                                e.id && (this.id = e.id, this.getNodes())
                            },
                            onReady: function() {},
                            onShow: function() {},
                            onShareAppMessage: function() {
                                return {
                                    title: this.shareInfo.title,
                                    path: this.shareInfo.link,
                                    imageUrl: this.shareInfo.imageUrl
                                }
                            },
                            onShareTimeline: function() {
                                return {
                                    title: this.shareInfo.title,
                                    query: this.shareInfo.link.split("?")[1] || "",
                                    imageUrl: this.shareInfo.imageUrl
                                }
                            },
                            methods: {
                                sharePage: function() {
                                    this.page.id && this.$refs.shareMenu.open()
                                },
                                getNodes: function() {
                                    var n = this;
                                    this.$api.showLoading(), this.$http({
                                        url: "/api/wepage/detail",
                                        data: {
                                            id: this.id
                                        }
                                    }).then((function(t) {
                                        if (n.$api.hideLoading(), 0 === t.errno) {
                                            if (1 == t.data.isHome) return void e.reLaunch({
                                                url: "/pages/index?tabType=home"
                                            });
                                            n.dataHasBeenLoaded = !0, n.page = t.data, n.nodes = t.data.nodes || []
                                        }
                                    }))
                                },
                                goLogin: function(e) {
                                    var n = this;
                                    (0, i.default)({
                                        component: !0,
                                        openComponent: function(e) {
                                            n.$refs.login.show()
                                        },
                                        success: function() {
                                            n.loginSuccess()
                                        }
                                    })
                                },
                                loginSuccess: function() {},
                                formatImageUrl: function(e, n, t, a) {
                                    return this.$api.formatImageUrl(e, n, t, a)
                                }
                            }
                        };
                    n.default = o
                }).call(this, t("df3c").default)
            },
            "7b2d": function(e, n, t) {
                "use strict";
                (function(e, n) {
                    var a = t("47a9");
                    t("8f74"), a(t("3240"));
                    var i = a(t("6a67"));
                    e.__webpack_require_UNI_MP_PLUGIN__ = t, n(i.default)
                }).call(this, t("3223").default, t("df3c").createPage)
            },
            "8c08": function(e, n, t) {},
            9857: function(e, n, t) {
                "use strict";
                t.r(n);
                var a = t("78b0"),
                    i = t.n(a);
                for (var o in a)["default"].indexOf(o) < 0 && function(e) {
                    t.d(n, e, (function() {
                        return a[e]
                    }))
                }(o);
                n.default = i.a
            }
        },
        [
            ["7b2d", "common/runtime", "common/vendor"]
        ]
    ]);
}, {
    isPage: true,
    isComponent: true,
    currentFile: 'pages/wepage/index.js'
});
require("pages/wepage/index.js");