$gwx5_XC_4 = function(_, _v, _n, _p, _s, _wp, _wl, $gwn, $gwl, $gwh, wh, $gstack, $gwrt, gra, grb, TestTest, wfor, _ca, _da, _r, _rz, _o, _oz, _1, _1z, _2, _2z, _m, _mz, nv_getDate, nv_getRegExp, nv_console, nv_parseInt, nv_parseFloat, nv_isNaN, nv_isFinite, nv_decodeURI, nv_decodeURIComponent, nv_encodeURI, nv_encodeURIComponent, $gdc, nv_JSON, _af, _gv, _ai, _grp, _gd, _gapi, $ixc, _ic, _w, _ev, _tsd) {
    return function(path, global) {
        if (typeof global === 'undefined') {
            if (typeof __GWX_GLOBAL__ === 'undefined') global = {};
            else global = __GWX_GLOBAL__;
        }
        if (typeof __WXML_GLOBAL__ === 'undefined') {
            __WXML_GLOBAL__ = {};
        }
        __WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
        var e_ = {}
        if (typeof(global.entrys) === 'undefined') global.entrys = {};
        e_ = global.entrys;
        var d_ = {}
        if (typeof(global.defines) === 'undefined') global.defines = {};
        d_ = global.defines;
        var f_ = {}
        if (typeof(global.modules) === 'undefined') global.modules = {};
        f_ = global.modules || {};
        var p_ = {}
        __WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
        __WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
        __WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
        var z = __WXML_GLOBAL__.ops_set.$gwx5_XC_4 || [];

        function gz$gwx5_XC_4_1() {
            if (__WXML_GLOBAL__.ops_cached.$gwx5_XC_4_1) return __WXML_GLOBAL__.ops_cached.$gwx5_XC_4_1
            __WXML_GLOBAL__.ops_cached.$gwx5_XC_4_1 = [];
            (function(z) {
                var a = 11;

                function Z(ops) {
                    z.push(ops)
                }
                Z([3, 'data-v-5bf1ac2e'])
                Z([
                    [2, '||'],
                    [
                        [7],
                        [3, 'themeColor']
                    ],
                    [1, '#fff']
                ])
                Z([3, '__l'])
                Z(z[0])
                Z([
                    [2, '||'],
                    [
                        [6],
                        [
                            [7],
                            [3, 'qbank']
                        ],
                        [3, 'title']
                    ],
                    [1, '在线题库']
                ])
                Z([3, 'd6fb36cc-1'])
                Z([
                    [7],
                    [3, 'pageReady']
                ])
                Z([3, 'page-wrap data-v-5bf1ac2e'])
                Z([3, 'pro-price-box data-v-5bf1ac2e'])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'g0']
                ])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'qbank']
                    ],
                    [3, 'marketPrice']
                ])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'qbank']
                    ],
                    [3, 'id']
                ])
                Z([
                    [2, '||'],
                    [
                        [7],
                        [3, 'isShowActivity']
                    ],
                    [
                        [7],
                        [3, 'isHaveXuDiscount']
                    ]
                ])
                Z([1, true])
                Z(z[13])
                Z([3, 'activity-swiper data-v-5bf1ac2e'])
                Z([1, 400])
                Z([1, false])
                Z([1, 3000])
                Z([
                    [2, '||'],
                    [
                        [7],
                        [3, 'isHaveDiscount']
                    ],
                    [
                        [7],
                        [3, 'isHaveXuDiscount']
                    ]
                ])
                Z(z[2])
                Z([3, '__e'])
                Z(z[21])
                Z([3, 'data-v-5bf1ac2e vue-ref'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [5],
                            [
                                [4],
                                [
                                    [5],
                                    [
                                        [5],
                                        [1, '^timeEnd']
                                    ],
                                    [
                                        [4],
                                        [
                                            [5],
                                            [
                                                [4],
                                                [
                                                    [5],
                                                    [1, 'getQbank']
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^xfEvent']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [
                                                    [5],
                                                    [1, 'intercept']
                                                ],
                                                [
                                                    [4],
                                                    [
                                                        [5],
                                                        [
                                                            [4],
                                                            [
                                                                [5],
                                                                [
                                                                    [5],
                                                                    [
                                                                        [5],
                                                                        [1, 'o']
                                                                    ],
                                                                    [
                                                                        [4],
                                                                        [
                                                                            [5],
                                                                            [
                                                                                [5],
                                                                                [1, 'methods']
                                                                            ],
                                                                            [1, 'clickBottomButton']
                                                                        ]
                                                                    ]
                                                                ],
                                                                [
                                                                    [4],
                                                                    [
                                                                        [5],
                                                                        [
                                                                            [5],
                                                                            [1, 'renew']
                                                                        ],
                                                                        [1, true]
                                                                    ]
                                                                ]
                                                            ]
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'timeDiscount'])
                Z([
                    [7],
                    [3, 'qbank']
                ])
                Z([
                    [6],
                    [
                        [6],
                        [
                            [7],
                            [3, 'qbank']
                        ],
                        [3, 'activity']
                    ],
                    [3, 'discount']
                ])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'qbank']
                    ],
                    [3, 'price']
                ])
                Z([3, 'd6fb36cc-2'])
                Z([
                    [7],
                    [3, 'isHaveTuan']
                ])
                Z(z[2])
                Z(z[21])
                Z(z[23])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^success']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'pintuanSuccessCallback']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'tuan'])
                Z(z[11])
                Z([3, 'q_bank'])
                Z([3, 'd6fb36cc-3'])
                Z([
                    [7],
                    [3, 'isHaveCut']
                ])
                Z(z[2])
                Z(z[23])
                Z([3, 'priceCut'])
                Z([
                    [6],
                    [
                        [6],
                        [
                            [6],
                            [
                                [7],
                                [3, 'qbank']
                            ],
                            [3, 'activity']
                        ],
                        [3, 'price_cut']
                    ],
                    [3, 'setId']
                ])
                Z([3, 'd6fb36cc-4'])
                Z([
                    [2, '&&'],
                    [
                        [2, '&&'],
                        [
                            [2, '=='],
                            [
                                [7],
                                [3, 'showUserSwitch']
                            ],
                            [1, 1]
                        ],
                        [
                            [7],
                            [3, 'memberHasBeenLoaded']
                        ]
                    ],
                    [
                        [7],
                        [3, 'allMemberCount']
                    ]
                ])
                Z([3, 'member-box data-v-5bf1ac2e'])
                Z([3, 'member-box-top data-v-5bf1ac2e'])
                Z([3, 'hover'])
                Z([3, 'user-avatar-list data-v-5bf1ac2e'])
                Z([3, 'index'])
                Z([3, 'item'])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'l0']
                ])
                Z(z[50])
                Z([
                    [2, '<'],
                    [
                        [7],
                        [3, 'index']
                    ],
                    [1, 3]
                ])
                Z([
                    [2, '>'],
                    [
                        [7],
                        [3, 'allMemberCount']
                    ],
                    [1, 3]
                ])
                Z([
                    [2, '>'],
                    [
                        [7],
                        [3, 'allMemberCount']
                    ],
                    [1, 10]
                ])
                Z([
                    [7],
                    [3, 'memberLoading']
                ])
                Z([
                    [7],
                    [3, 'memberAutoplay']
                ])
                Z(z[21])
                Z(z[13])
                Z([3, 'member-box-swiper data-v-5bf1ac2e'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, 'change']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [
                                                    [5],
                                                    [1, 'memberSwiperChange']
                                                ],
                                                [
                                                    [4],
                                                    [
                                                        [5],
                                                        [1, '$event']
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([1, 300])
                Z(z[17])
                Z(z[18])
                Z(z[13])
                Z(z[50])
                Z(z[51])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'l1']
                ])
                Z([3, 'id'])
                Z([
                    [2, '=='],
                    [
                        [2, '%'],
                        [
                            [7],
                            [3, 'index']
                        ],
                        [1, 2]
                    ],
                    [1, 0]
                ])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'memberList']
                    ],
                    [
                        [2, '+'],
                        [
                            [7],
                            [3, 'index']
                        ],
                        [1, 1]
                    ]
                ])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'g1']
                ])
                Z([3, 'detail card-common data-v-5bf1ac2e'])
                Z([
                    [7],
                    [3, 'isHaveWechatGroup']
                ])
                Z([
                    [7],
                    [3, 'detail']
                ])
                Z(z[2])
                Z([3, 'detail-content data-v-5bf1ac2e'])
                Z(z[76])
                Z([3, 'd6fb36cc-5'])
                Z([3, 'page-bottom data-v-5bf1ac2e'])
                Z([
                    [2, '+'],
                    [
                        [2, '+'],
                        [1, 'padding-bottom:'],
                        [
                            [2, '+'],
                            [
                                [7],
                                [3, 'safeAreaBottom']
                            ],
                            [1, 'px']
                        ]
                    ],
                    [1, ';']
                ])
                Z([
                    [7],
                    [3, 'showBuyVip']
                ])
                Z([
                    [2, '||'],
                    [
                        [7],
                        [3, 'showKefu']
                    ],
                    [
                        [7],
                        [3, 'useCart']
                    ]
                ])
                Z([3, 'bottom-left data-v-5bf1ac2e'])
                Z([
                    [7],
                    [3, 'showKefu']
                ])
                Z([
                    [7],
                    [3, 'useCart']
                ])
                Z(z[21])
                Z([3, 'item data-v-5bf1ac2e'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, 'tap']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [
                                                    [5],
                                                    [1, 'intercept']
                                                ],
                                                [
                                                    [4],
                                                    [
                                                        [5],
                                                        [
                                                            [4],
                                                            [
                                                                [5],
                                                                [
                                                                    [5],
                                                                    [1, 'o']
                                                                ],
                                                                [
                                                                    [4],
                                                                    [
                                                                        [5],
                                                                        [
                                                                            [5],
                                                                            [1, 'methods']
                                                                        ],
                                                                        [1, 'goCart']
                                                                    ]
                                                                ]
                                                            ]
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([
                    [7],
                    [3, 'cartGoodsNum']
                ])
                Z(z[21])
                Z([3, 'button data-v-5bf1ac2e'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, 'tap']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [
                                                    [5],
                                                    [1, 'intercept']
                                                ],
                                                [
                                                    [4],
                                                    [
                                                        [5],
                                                        [
                                                            [4],
                                                            [
                                                                [5],
                                                                [
                                                                    [5],
                                                                    [1, 'o']
                                                                ],
                                                                [
                                                                    [4],
                                                                    [
                                                                        [5],
                                                                        [
                                                                            [5],
                                                                            [1, 'methods']
                                                                        ],
                                                                        [1, 'clickBottomButton']
                                                                    ]
                                                                ]
                                                            ]
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([
                    [2, '!'],
                    [
                        [7],
                        [3, 'isHave']
                    ]
                ])
                Z(z[0])
                Z(z[28])
                Z([
                    [2, '&&'],
                    [
                        [7],
                        [3, 'adminConfig']
                    ],
                    [
                        [6],
                        [
                            [7],
                            [3, 'adminConfig']
                        ],
                        [3, 'loaded']
                    ]
                ])
                Z(z[0])
                Z([
                    [7],
                    [3, 'hiddenPrice']
                ])
                Z([
                    [7],
                    [3, 'sellOut']
                ])
                Z([
                    [7],
                    [3, 'isCanUseDiscount']
                ])
                Z([
                    [2, '||'],
                    [
                        [7],
                        [3, 'isHaveTuan']
                    ],
                    [
                        [7],
                        [3, 'isHaveCut']
                    ]
                ])
                Z([3, 'button-tuan data-v-5bf1ac2e'])
                Z(z[30])
                Z(z[39])
                Z(z[2])
                Z(z[23])
                Z([3, 'skus'])
                Z([3, 'd6fb36cc-6'])
                Z(z[2])
                Z(z[21])
                Z(z[23])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^selectItem']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'clickShareMenuItem']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'shareMenu'])
                Z([
                    [7],
                    [3, 'shareMenuPushData']
                ])
                Z([
                    [7],
                    [3, 'shareInfo']
                ])
                Z([3, 'd6fb36cc-7'])
                Z(z[2])
                Z(z[21])
                Z(z[23])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^success']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'loginSuccess']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'login'])
                Z([3, 'd6fb36cc-8'])
                Z([
                    [7],
                    [3, 'allowPayType']
                ])
                Z([
                    [7],
                    [3, 'assistantQrcode']
                ])
                Z(z[2])
                Z(z[21])
                Z(z[23])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^success']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'paymentSuccess']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'payment'])
                Z([
                    [7],
                    [3, 'qbankId']
                ])
                Z([3, 'questions'])
                Z([3, 'd6fb36cc-9'])
                Z(z[2])
                Z(z[23])
                Z([3, 'openModal'])
                Z([3, 'd6fb36cc-10'])
                Z([
                    [7],
                    [3, 'isShowWechatGroup']
                ])
                Z(z[21])
                Z([3, 'add-group-modal data-v-5bf1ac2e'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, 'tap']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [
                                                    [5],
                                                    [1, 'e0']
                                                ],
                                                [
                                                    [4],
                                                    [
                                                        [5],
                                                        [1, '$event']
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z(z[21])
                Z([3, 'add-group-panel data-v-5bf1ac2e'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, 'tap']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [
                                                    [5],
                                                    [1, '']
                                                ],
                                                [
                                                    [4],
                                                    [
                                                        [5],
                                                        [1, '$event']
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z(z[2])
                Z(z[0])
                Z(z[11])
                Z(z[133])
                Z([3, 'd6fb36cc-11'])
                Z(z[2])
                Z(z[23])
                Z([3, 'giftList'])
                Z([3, 'd6fb36cc-12'])
                Z(z[2])
                Z(z[0])
                Z([3, 'd6fb36cc-13'])
            })(__WXML_GLOBAL__.ops_cached.$gwx5_XC_4_1);
            return __WXML_GLOBAL__.ops_cached.$gwx5_XC_4_1
        }
        __WXML_GLOBAL__.ops_set.$gwx5_XC_4 = z;
        __WXML_GLOBAL__.ops_init.$gwx5_XC_4 = true;
        var x = ['./pages/qbank/detail.wxml'];
        d_[x[0]] = {}
        var m0 = function(e, s, r, gg) {
            var z = gz$gwx5_XC_4_1()
            var cZB = _n('view')
            _rz(z, cZB, 'class', 0, e, s, gg)
            var c3B = _mz(z, 'nav-bar', ['bgColor', 1, 'bind:__l', 1, 'class', 2, 'title', 3, 'vueId', 4], [], e, s, gg)
            _(cZB, c3B)
            var h1B = _v()
            _(cZB, h1B)
            if (_oz(z, 6, e, s, gg)) {
                h1B.wxVkey = 1
                var o4B = _n('view')
                _rz(z, o4B, 'class', 7, e, s, gg)
                var e8B = _n('view')
                _rz(z, e8B, 'class', 8, e, s, gg)
                var b9B = _v()
                _(e8B, b9B)
                if (_oz(z, 9, e, s, gg)) {
                    b9B.wxVkey = 1
                }
                var o0B = _v()
                _(e8B, o0B)
                if (_oz(z, 10, e, s, gg)) {
                    o0B.wxVkey = 1
                }
                var xAC = _v()
                _(e8B, xAC)
                if (_oz(z, 11, e, s, gg)) {
                    xAC.wxVkey = 1
                }
                b9B.wxXCkey = 1
                o0B.wxXCkey = 1
                xAC.wxXCkey = 1
                _(o4B, e8B)
                var l5B = _v()
                _(o4B, l5B)
                if (_oz(z, 12, e, s, gg)) {
                    l5B.wxVkey = 1
                    var oBC = _mz(z, 'swiper', ['autoplay', 13, 'circular', 1, 'class', 2, 'duration', 3, 'indicatorDots', 4, 'interval', 5], [], e, s, gg)
                    var fCC = _v()
                    _(oBC, fCC)
                    if (_oz(z, 19, e, s, gg)) {
                        fCC.wxVkey = 1
                        var oFC = _mz(z, 'time-discount', ['bind:__l', 20, 'bind:timeEnd', 1, 'bind:xfEvent', 2, 'class', 3, 'data-event-opts', 4, 'data-ref', 5, 'goodsInfo', 6, 'info', 7, 'price', 8, 'vueId', 9], [], e, s, gg)
                        _(fCC, oFC)
                    }
                    var cDC = _v()
                    _(oBC, cDC)
                    if (_oz(z, 30, e, s, gg)) {
                        cDC.wxVkey = 1
                        var cGC = _mz(z, 'pintuan', ['bind:__l', 31, 'bind:success', 1, 'class', 2, 'data-event-opts', 3, 'data-ref', 4, 'goodsId', 5, 'goodsType', 6, 'vueId', 7], [], e, s, gg)
                        _(cDC, cGC)
                    }
                    var hEC = _v()
                    _(oBC, hEC)
                    if (_oz(z, 39, e, s, gg)) {
                        hEC.wxVkey = 1
                        var oHC = _mz(z, 'price-cut', ['bind:__l', 40, 'class', 1, 'data-ref', 2, 'setId', 3, 'vueId', 4], [], e, s, gg)
                        _(hEC, oHC)
                    }
                    fCC.wxXCkey = 1
                    fCC.wxXCkey = 3
                    cDC.wxXCkey = 1
                    cDC.wxXCkey = 3
                    hEC.wxXCkey = 1
                    hEC.wxXCkey = 3
                    _(l5B, oBC)
                }
                var a6B = _v()
                _(o4B, a6B)
                if (_oz(z, 45, e, s, gg)) {
                    a6B.wxVkey = 1
                    var lIC = _n('view')
                    _rz(z, lIC, 'class', 46, e, s, gg)
                    var tKC = _mz(z, 'view', ['class', 47, 'hoverClass', 1], [], e, s, gg)
                    var bMC = _n('view')
                    _rz(z, bMC, 'class', 49, e, s, gg)
                    var xOC = _v()
                    _(bMC, xOC)
                    var oPC = function(cRC, fQC, hSC, gg) {
                        var cUC = _v()
                        _(hSC, cUC)
                        if (_oz(z, 54, cRC, fQC, gg)) {
                            cUC.wxVkey = 1
                        }
                        cUC.wxXCkey = 1
                        return hSC
                    }
                    xOC.wxXCkey = 2
                    _2z(z, 52, oPC, e, s, gg, xOC, 'item', 'index', 'index')
                    var oNC = _v()
                    _(bMC, oNC)
                    if (_oz(z, 55, e, s, gg)) {
                        oNC.wxVkey = 1
                    }
                    oNC.wxXCkey = 1
                    _(tKC, bMC)
                    var eLC = _v()
                    _(tKC, eLC)
                    if (_oz(z, 56, e, s, gg)) {
                        eLC.wxVkey = 1
                    }
                    eLC.wxXCkey = 1
                    _(lIC, tKC)
                    var aJC = _v()
                    _(lIC, aJC)
                    if (_oz(z, 57, e, s, gg)) {
                        aJC.wxVkey = 1
                        var oVC = _mz(z, 'swiper', ['autoplay', 58, 'bindchange', 1, 'circular', 2, 'class', 3, 'data-event-opts', 4, 'duration', 5, 'indicatorDots', 6, 'interval', 7, 'vertical', 8], [], e, s, gg)
                        var lWC = _v()
                        _(oVC, lWC)
                        var aXC = function(eZC, tYC, b1C, gg) {
                            var x3C = _v()
                            _(b1C, x3C)
                            if (_oz(z, 71, eZC, tYC, gg)) {
                                x3C.wxVkey = 1
                                var o4C = _v()
                                _(x3C, o4C)
                                if (_oz(z, 72, eZC, tYC, gg)) {
                                    o4C.wxVkey = 1
                                }
                                o4C.wxXCkey = 1
                            }
                            x3C.wxXCkey = 1
                            return b1C
                        }
                        lWC.wxXCkey = 2
                        _2z(z, 69, aXC, e, s, gg, lWC, 'item', 'index', 'id')
                        _(aJC, oVC)
                    }
                    aJC.wxXCkey = 1
                    _(a6B, lIC)
                }
                var t7B = _v()
                _(o4B, t7B)
                if (_oz(z, 73, e, s, gg)) {
                    t7B.wxVkey = 1
                }
                var f5C = _n('view')
                _rz(z, f5C, 'class', 74, e, s, gg)
                var c6C = _v()
                _(f5C, c6C)
                if (_oz(z, 75, e, s, gg)) {
                    c6C.wxVkey = 1
                }
                var h7C = _v()
                _(f5C, h7C)
                if (_oz(z, 76, e, s, gg)) {
                    h7C.wxVkey = 1
                    var o8C = _mz(z, 'html-parse', ['bind:__l', 77, 'class', 1, 'html', 2, 'vueId', 3], [], e, s, gg)
                    _(h7C, o8C)
                }
                c6C.wxXCkey = 1
                h7C.wxXCkey = 1
                h7C.wxXCkey = 3
                _(o4B, f5C)
                var c9C = _mz(z, 'view', ['class', 81, 'style', 1], [], e, s, gg)
                var o0C = _v()
                _(c9C, o0C)
                if (_oz(z, 83, e, s, gg)) {
                    o0C.wxVkey = 1
                }
                var lAD = _v()
                _(c9C, lAD)
                if (_oz(z, 84, e, s, gg)) {
                    lAD.wxVkey = 1
                    var aBD = _n('view')
                    _rz(z, aBD, 'class', 85, e, s, gg)
                    var tCD = _v()
                    _(aBD, tCD)
                    if (_oz(z, 86, e, s, gg)) {
                        tCD.wxVkey = 1
                    }
                    var eDD = _v()
                    _(aBD, eDD)
                    if (_oz(z, 87, e, s, gg)) {
                        eDD.wxVkey = 1
                        var bED = _mz(z, 'view', ['bindtap', 88, 'class', 1, 'data-event-opts', 2], [], e, s, gg)
                        var oFD = _v()
                        _(bED, oFD)
                        if (_oz(z, 91, e, s, gg)) {
                            oFD.wxVkey = 1
                        }
                        oFD.wxXCkey = 1
                        _(eDD, bED)
                    }
                    tCD.wxXCkey = 1
                    eDD.wxXCkey = 1
                    _(lAD, aBD)
                }
                var xGD = _mz(z, 'view', ['bindtap', 92, 'class', 1, 'data-event-opts', 2], [], e, s, gg)
                var oHD = _v()
                _(xGD, oHD)
                if (_oz(z, 95, e, s, gg)) {
                    oHD.wxVkey = 1
                    var fID = _v()
                    _(oHD, fID)
                    if (_oz(z, 97, e, s, gg)) {
                        fID.wxVkey = 1
                        var cJD = _v()
                        _(fID, cJD)
                        if (_oz(z, 98, e, s, gg)) {
                            cJD.wxVkey = 1
                            var hKD = _v()
                            _(cJD, hKD)
                            if (_oz(z, 100, e, s, gg)) {
                                hKD.wxVkey = 1
                            } else {
                                hKD.wxVkey = 2
                                var oLD = _v()
                                _(hKD, oLD)
                                if (_oz(z, 101, e, s, gg)) {
                                    oLD.wxVkey = 1
                                } else {
                                    oLD.wxVkey = 2
                                    var cMD = _v()
                                    _(oLD, cMD)
                                    if (_oz(z, 102, e, s, gg)) {
                                        cMD.wxVkey = 1
                                    } else {
                                        cMD.wxVkey = 2
                                        var oND = _v()
                                        _(cMD, oND)
                                        if (_oz(z, 103, e, s, gg)) {
                                            oND.wxVkey = 1
                                            var lOD = _n('view')
                                            _rz(z, lOD, 'class', 104, e, s, gg)
                                            var aPD = _v()
                                            _(lOD, aPD)
                                            if (_oz(z, 105, e, s, gg)) {
                                                aPD.wxVkey = 1
                                            }
                                            var tQD = _v()
                                            _(lOD, tQD)
                                            if (_oz(z, 106, e, s, gg)) {
                                                tQD.wxVkey = 1
                                            }
                                            aPD.wxXCkey = 1
                                            tQD.wxXCkey = 1
                                            _(oND, lOD)
                                        } else {
                                            oND.wxVkey = 2
                                        }
                                        oND.wxXCkey = 1
                                    }
                                    cMD.wxXCkey = 1
                                }
                                oLD.wxXCkey = 1
                            }
                            hKD.wxXCkey = 1
                        }
                        cJD.wxXCkey = 1
                    } else {
                        fID.wxVkey = 2
                    }
                    fID.wxXCkey = 1
                } else {
                    oHD.wxVkey = 2
                }
                oHD.wxXCkey = 1
                _(c9C, xGD)
                o0C.wxXCkey = 1
                lAD.wxXCkey = 1
                _(o4B, c9C)
                var eRD = _mz(z, 'skus', ['bind:__l', 107, 'class', 1, 'data-ref', 2, 'vueId', 3], [], e, s, gg)
                _(o4B, eRD)
                l5B.wxXCkey = 1
                l5B.wxXCkey = 3
                a6B.wxXCkey = 1
                t7B.wxXCkey = 1
                _(h1B, o4B)
            }
            var bSD = _mz(z, 'xk-share', ['bind:__l', 111, 'bind:selectItem', 1, 'class', 2, 'data-event-opts', 3, 'data-ref', 4, 'pushData', 5, 'shareInfo', 6, 'vueId', 7], [], e, s, gg)
            _(cZB, bSD)
            var oTD = _mz(z, 'xk-login', ['bind:__l', 119, 'bind:success', 1, 'class', 2, 'data-event-opts', 3, 'data-ref', 4, 'vueId', 5], [], e, s, gg)
            _(cZB, oTD)
            var xUD = _mz(z, 'payment', ['allowPayType', 125, 'assistantQrcode', 1, 'bind:__l', 2, 'bind:success', 3, 'class', 4, 'data-event-opts', 5, 'data-ref', 6, 'productId', 7, 'productType', 8, 'vueId', 9], [], e, s, gg)
            _(cZB, xUD)
            var oVD = _mz(z, 'open-modal', ['bind:__l', 135, 'class', 1, 'data-ref', 2, 'vueId', 3], [], e, s, gg)
            _(cZB, oVD)
            var o2B = _v()
            _(cZB, o2B)
            if (_oz(z, 139, e, s, gg)) {
                o2B.wxVkey = 1
                var fWD = _mz(z, 'view', ['bindtap', 140, 'class', 1, 'data-event-opts', 2], [], e, s, gg)
                var cXD = _mz(z, 'view', ['catchtap', 143, 'class', 1, 'data-event-opts', 2], [], e, s, gg)
                var hYD = _mz(z, 'add-group', ['bind:__l', 146, 'class', 1, 'goodsId', 2, 'goodsType', 3, 'vueId', 4], [], e, s, gg)
                _(cXD, hYD)
                _(fWD, cXD)
                _(o2B, fWD)
            }
            var oZD = _mz(z, 'gift-list', ['bind:__l', 151, 'class', 1, 'data-ref', 2, 'vueId', 3], [], e, s, gg)
            _(cZB, oZD)
            var c1D = _mz(z, 'mp-privacy', ['bind:__l', 155, 'class', 1, 'vueId', 2], [], e, s, gg)
            _(cZB, c1D)
            h1B.wxXCkey = 1
            h1B.wxXCkey = 3
            o2B.wxXCkey = 1
            o2B.wxXCkey = 3
            _(r, cZB)
            return r
        }
        e_[x[0]] = {
            f: m0,
            j: [],
            i: [],
            ti: [],
            ic: []
        }
        if (path && e_[path]) {
            return function(env, dd, global) {
                $gwxc = 0;
                var root = {
                    "tag": "wx-page"
                };
                root.children = [];
                g = "$gwx5_XC_4";
                var main = e_[path].f
                if (typeof global === "undefined") global = {};
                global.f = $gdc(f_[path], "", 1);
                try {
                    main(env, {}, root, global);
                    _tsd(root)
                } catch (err) {
                    console.log(err)
                };
                g = "";
                return root;
            }
        }
    }
}(__g.a, __g.b, __g.c, __g.d, __g.e, __g.f, __g.g, __g.h, __g.i, __g.j, __g.k, __g.l, __g.m, __g.n, __g.o, __g.p, __g.q, __g.r, __g.s, __g.t, __g.u, __g.v, __g.w, __g.x, __g.y, __g.z, __g.A, __g.B, __g.C, __g.D, __g.E, __g.F, __g.G, __g.H, __g.I, __g.J, __g.K, __g.L, __g.M, __g.N, __g.O, __g.P, __g.Q, __g.R, __g.S, __g.T, __g.U, __g.V, __g.W, __g.X, __g.Y, __g.Z, __g.aa);
if (__vd_version_info__.delayedGwx || false) $gwx5_XC_4();
if (__vd_version_info__.delayedGwx) __wxAppCode__['pages/qbank/detail.wxml'] = [$gwx5_XC_4, './pages/qbank/detail.wxml'];
else __wxAppCode__['pages/qbank/detail.wxml'] = $gwx5_XC_4('./pages/qbank/detail.wxml');;
__wxRoute = "pages/qbank/detail";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/qbank/detail.js";
define("pages/qbank/detail.js", function(require, module, exports, window, document, frames, self, location, navigator, localStorage, history, Caches, screen, alert, confirm, prompt, XMLHttpRequest, WebSocket, Reporter, webkit, WeixinJSCore) {
    (global.webpackJsonp = global.webpackJsonp || []).push([
        ["pages/qbank/detail"], {
            "528f": function(e, t, n) {
                "use strict";
                n.r(t);
                var i = n("f5b7"),
                    a = n.n(i);
                for (var o in i)["default"].indexOf(o) < 0 && function(e) {
                    n.d(t, e, (function() {
                        return i[e]
                    }))
                }(o);
                t.default = a.a
            },
            "96dd": function(e, t, n) {},
            abc3: function(e, t, n) {
                "use strict";
                n.r(t);
                var i = n("c0b9"),
                    a = n("528f");
                for (var o in a)["default"].indexOf(o) < 0 && function(e) {
                    n.d(t, e, (function() {
                        return a[e]
                    }))
                }(o);
                n("ecb0");
                var s = n("828b"),
                    r = Object(s.a)(a.default, i.b, i.c, !1, null, "5bf1ac2e", null, !1, i.a, void 0);
                t.default = r.exports
            },
            c0b9: function(e, t, n) {
                "use strict";
                n.d(t, "b", (function() {
                    return a
                })), n.d(t, "c", (function() {
                    return o
                })), n.d(t, "a", (function() {
                    return i
                }));
                var i = {
                        payment: function() {
                            return Promise.all([n.e("common/vendor"), n.e("components/payment/payment")]).then(n.bind(null, "5d4b"))
                        }
                    },
                    a = function() {
                        var e = this,
                            t = (e.$createElement, e._self._c, e.pageReady ? e.formatImageUrl(e.qbank.originPicture, 800, 450, 1) : null),
                            n = e.pageReady ? e.formatThePrice(e.qbank.price) : null,
                            i = e.pageReady ? !e.hiddenQi && e.skus.length > 1 : null,
                            a = e.pageReady && e.qbank.marketPrice ? e.formatThePrice(e.qbank.marketPrice, 3) : null,
                            o = e.pageReady && 1 == e.showUserSwitch && e.memberHasBeenLoaded && e.allMemberCount ? e.__map(e.memberPage1List, (function(t, n) {
                                return {
                                    $orig: e.__get_orig(t),
                                    m3: n < 3 ? e.formatImageUrl(t.userInfo.smallAvatar, 200, 200, 1) : null
                                }
                            })) : null,
                            s = e.pageReady && 1 == e.showUserSwitch && e.memberHasBeenLoaded && e.allMemberCount && e.memberLoading ? e.__map(e.memberList, (function(t, n) {
                                return {
                                    $orig: e.__get_orig(t),
                                    m4: n % 2 == 0 ? e.formatImageUrl(t.userInfo.smallAvatar, 200, 200, 1) : null,
                                    m5: n % 2 == 0 ? e.shortName(t.userInfo.nickname) : null,
                                    m6: n % 2 == 0 ? e.dateFormat(t.joinTime) : null,
                                    m7: n % 2 == 0 && e.memberList[n + 1] ? e.formatImageUrl(e.memberList[n + 1].userInfo.smallAvatar, 200, 200, 1) : null,
                                    m8: n % 2 == 0 && e.memberList[n + 1] ? e.shortName(e.memberList[n + 1].userInfo.nickname) : null,
                                    m9: n % 2 == 0 && e.memberList[n + 1] ? e.dateFormat(e.memberList[n + 1].joinTime) : null
                                }
                            })) : null,
                            r = e.pageReady ? e.giftList.length : null,
                            u = !(e.pageReady && !e.isHave && e.qbank.price && e.adminConfig && e.adminConfig.loaded) || e.hiddenPrice || e.sellOut || e.isCanUseDiscount || e.isHaveTuan || e.isHaveCut || e.useCart ? null : e.allowPayType.length;
                        e._isMounted || (e.e0 = function(t) {
                            e.isShowWechatGroup = !1
                        }), e.$mp.data = Object.assign({}, {
                            $root: {
                                m0: t,
                                m1: n,
                                g0: i,
                                m2: a,
                                l0: o,
                                l1: s,
                                g1: r,
                                g2: u
                            }
                        })
                    },
                    o = []
            },
            c8ec: function(e, t, n) {
                "use strict";
                (function(e, t) {
                    var i = n("47a9");
                    n("8f74"), i(n("3240"));
                    var a = i(n("abc3"));
                    e.__webpack_require_UNI_MP_PLUGIN__ = n, t(a.default)
                }).call(this, n("3223").default, n("df3c").createPage)
            },
            ecb0: function(e, t, n) {
                "use strict";
                var i = n("96dd");
                n.n(i).a
            },
            f5b7: function(e, t, n) {
                "use strict";
                (function(e) {
                    var i = n("47a9");
                    Object.defineProperty(t, "__esModule", {
                        value: !0
                    }), t.default = void 0;
                    var a = i(n("7eb4")),
                        o = i(n("ee10")),
                        s = n("2eff"),
                        r = (n("88d2"), n("0938")),
                        u = i(n("f462")),
                        c = n("62a4"),
                        h = {
                            components: {
                                xkShare: function() {
                                    Promise.all([n.e("common/vendor"), n.e("components/share/share")]).then(function() {
                                        return resolve(n("d066"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                payment: function() {
                                    Promise.all([n.e("common/vendor"), n.e("components/payment/payment")]).then(function() {
                                        return resolve(n("5d4b"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                pintuan: function() {
                                    n.e("components/common/pintuan").then(function() {
                                        return resolve(n("45b8"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                timeDiscount: function() {
                                    n.e("components/common/time-discount").then(function() {
                                        return resolve(n("394f"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                priceCut: function() {
                                    n.e("components/common/price-cut").then(function() {
                                        return resolve(n("4153"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                openModal: function() {
                                    n.e("components/common/modal-open").then(function() {
                                        return resolve(n("8ce3"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                addGroup: function() {
                                    n.e("components/common/add-group").then(function() {
                                        return resolve(n("e22f"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                skus: function() {
                                    Promise.all([n.e("common/vendor"), n.e("components/common/skus")]).then(function() {
                                        return resolve(n("b2e6"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                htmlParse: function() {
                                    n.e("components/htmlParse/index").then(function() {
                                        return resolve(n("cdbd"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                giftList: function() {
                                    Promise.all([n.e("common/vendor"), n.e("components/common/gift-list")]).then(function() {
                                        return resolve(n("4e22"))
                                    }.bind(null, n)).catch(n.oe)
                                }
                            },
                            data: function() {
                                return {
                                    navBarHeight: this.$customBar + this.$statusBar,
                                    safeAreaBottom: this.$safeAreaBottom,
                                    pageReady: !1,
                                    qbankId: 0,
                                    qbank: {},
                                    detail: "",
                                    adminConfig: {},
                                    showKefu: !1,
                                    token: (0, c.getToken)(),
                                    isHave: !1,
                                    sellOut: !1,
                                    readLocalConfig: this.$readLocalConfig,
                                    allowPayType: [],
                                    couponCount: 0,
                                    skus: [],
                                    isShowWechatGroup: !1,
                                    pageParams: {},
                                    allMembeData: {},
                                    allMemberCount: 0,
                                    memberPage: 1,
                                    memberList: [],
                                    memberPage1List: [],
                                    memberHasBeenLoaded: !1,
                                    memberLoading: !1,
                                    memberAutoplay: !0,
                                    shareMenuPushData: [],
                                    giftList: [],
                                    vipShake: !1
                                }
                            },
                            computed: {
                                cartGoodsNum: function() {
                                    return this.$store.state.cartGoods.length
                                },
                                useCart: function() {
                                    return 1 == this.$store.state.useCart && this.allowPayType.findIndex((function(e) {
                                        return "pay" == e
                                    })) > -1
                                },
                                hiddenPrice: function() {
                                    return "" === this.$api.formatThePrice(1, 3)
                                },
                                hiddenQi: function() {
                                    return "" === this.$api.formatThePrice(1, 3) || null === this.$api.formatThePrice(1, 3)
                                },
                                userInfo: function() {
                                    return this.$store.state.userInfo
                                },
                                shareInfo: function() {
                                    if (this.qbank.id) {
                                        var e = "/pages/qbank/detail?id=" + this.qbank.id;
                                        return this.userInfo.agentInfo && this.userInfo.agentInfo.userId ? e += "&agentId=".concat(this.userInfo.agentInfo.userId) : this.qbank.resellerId && (e += "&resellerId=".concat(this.qbank.resellerId)), {
                                            title: this.qbank.title,
                                            summary: this.qbank.description || "点击跟我一起学习",
                                            link: e,
                                            imageUrl: this.formatImageUrl(this.qbank.smallPicture || this.qbank.originPicture, 750, 600, 1)
                                        }
                                    }
                                    return {}
                                },
                                isHaveTuan: function() {
                                    var e = !1;
                                    if (this.qbank.activity && this.qbank.activity.joinbuy) {
                                        var t = +new Date,
                                            n = this.qbank.activity.joinbuy.endTime;
                                        n && t < n && (e = !0)
                                    }
                                    return e
                                },
                                isHaveDiscount: function() {
                                    var e = !1;
                                    if (this.qbank.activity && this.qbank.activity.discount) {
                                        var t = +new Date,
                                            n = (this.qbank.activity.discount.startTime, this.qbank.activity.discount.endTime),
                                            i = this.qbank.activity.discount.discountSetType || 1;
                                        (n && t < n || 2 == i) && (e = !0)
                                    }
                                    return e
                                },
                                isHaveXuDiscount: function() {
                                    var e = !1;
                                    if (this.qbank.activity && this.qbank.activity.discount) {
                                        new Date;
                                        var t = this.qbank.activity.discount.startTime,
                                            n = this.qbank.activity.discount.endTime,
                                            i = this.qbank.activity.discount.discountSetType || 1;
                                        0 == t && 0 == n && 2 == i && (e = !0)
                                    }
                                    return e
                                },
                                isHaveCut: function() {
                                    var e = !1;
                                    if (this.qbank.activity && this.qbank.activity.price_cut) {
                                        var t = +new Date,
                                            n = (this.qbank.activity.price_cut.startTime, this.qbank.activity.price_cut.endTime);
                                        n && t < n && (e = !0)
                                    }
                                    return e
                                },
                                isShowActivity: function() {
                                    return !!!(this.isHave || this.isAgentCoupon || this.hiddenPrice || this.sellOut) && (this.isHaveTuan || this.isHaveDiscount || this.isHaveCut)
                                },
                                isCanUseDiscount: function() {
                                    var e = !1;
                                    if (this.qbank.activity && this.qbank.activity.discount) {
                                        var t = +new Date,
                                            n = this.qbank.activity.discount,
                                            i = n.startTime,
                                            a = n.endTime,
                                            o = 0 == n.count || n.stock > 0,
                                            s = this.qbank.activity.discount.discountSetType || 1;
                                        (t > i && t < a || 2 == s) && o && (e = !0)
                                    }
                                    return e
                                },
                                isHaveWechatGroup: function() {
                                    var e = !1;
                                    if (this.qbank.optional && this.qbank.optional.hook) {
                                        var t = this.qbank.optional.hook.anchor || 0;
                                        e = 1 == t || 3 == t
                                    }
                                    return e
                                },
                                addWechatGroupHook: function() {
                                    var e = 0;
                                    return this.qbank.optional && this.qbank.optional.hook && (e = this.qbank.optional.hook.anchor || 0), e
                                },
                                beforeJoinShowWechatGroup: function() {
                                    var e = !1;
                                    return this.qbank.optional && this.qbank.optional.hook && (e = 1 == this.qbank.optional.hook.beforeJoinShow), e
                                },
                                showUserSwitch: function() {
                                    return "ios" == e.getSystemInfoSync().platform ? 0 : this.qbank.optional && this.qbank.optional.showUser && this.qbank.optional.showUser.switch || 0
                                },
                                showBuyVip: function() {
                                    return "ios" != e.getSystemInfoSync().platform && !(!this.qbank.vipInfo || !this.qbank.vipInfo.id || this.isHave)
                                },
                                assistantQrcode: function() {
                                    return this.qbank.optional && this.qbank.optional.assistantQrcode ? this.qbank.optional.assistantQrcode : ""
                                }
                            },
                            onLoad: function(e) {
                                if (!e.id) return this.$api.error("缺少参数id");
                                this.pageParams = e, this.qbankId = e.id, this.inviterId = e.inviterId || 0, this.getQbank()
                            },
                            onShow: function() {
                                this.getActivityInfo(), this.memberAutoplay = !0
                            },
                            onHide: function() {
                                this.memberAutoplay = !1
                            },
                            onShareAppMessage: function() {
                                return {
                                    title: this.shareInfo.title,
                                    path: this.shareInfo.link,
                                    imageUrl: this.shareInfo.imageUrl
                                }
                            },
                            onShareTimeline: function() {
                                return {
                                    title: this.shareInfo.title,
                                    query: this.shareInfo.link.split("?")[1] || "",
                                    imageUrl: this.shareInfo.imageUrl
                                }
                            },
                            filters: {
                                toFixed: function(e) {
                                    var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 2;
                                    return e.toFixed(t)
                                }
                            },
                            methods: {
                                addEventListener: function(e, t) {
                                    "paymented" == e && "questions" == t.type && this.paymentSuccess()
                                },
                                shortName: function(e) {
                                    return e ? e.replace(/^([\w\W])[\w\W]*([\w\W])$/, "$1****$2") : "******"
                                },
                                dateFormat: function(e) {
                                    return (0, r.dateFormat2)(e)
                                },
                                sharePage: function() {
                                    this.qbank.id && (this.qbank.price && this.qbank.resellerId && (this.shareMenuPushData = [{
                                        type: "shareToMoeny",
                                        imgUrl: "https://js.cdn.ixunke.com/v3/app/imgcdn/menu/money.png",
                                        text: "分享赚钱",
                                        badge: "推荐"
                                    }]), this.$refs.shareMenu.open())
                                },
                                clickShareMenuItem: function(e) {
                                    "shareToMoeny" == e && this.intercept({
                                        methods: "goShareCard"
                                    })
                                },
                                goShareCard: function() {
                                    e.setStorageSync("resellerShareProInfo", {
                                        thumb: this.qbank.originPicture,
                                        title: this.qbank.title
                                    }), this.$api.openWin({
                                        url: "/pages/reseller/card",
                                        params: {
                                            type: "product",
                                            goodsType: "qBank",
                                            goodsId: this.qbank.id
                                        }
                                    })
                                },
                                pintuanSuccessCallback: function() {
                                    this.pintuanSuccessHaveCallback || (this.pintuanSuccessHaveCallback = !0, this.getQbank())
                                },
                                recordInviterId: function() {
                                    this.token && this.inviterId && this.qbankId && this.$http({
                                        method: "POST",
                                        url: "/api/user/record_invite",
                                        data: {
                                            note: "questions",
                                            id: this.qbankId,
                                            userId: this.inviterId
                                        },
                                        token: 1
                                    })
                                },
                                getQbank: function() {
                                    var t = this;
                                    return (0, o.default)(a.default.mark((function n() {
                                        var i, o, u, h, d, l, f, m, p, g;
                                        return a.default.wrap((function(n) {
                                            for (;;) switch (n.prev = n.next) {
                                                case 0:
                                                    return t.token = (0, c.getToken)(), t.$api.showLoading(), n.next = 4, t.$http({
                                                        url: "/api/q_bank",
                                                        data: {
                                                            id: t.qbankId
                                                        }
                                                    });
                                                case 4:
                                                    if (i = n.sent, t.$api.hideLoading(), t.recordInviterId(), t.pageReady = !0, 0 !== i.errno) {
                                                        n.next = 39;
                                                        break
                                                    }
                                                    return i.data.goodsText = "题库", t.qbank = i.data, t.isHave = i.data.isAuthorized, t.pluginArr = t.qbank.pluginArr || [], t.detail = (0, r.formatRichText)(t.qbank.content, {
                                                        pBottom: 0,
                                                        fontSize: 15,
                                                        imgBlock: !0
                                                    }), o = i.data.qBankSku || [], u = Date.now(), (o = o.filter((function(e) {
                                                        return !(e.learnDeadline && e.learnDeadline < u)
                                                    }))).length ? t.readLocalConfig ? (o.forEach((function(e) {
                                                        0 != e.expiryDay && 0 == e.learnDeadline && (e.learnDeadline = Date.now() + 24 * e.expiryDay * 60 * 60 * 1e3)
                                                    })), 0 == (o = o.sort((function(e, t) {
                                                        return e.learnDeadline - t.learnDeadline
                                                    })))[0].learnDeadline ? t.skus = [o[0]] : t.skus = [o[o.length - 1]]) : t.skus = o.sort((function(e, t) {
                                                        return e.price - t.price
                                                    })) : t.skus = [{
                                                        expiryDay: 0,
                                                        price: i.data.price
                                                    }], t.qbank.price = t.skus[0].price, n.next = 21, (0, s._getAdminConfig)();
                                                case 21:
                                                    return t.adminConfig = n.sent, n.next = 24, (0, s._getAboutApp)();
                                                case 24:
                                                    h = n.sent, d = h.kefuConfig, t.showKefu = 1 == d.kefuSwitch && d.kefuArr.length, l = [], l = t.qbank.optional ? t.qbank.optional.allowPayType || [] : ["pay", "key", "password"], t.adminConfig.payThreeUrl && (l = ["pay"]), "ios" == e.getSystemInfoSync().platform && ((f = l.findIndex((function(e) {
                                                        return "pay" == e
                                                    }))) > -1 && l.splice(f, 1)), t.adminConfig.paySwitch || (m = l.findIndex((function(e) {
                                                        return "pay" == e
                                                    }))) > -1 && l.splice(m, 1), t.allowPayType = l, t.isHave && t.needCheckWechatGroupForJoin && (t.needCheckWechatGroupForJoin = !1, 2 != t.addWechatGroupHook && 3 != t.addWechatGroupHook || (t.isShowWechatGroup = !0)), t.qbank.marketStrategy && (p = t.qbank.marketStrategy.gift || [], g = t.qbank.marketStrategy.virtualGift || [], t.giftList = p.concat(g)), t.getActivityInfo(), t.getMembers(), n.next = 40;
                                                    break;
                                                case 39:
                                                    100143 === i.errno && (t.token = 0, t.goLogin());
                                                case 40:
                                                case "end":
                                                    return n.stop()
                                            }
                                        }), n)
                                    })))()
                                },
                                openGiftList: function(e) {
                                    this.$refs.giftList && this.$refs.giftList.open({
                                        list: e || this.giftList
                                    })
                                },
                                getMembers: function() {
                                    var e = this;
                                    if (0 != this.showUserSwitch && !this.qbank.isAuthorized) {
                                        if (this.memberLoading = !1, this.allMembeData["page" + this.memberPage]) return this.memberList = this.allMembeData["page" + this.memberPage], void(this.memberLoading = !0);
                                        this.$http({
                                            url: "/api/user/goods_user_list",
                                            data: {
                                                goodsId: this.qbank.id,
                                                goodsType: "qBank",
                                                page: this.memberPage,
                                                pageSize: 20
                                            }
                                        }).then((function(t) {
                                            if (0 == t.errno) {
                                                t.data.forEach((function(e) {
                                                    e.userInfo = e.userInfo || {}
                                                })), e.allMemberCount = t.count;
                                                var n = JSON.stringify(t.data);
                                                1 == t.currentPage && (e.memberPage1List = JSON.parse(n)), e.totalPages = t.totalPages, e.memberList = t.data, e.memberLoading = !0, e.memberHasBeenLoaded = !0, e.allMembeData["page" + e.memberPage] = JSON.parse(n)
                                            }
                                        }))
                                    }
                                },
                                memberSwiperChange: function(e) {
                                    0 == e.detail.current && 1 != this.totalPages && (this.memberPage == this.totalPages ? this.memberPage = 1 : this.memberPage++, this.getMembers())
                                },
                                goGoodsMember: function() {
                                    this.$api.openWin({
                                        url: "/pages/system/goods_member",
                                        params: {
                                            type: "qBank",
                                            id: this.qbank.id
                                        }
                                    })
                                },
                                getActivityInfo: function() {
                                    var e = this;
                                    this.isHaveTuan && this.$nextTick((function() {
                                        e.$refs.tuan && e.$refs.tuan.getInfo()
                                    })), this.isHaveCut && this.$nextTick((function() {
                                        e.$refs.priceCut && e.$refs.priceCut.getInfo()
                                    }))
                                },
                                goPinTuan: function() {
                                    this.$refs.tuan && this.$refs.tuan.clickButton()
                                },
                                goKanjia: function() {
                                    this.$refs.priceCut && this.$refs.priceCut.clickButton()
                                },
                                getUserSelectSku: function() {
                                    var e = this;
                                    return new Promise((function(t, n) {
                                        e.$refs.skus.open({
                                            skus: e.skus,
                                            thumb: e.qbank.originPicture,
                                            success: function(e) {
                                                t(e.id)
                                            }
                                        })
                                    }))
                                },
                                joinCart: function() {
                                    var e = this;
                                    return (0, o.default)(a.default.mark((function t() {
                                        var n;
                                        return a.default.wrap((function(t) {
                                            for (;;) switch (t.prev = t.next) {
                                                case 0:
                                                    if (n = {
                                                            goodsId: e.qbank.id,
                                                            goodsType: "q_bank",
                                                            num: 1
                                                        }, !(e.skus.length > 1)) {
                                                        t.next = 11;
                                                        break
                                                    }
                                                    if (!e.hiddenPrice) {
                                                        t.next = 6;
                                                        break
                                                    }
                                                    n.skuId = e.skus[0].id, t.next = 9;
                                                    break;
                                                case 6:
                                                    return t.next = 8, e.getUserSelectSku();
                                                case 8:
                                                    n.skuId = t.sent;
                                                case 9:
                                                    t.next = 12;
                                                    break;
                                                case 11:
                                                    1 == e.skus.length && e.skus[0].id && (n.skuId = e.skus[0].id);
                                                case 12:
                                                    e.$api.showLoading(), e.$http({
                                                        url: "/api/cart/push",
                                                        data: n,
                                                        method: "POST",
                                                        token: 1
                                                    }).then((function(t) {
                                                        e.$api.hideLoading(), 0 === t.errno && (e.$api.toast("已成功加入购物车"), e.$store.dispatch("getCartGoods"))
                                                    }));
                                                case 14:
                                                case "end":
                                                    return t.stop()
                                            }
                                        }), t)
                                    })))()
                                },
                                goKefu: function() {
                                    e.navigateTo({
                                        url: "/pages/user/kefu"
                                    })
                                },
                                goCart: function() {
                                    e.navigateTo({
                                        url: "/pages/sale/cart"
                                    })
                                },
                                goVip: function() {
                                    e.navigateTo({
                                        url: "/pages/user/vip?id=" + this.qbank.vipInfo.id
                                    })
                                },
                                goLogin: function() {
                                    var e = this;
                                    1154 != this.$store.state.launchOptions.scene ? (0, u.default)({
                                        component: !0,
                                        openComponent: function(t) {
                                            t.reload ? e.getQbank() : e.$refs.login.show()
                                        },
                                        success: function() {
                                            e.loginSuccess()
                                        }
                                    }) : this.$api.toast("请前往小程序使用完整服务")
                                },
                                loginSuccess: function() {
                                    this.getQbank()
                                },
                                intercept: function(e) {
                                    this.token ? "function" == typeof this[e.methods] ? this[e.methods](e) : console.error("错误：" + e.methods + "必须是function") : this.goLogin()
                                },
                                interceptPhone: function(t) {
                                    var n = this,
                                        i = (e.getStorageSync("userInfo") || {}).phone;
                                    this.adminConfig && this.adminConfig.needPhone && !i ? this.$refs.openModal.open({
                                        content: this.adminConfig.needPhoneTip,
                                        success: function(t) {
                                            t.confirm && (t.errMsg && t.errMsg.indexOf("getPhoneNumber") > -1 ? n.bindWechatPhone(t) : e.navigateTo({
                                                url: "/pages/user/phone"
                                            }))
                                        }
                                    }) : t()
                                },
                                addWechatGroup: function() {
                                    this.isHave || this.beforeJoinShowWechatGroup ? this.isShowWechatGroup = !0 : this.$api.toast("开通题库后才可以加群哦")
                                },
                                clickBottomButton: function() {
                                    var e = this,
                                        t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
                                    this.interceptPhone((function() {
                                        if (e.isHave && !t.renew) e.goQbankIndex();
                                        else if (e.needGoRenew = t.renew, e.qbank.price)
                                            if (e.sellOut) e.$api.toast("已售罄，暂无法购买");
                                            else {
                                                var n = e.allowPayType;
                                                0 == n.length ? e.showBuyVip ? setTimeout((function() {
                                                    e.vipShake = !0, e.vipShakeTimer || (e.vipShakeTimer = setTimeout((function() {
                                                        e.vipShake = !1, clearTimeout(e.vipShakeTimer), e.vipShakeTimer = null
                                                    }), 3e3))
                                                }), 10) : e.$api.toast(e.isMpios ? "暂不支持加入" : "暂不支持购买") : 1 == n.length ? "password" == n[0] ? e.$refs.payment.joinProductByPassword() : "key" == n[0] ? e.$refs.payment.joinProductByKey() : "assistant" == n[0] && e.assistantQrcode ? e.$refs.payment.joinProductByAssistant() : e.goBuy() : n.includes("pay") ? e.goBuy() : e.$refs.payment.open()
                                            } else e.joinByFace()
                                    }))
                                },
                                goQbankIndex: function() {
                                    var e = this.qbank.pId || this.qbank.id;
                                    this.isHave ? this.$api.openWin({
                                        type: "reLaunch",
                                        url: "/pages/index",
                                        params: {
                                            tabType: "qbank",
                                            qbankId: e
                                        }
                                    }) : this.$api.openWin({
                                        url: "/pages/qbank/index",
                                        params: {
                                            id: e
                                        }
                                    })
                                },
                                goBuy: function() {
                                    var e = {
                                        type: "questions",
                                        id: this.qbankId
                                    };
                                    this.needGoRenew && (e.renew = 1, this.needGoRenew = !1), this.pageParams.roomId && (e.roomId = this.pageParams.roomId), this.$api.openWin({
                                        url: "/pages/buy/index",
                                        params: e
                                    })
                                },
                                paymentSuccess: function() {
                                    this.needCheckWechatGroupForJoin = !0, this.getQbank()
                                },
                                joinByFace: function() {
                                    var e = this;
                                    this.$api.showLoading(), this.$http({
                                        methods: "POST",
                                        url: "/api/questions_member",
                                        data: {
                                            qBankId: this.qbank.id
                                        }
                                    }).then((function(t) {
                                        e.$api.hideLoading(), 0 === t.errno && (e.$api.toast("报名成功"), e.getQbank())
                                    }))
                                },
                                formatThePrice: function() {
                                    return this.$api.formatThePrice(arguments[0], arguments[1])
                                },
                                formatImageUrl: function(e, t, n, i) {
                                    return this.$api.formatImageUrl(e, t, n, i)
                                }
                            }
                        };
                    t.default = h
                }).call(this, n("df3c").default)
            }
        },
        [
            ["c8ec", "common/runtime", "common/vendor"]
        ]
    ]);
}, {
    isPage: true,
    isComponent: true,
    currentFile: 'pages/qbank/detail.js'
});
require("pages/qbank/detail.js");