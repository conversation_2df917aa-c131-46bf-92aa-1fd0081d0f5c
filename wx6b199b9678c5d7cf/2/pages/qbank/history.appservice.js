$gwx5_XC_5 = function(_, _v, _n, _p, _s, _wp, _wl, $gwn, $gwl, $gwh, wh, $gstack, $gwrt, gra, grb, TestTest, wfor, _ca, _da, _r, _rz, _o, _oz, _1, _1z, _2, _2z, _m, _mz, nv_getDate, nv_getRegExp, nv_console, nv_parseInt, nv_parseFloat, nv_isNaN, nv_isFinite, nv_decodeURI, nv_decodeURIComponent, nv_encodeURI, nv_encodeURIComponent, $gdc, nv_JSON, _af, _gv, _ai, _grp, _gd, _gapi, $ixc, _ic, _w, _ev, _tsd) {
    return function(path, global) {
        if (typeof global === 'undefined') {
            if (typeof __GWX_GLOBAL__ === 'undefined') global = {};
            else global = __GWX_GLOBAL__;
        }
        if (typeof __WXML_GLOBAL__ === 'undefined') {
            __WXML_GLOBAL__ = {};
        }
        __WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
        var e_ = {}
        if (typeof(global.entrys) === 'undefined') global.entrys = {};
        e_ = global.entrys;
        var d_ = {}
        if (typeof(global.defines) === 'undefined') global.defines = {};
        d_ = global.defines;
        var f_ = {}
        if (typeof(global.modules) === 'undefined') global.modules = {};
        f_ = global.modules || {};
        var p_ = {}
        __WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
        __WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
        __WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
        var z = __WXML_GLOBAL__.ops_set.$gwx5_XC_5 || [];

        function gz$gwx5_XC_5_1() {
            if (__WXML_GLOBAL__.ops_cached.$gwx5_XC_5_1) return __WXML_GLOBAL__.ops_cached.$gwx5_XC_5_1
            __WXML_GLOBAL__.ops_cached.$gwx5_XC_5_1 = [];
            (function(z) {
                var a = 11;

                function Z(ops) {
                    z.push(ops)
                }
                Z([
                    [7],
                    [3, 'themeColor']
                ])
                Z([3, '__l'])
                Z([
                    [7],
                    [3, 'pageTitle']
                ])
                Z([3, 'b8dd77c6-1'])
                Z([
                    [7],
                    [3, 'dataHasBeenLoaded']
                ])
                Z([3, 'history-wrap'])
                Z([3, 'index'])
                Z([3, 'item'])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'l1']
                ])
                Z(z[6])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'item']
                    ],
                    [3, 'g0']
                ])
                Z([3, 'jndex'])
                Z([3, 'itemName'])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'item']
                    ],
                    [3, 'l0']
                ])
                Z(z[11])
                Z([3, 'ixunke_unitBox'])
                Z([
                    [2, '=='],
                    [
                        [6],
                        [
                            [6],
                            [
                                [7],
                                [3, 'itemName']
                            ],
                            [3, '$orig']
                        ],
                        [3, 'type']
                    ],
                    [1, 1]
                ])
                Z([3, '__e'])
                Z([3, 'ixunke_unit'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, 'tap']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [
                                                    [5],
                                                    [
                                                        [5],
                                                        [1, 'goHistory']
                                                    ],
                                                    [
                                                        [4],
                                                        [
                                                            [5],
                                                            [1, '$0']
                                                        ]
                                                    ]
                                                ],
                                                [
                                                    [4],
                                                    [
                                                        [5],
                                                        [
                                                            [4],
                                                            [
                                                                [5],
                                                                [
                                                                    [5],
                                                                    [
                                                                        [4],
                                                                        [
                                                                            [5],
                                                                            [
                                                                                [5],
                                                                                [
                                                                                    [5],
                                                                                    [1, 'historyInfo']
                                                                                ],
                                                                                [1, '']
                                                                            ],
                                                                            [
                                                                                [7],
                                                                                [3, 'index']
                                                                            ]
                                                                        ]
                                                                    ]
                                                                ],
                                                                [
                                                                    [4],
                                                                    [
                                                                        [5],
                                                                        [
                                                                            [5],
                                                                            [
                                                                                [5],
                                                                                [1, 'historys']
                                                                            ],
                                                                            [1, '']
                                                                        ],
                                                                        [
                                                                            [7],
                                                                            [3, 'jndex']
                                                                        ]
                                                                    ]
                                                                ]
                                                            ]
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([
                    [2, '>'],
                    [
                        [6],
                        [
                            [7],
                            [3, 'itemName']
                        ],
                        [3, 'g1']
                    ],
                    [1, 0]
                ])
                Z([
                    [2, '>'],
                    [
                        [6],
                        [
                            [7],
                            [3, 'itemName']
                        ],
                        [3, 'g2']
                    ],
                    [1, 1]
                ])
                Z([
                    [6],
                    [
                        [6],
                        [
                            [7],
                            [3, 'itemName']
                        ],
                        [3, '$orig']
                    ],
                    [3, 'questionCount']
                ])
                Z([
                    [2, '=='],
                    [
                        [6],
                        [
                            [6],
                            [
                                [7],
                                [3, 'itemName']
                            ],
                            [3, '$orig']
                        ],
                        [3, 'type']
                    ],
                    [1, 2]
                ])
                Z(z[17])
                Z(z[18])
                Z(z[19])
                Z([3, 'ixunke_unitInfo'])
                Z([
                    [2, '=='],
                    [
                        [6],
                        [
                            [6],
                            [
                                [7],
                                [3, 'itemName']
                            ],
                            [3, '$orig']
                        ],
                        [3, 'status']
                    ],
                    [1, 0]
                ])
                Z([
                    [2, '=='],
                    [
                        [6],
                        [
                            [6],
                            [
                                [7],
                                [3, 'itemName']
                            ],
                            [3, '$orig']
                        ],
                        [3, 'status']
                    ],
                    [1, 1]
                ])
                Z([
                    [2, '=='],
                    [
                        [6],
                        [
                            [6],
                            [
                                [7],
                                [3, 'itemName']
                            ],
                            [3, '$orig']
                        ],
                        [3, 'status']
                    ],
                    [1, 2]
                ])
                Z([
                    [2, '=='],
                    [
                        [7],
                        [3, 'length']
                    ],
                    [1, 0]
                ])
                Z(z[1])
                Z([3, '暂无练习历史'])
                Z([3, 'empty2'])
                Z([3, 'b8dd77c6-2'])
                Z(z[1])
                Z([3, 'vue-ref'])
                Z([3, 'paperModal'])
                Z([3, 'b8dd77c6-3'])
            })(__WXML_GLOBAL__.ops_cached.$gwx5_XC_5_1);
            return __WXML_GLOBAL__.ops_cached.$gwx5_XC_5_1
        }
        __WXML_GLOBAL__.ops_set.$gwx5_XC_5 = z;
        __WXML_GLOBAL__.ops_init.$gwx5_XC_5 = true;
        var x = ['./pages/qbank/history.wxml'];
        d_[x[0]] = {}
        var m0 = function(e, s, r, gg) {
            var z = gz$gwx5_XC_5_1()
            var l3D = _n('view')
            var a4D = _v()
            _(l3D, a4D)
            if (_oz(z, 0, e, s, gg)) {
                a4D.wxVkey = 1
                var e6D = _mz(z, 'nav-bar', ['bind:__l', 1, 'title', 1, 'vueId', 2], [], e, s, gg)
                _(a4D, e6D)
            }
            var t5D = _v()
            _(l3D, t5D)
            if (_oz(z, 4, e, s, gg)) {
                t5D.wxVkey = 1
                var b7D = _n('view')
                _rz(z, b7D, 'class', 5, e, s, gg)
                var x9D = _v()
                _(b7D, x9D)
                var o0D = function(cBE, fAE, hCE, gg) {
                    var cEE = _v()
                    _(hCE, cEE)
                    if (_oz(z, 10, cBE, fAE, gg)) {
                        cEE.wxVkey = 1
                        var oFE = _v()
                        _(cEE, oFE)
                        var lGE = function(tIE, aHE, eJE, gg) {
                            var oLE = _n('view')
                            _rz(z, oLE, 'class', 15, tIE, aHE, gg)
                            var xME = _v()
                            _(oLE, xME)
                            if (_oz(z, 16, tIE, aHE, gg)) {
                                xME.wxVkey = 1
                                var oNE = _mz(z, 'view', ['bindtap', 17, 'class', 1, 'data-event-opts', 2], [], tIE, aHE, gg)
                                var cPE = _n('view')
                                var hQE = _v()
                                _(cPE, hQE)
                                if (_oz(z, 20, tIE, aHE, gg)) {
                                    hQE.wxVkey = 1
                                }
                                var oRE = _v()
                                _(cPE, oRE)
                                if (_oz(z, 21, tIE, aHE, gg)) {
                                    oRE.wxVkey = 1
                                }
                                hQE.wxXCkey = 1
                                oRE.wxXCkey = 1
                                _(oNE, cPE)
                                var fOE = _v()
                                _(oNE, fOE)
                                if (_oz(z, 22, tIE, aHE, gg)) {
                                    fOE.wxVkey = 1
                                }
                                fOE.wxXCkey = 1
                                _(xME, oNE)
                            } else {
                                xME.wxVkey = 2
                                var cSE = _v()
                                _(xME, cSE)
                                if (_oz(z, 23, tIE, aHE, gg)) {
                                    cSE.wxVkey = 1
                                    var oTE = _mz(z, 'view', ['bindtap', 24, 'class', 1, 'data-event-opts', 2], [], tIE, aHE, gg)
                                    var lUE = _n('view')
                                    _rz(z, lUE, 'class', 27, tIE, aHE, gg)
                                    var aVE = _v()
                                    _(lUE, aVE)
                                    if (_oz(z, 28, tIE, aHE, gg)) {
                                        aVE.wxVkey = 1
                                    } else {
                                        aVE.wxVkey = 2
                                        var tWE = _v()
                                        _(aVE, tWE)
                                        if (_oz(z, 29, tIE, aHE, gg)) {
                                            tWE.wxVkey = 1
                                        } else {
                                            tWE.wxVkey = 2
                                            var eXE = _v()
                                            _(tWE, eXE)
                                            if (_oz(z, 30, tIE, aHE, gg)) {
                                                eXE.wxVkey = 1
                                            }
                                            eXE.wxXCkey = 1
                                        }
                                        tWE.wxXCkey = 1
                                    }
                                    aVE.wxXCkey = 1
                                    _(oTE, lUE)
                                    _(cSE, oTE)
                                }
                                cSE.wxXCkey = 1
                            }
                            xME.wxXCkey = 1
                            _(eJE, oLE)
                            return eJE
                        }
                        oFE.wxXCkey = 2
                        _2z(z, 13, lGE, cBE, fAE, gg, oFE, 'itemName', 'jndex', 'jndex')
                    }
                    cEE.wxXCkey = 1
                    return hCE
                }
                x9D.wxXCkey = 2
                _2z(z, 8, o0D, e, s, gg, x9D, 'item', 'index', 'index')
                var o8D = _v()
                _(b7D, o8D)
                if (_oz(z, 31, e, s, gg)) {
                    o8D.wxVkey = 1
                    var bYE = _mz(z, 'xk-empty', ['bind:__l', 32, 'text', 1, 'type', 2, 'vueId', 3], [], e, s, gg)
                    _(o8D, bYE)
                }
                var oZE = _mz(z, 'paper-modal', ['bind:__l', 36, 'class', 1, 'data-ref', 2, 'vueId', 3], [], e, s, gg)
                _(b7D, oZE)
                o8D.wxXCkey = 1
                o8D.wxXCkey = 3
                _(t5D, b7D)
            }
            a4D.wxXCkey = 1
            a4D.wxXCkey = 3
            t5D.wxXCkey = 1
            t5D.wxXCkey = 3
            _(r, l3D)
            return r
        }
        e_[x[0]] = {
            f: m0,
            j: [],
            i: [],
            ti: [],
            ic: []
        }
        if (path && e_[path]) {
            return function(env, dd, global) {
                $gwxc = 0;
                var root = {
                    "tag": "wx-page"
                };
                root.children = [];
                g = "$gwx5_XC_5";
                var main = e_[path].f
                if (typeof global === "undefined") global = {};
                global.f = $gdc(f_[path], "", 1);
                try {
                    main(env, {}, root, global);
                    _tsd(root)
                } catch (err) {
                    console.log(err)
                };
                g = "";
                return root;
            }
        }
    }
}(__g.a, __g.b, __g.c, __g.d, __g.e, __g.f, __g.g, __g.h, __g.i, __g.j, __g.k, __g.l, __g.m, __g.n, __g.o, __g.p, __g.q, __g.r, __g.s, __g.t, __g.u, __g.v, __g.w, __g.x, __g.y, __g.z, __g.A, __g.B, __g.C, __g.D, __g.E, __g.F, __g.G, __g.H, __g.I, __g.J, __g.K, __g.L, __g.M, __g.N, __g.O, __g.P, __g.Q, __g.R, __g.S, __g.T, __g.U, __g.V, __g.W, __g.X, __g.Y, __g.Z, __g.aa);
if (__vd_version_info__.delayedGwx || false) $gwx5_XC_5();
if (__vd_version_info__.delayedGwx) __wxAppCode__['pages/qbank/history.wxml'] = [$gwx5_XC_5, './pages/qbank/history.wxml'];
else __wxAppCode__['pages/qbank/history.wxml'] = $gwx5_XC_5('./pages/qbank/history.wxml');;
__wxRoute = "pages/qbank/history";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/qbank/history.js";
define("pages/qbank/history.js", function(require, module, exports, window, document, frames, self, location, navigator, localStorage, history, Caches, screen, alert, confirm, prompt, XMLHttpRequest, WebSocket, Reporter, webkit, WeixinJSCore) {
    (global.webpackJsonp = global.webpackJsonp || []).push([
        ["pages/qbank/history"], {
            "00be": function(t, e, n) {
                "use strict";
                var a = n("47a9");
                Object.defineProperty(e, "__esModule", {
                    value: !0
                }), e.default = void 0;
                var r = a(n("7eb4")),
                    o = a(n("7ca3")),
                    i = a(n("ee10")),
                    c = a(n("e6fb")),
                    u = n("0938"),
                    s = n("2eff");

                function f(t, e) {
                    var n = Object.keys(t);
                    if (Object.getOwnPropertySymbols) {
                        var a = Object.getOwnPropertySymbols(t);
                        e && (a = a.filter((function(e) {
                            return Object.getOwnPropertyDescriptor(t, e).enumerable
                        }))), n.push.apply(n, a)
                    }
                    return n
                }

                function d(t) {
                    for (var e = 1; e < arguments.length; e++) {
                        var n = null != arguments[e] ? arguments[e] : {};
                        e % 2 ? f(Object(n), !0).forEach((function(e) {
                            (0, o.default)(t, e, n[e])
                        })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(n)) : f(Object(n)).forEach((function(e) {
                            Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(n, e))
                        }))
                    }
                    return t
                }
                var p = {
                    components: {
                        paperModal: function() {
                            Promise.all([n.e("common/vendor"), n.e("components/qbank/paper-modal")]).then(function() {
                                return resolve(n("83b0"))
                            }.bind(null, n)).catch(n.oe)
                        }
                    },
                    data: function() {
                        return {
                            pageTitle: "",
                            dataHasBeenLoaded: !1,
                            length: 0,
                            historyInfo: [],
                            showEmpty: !1
                        }
                    },
                    onLoad: function() {
                        this.getHistoryInfo(), this.getPageTitle()
                    },
                    filters: {
                        dateFormat: function(t) {
                            return (0, u.dateFormat)(t, "yyyy-MM-dd hh:mm")
                        }
                    },
                    methods: {
                        addEventListener: function(t, e) {
                            "updateQbank" == t && this.getHistoryInfo()
                        },
                        getPageTitle: function() {
                            var t = this;
                            (0, s._getAppConfig)(1).then((function(e) {
                                if (0 === e.errno) {
                                    var n = (e.data.qbankLayout || []).find((function(t) {
                                        return "icon" == t.type
                                    })).data.find((function(t) {
                                        return "练习历史" == t.name
                                    }));
                                    t.pageTitle = n ? n.alias || n.name : "练习历史"
                                }
                            }))
                        },
                        getHistoryInfo: function() {
                            var t = this;
                            return (0, i.default)(r.default.mark((function e() {
                                var n, a, o;
                                return r.default.wrap((function(e) {
                                    for (;;) switch (e.prev = e.next) {
                                        case 0:
                                            return t.$api.showLoading(), e.next = 3, (0, c.default)({
                                                method: "GET",
                                                path: "/api/history",
                                                params: {},
                                                checkToken: 1
                                            });
                                        case 3:
                                            if (n = e.sent, t.$api.hideLoading(), t.dataHasBeenLoaded = !0, 0 !== n.errno) {
                                                e.next = 17;
                                                break
                                            }
                                            a = n.data, o = 0;
                                        case 9:
                                            if (!(o < a.length)) {
                                                e.next = 16;
                                                break
                                            }
                                            if (!(a[o].historys.length > 0)) {
                                                e.next = 13;
                                                break
                                            }
                                            return t.length = !0, e.abrupt("break", 16);
                                        case 13:
                                            o++, e.next = 9;
                                            break;
                                        case 16:
                                            t.historyInfo = a;
                                        case 17:
                                        case "end":
                                            return e.stop()
                                    }
                                }), e)
                            })))()
                        },
                        goHistory: function(t) {
                            1 == t.type ? this.goPracticeHistory(t) : 2 == t.type && this.goPaperHistory(t)
                        },
                        goPracticeHistory: function(t) {
                            this.$api.openWin({
                                url: "/pages/question/index",
                                params: {
                                    type: "practice",
                                    subType: "history",
                                    qid: t.qBankId,
                                    cid: t.chapterId,
                                    title: "查看练习记录"
                                }
                            })
                        },
                        goPaperHistory: function(t) {
                            "mockExam" == t.practiseType && 1 == t.showAnswer && 2 != t.status && (t.checkStatus = -1), this.$refs.paperModal.open({
                                paper: d(d({}, t), {}, {
                                    note: "common",
                                    name: t.paperName,
                                    checkStatus: t.status,
                                    id: t.paperId
                                })
                            })
                        }
                    }
                };
                e.default = p
            },
            "2a3f": function(t, e, n) {
                "use strict";
                n.r(e);
                var a = n("fdad"),
                    r = n("baa0");
                for (var o in r)["default"].indexOf(o) < 0 && function(t) {
                    n.d(e, t, (function() {
                        return r[t]
                    }))
                }(o);
                n("5a30");
                var i = n("828b"),
                    c = Object(i.a)(r.default, a.b, a.c, !1, null, null, null, !1, a.a, void 0);
                e.default = c.exports
            },
            3987: function(t, e, n) {
                "use strict";
                (function(t, e) {
                    var a = n("47a9");
                    n("8f74"), a(n("3240"));
                    var r = a(n("2a3f"));
                    t.__webpack_require_UNI_MP_PLUGIN__ = n, e(r.default)
                }).call(this, n("3223").default, n("df3c").createPage)
            },
            "5a30": function(t, e, n) {
                "use strict";
                var a = n("6f66");
                n.n(a).a
            },
            "6f66": function(t, e, n) {},
            baa0: function(t, e, n) {
                "use strict";
                n.r(e);
                var a = n("00be"),
                    r = n.n(a);
                for (var o in a)["default"].indexOf(o) < 0 && function(t) {
                    n.d(e, t, (function() {
                        return a[t]
                    }))
                }(o);
                e.default = r.a
            },
            fdad: function(t, e, n) {
                "use strict";
                n.d(e, "b", (function() {
                    return a
                })), n.d(e, "c", (function() {
                    return r
                })), n.d(e, "a", (function() {}));
                var a = function() {
                        var t = this,
                            e = (t.$createElement, t._self._c, t.dataHasBeenLoaded ? t.__map(t.historyInfo, (function(e, n) {
                                var a = t.__get_orig(e),
                                    r = e.historys.length;
                                return {
                                    $orig: a,
                                    g0: r,
                                    l0: r ? t.__map(e.historys, (function(e, n) {
                                        return {
                                            $orig: t.__get_orig(e),
                                            g1: 1 == e.type ? e.chapters.length : null,
                                            g2: 1 == e.type ? e.chapters.length : null,
                                            f0: 1 == e.type ? t._f("dateFormat")(e.addTime) : null,
                                            f1: 1 != e.type && 2 == e.type ? t._f("dateFormat")(e.addTime) : null
                                        }
                                    })) : null
                                }
                            })) : null);
                        t.$mp.data = Object.assign({}, {
                            $root: {
                                l1: e
                            }
                        })
                    },
                    r = []
            }
        },
        [
            ["3987", "common/runtime", "common/vendor"]
        ]
    ]);
}, {
    isPage: true,
    isComponent: true,
    currentFile: 'pages/qbank/history.js'
});
require("pages/qbank/history.js");