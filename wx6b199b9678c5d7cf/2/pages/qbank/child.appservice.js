$gwx5_XC_1 = function(_, _v, _n, _p, _s, _wp, _wl, $gwn, $gwl, $gwh, wh, $gstack, $gwrt, gra, grb, TestTest, wfor, _ca, _da, _r, _rz, _o, _oz, _1, _1z, _2, _2z, _m, _mz, nv_getDate, nv_getRegExp, nv_console, nv_parseInt, nv_parseFloat, nv_isNaN, nv_isFinite, nv_decodeURI, nv_decodeURIComponent, nv_encodeURI, nv_encodeURIComponent, $gdc, nv_JSON, _af, _gv, _ai, _grp, _gd, _gapi, $ixc, _ic, _w, _ev, _tsd) {
    return function(path, global) {
        if (typeof global === 'undefined') {
            if (typeof __GWX_GLOBAL__ === 'undefined') global = {};
            else global = __GWX_GLOBAL__;
        }
        if (typeof __WXML_GLOBAL__ === 'undefined') {
            __WXML_GLOBAL__ = {};
        }
        __WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
        var e_ = {}
        if (typeof(global.entrys) === 'undefined') global.entrys = {};
        e_ = global.entrys;
        var d_ = {}
        if (typeof(global.defines) === 'undefined') global.defines = {};
        d_ = global.defines;
        var f_ = {}
        if (typeof(global.modules) === 'undefined') global.modules = {};
        f_ = global.modules || {};
        var p_ = {}
        __WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
        __WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
        __WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
        var z = __WXML_GLOBAL__.ops_set.$gwx5_XC_1 || [];

        function gz$gwx5_XC_1_1() {
            if (__WXML_GLOBAL__.ops_cached.$gwx5_XC_1_1) return __WXML_GLOBAL__.ops_cached.$gwx5_XC_1_1
            __WXML_GLOBAL__.ops_cached.$gwx5_XC_1_1 = [];
            (function(z) {
                var a = 11;

                function Z(ops) {
                    z.push(ops)
                }
                Z([3, 'data-v-0f067932'])
                Z([3, '__l'])
                Z(z[0])
                Z([
                    [7],
                    [3, 'pageTitle']
                ])
                Z([3, '0c941d55-1'])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'qbank']
                    ],
                    [3, 'id']
                ])
                Z([3, 'page-content data-v-0f067932'])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'g0']
                ])
                Z([
                    [7],
                    [3, 'adminConfig']
                ])
                Z(z[1])
                Z([3, '__e'])
                Z([
                    [7],
                    [3, 'chapters']
                ])
                Z(z[0])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^select']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'selectChapter']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([
                    [7],
                    [3, 'examPointChapters']
                ])
                Z([
                    [7],
                    [3, 'chapterHasBeenLoaded']
                ])
                Z([
                    [7],
                    [3, 'qbank']
                ])
                Z([
                    [9],
                    [
                        [8], 'fullScreen', [1, true]
                    ],
                    [
                        [8], 'radius', [1, 10]
                    ]
                ])
                Z([3, '0c941d55-2'])
                Z([
                    [2, '=='],
                    [
                        [6],
                        [
                            [7],
                            [3, 'qbank']
                        ],
                        [3, 'actionType']
                    ],
                    [1, 'paper']
                ])
                Z([
                    [7],
                    [3, 'paperHasBeenLoaded']
                ])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'g1']
                ])
                Z([3, 'index'])
                Z([3, 'item'])
                Z([
                    [7],
                    [3, 'paperList']
                ])
                Z(z[22])
                Z(z[1])
                Z(z[0])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'qbank']
                    ],
                    [3, 'isAuthorized']
                ])
                Z([
                    [7],
                    [3, 'item']
                ])
                Z([
                    [2, '+'],
                    [1, '0c941d55-3-'],
                    [
                        [7],
                        [3, 'index']
                    ]
                ])
                Z(z[1])
                Z(z[0])
                Z([3, '暂无相关试卷'])
                Z([3, '30%'])
                Z([3, 'order'])
                Z([3, '0c941d55-4'])
                Z(z[1])
                Z(z[0])
                Z([
                    [2, '!'],
                    [
                        [7],
                        [3, 'loadMoreState']
                    ]
                ])
                Z([
                    [7],
                    [3, 'loadMoreState']
                ])
                Z([3, '0c941d55-5'])
                Z(z[5])
                Z([
                    [2, '!'],
                    [
                        [6],
                        [
                            [7],
                            [3, 'qbank']
                        ],
                        [3, 'isAuthorized']
                    ]
                ])
                Z(z[1])
                Z([3, 'data-v-0f067932 vue-ref'])
                Z([3, 'paperModal'])
                Z([3, '0c941d55-6'])
                Z(z[1])
                Z(z[45])
                Z([3, 'actionSheet'])
                Z([3, '0c941d55-7'])
                Z(z[1])
                Z(z[10])
                Z(z[45])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^success']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'loginSuccess']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'login'])
                Z([3, '0c941d55-8'])
                Z([
                    [7],
                    [3, 'allowPayType']
                ])
                Z([
                    [7],
                    [3, 'assistantQrcode']
                ])
                Z(z[1])
                Z(z[10])
                Z(z[45])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^success']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'paymentSuccess']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'payment'])
                Z(z[5])
                Z([3, 'questions'])
                Z([3, '0c941d55-9'])
                Z(z[1])
                Z(z[45])
                Z([3, 'openModal'])
                Z([3, '0c941d55-10'])
            })(__WXML_GLOBAL__.ops_cached.$gwx5_XC_1_1);
            return __WXML_GLOBAL__.ops_cached.$gwx5_XC_1_1
        }
        __WXML_GLOBAL__.ops_set.$gwx5_XC_1 = z;
        __WXML_GLOBAL__.ops_init.$gwx5_XC_1 = true;
        var x = ['./pages/qbank/child.wxml'];
        d_[x[0]] = {}
        var m0 = function(e, s, r, gg) {
            var z = gz$gwx5_XC_1_1()
            var b3 = _n('view')
            _rz(z, b3, 'class', 0, e, s, gg)
            var o6 = _mz(z, 'nav-bar', ['bind:__l', 1, 'class', 1, 'title', 2, 'vueId', 3], [], e, s, gg)
            _(b3, o6)
            var o4 = _v()
            _(b3, o4)
            if (_oz(z, 5, e, s, gg)) {
                o4.wxVkey = 1
                var f7 = _n('view')
                _rz(z, f7, 'class', 6, e, s, gg)
                var c8 = _v()
                _(f7, c8)
                if (_oz(z, 7, e, s, gg)) {
                    c8.wxVkey = 1
                    var h9 = _mz(z, 'xk-qbank-chapter', ['adminConfig', 8, 'bind:__l', 1, 'bind:select', 2, 'chapters', 3, 'class', 4, 'data-event-opts', 5, 'examPointChapters', 6, 'loaded', 7, 'qbank', 8, 'styles', 9, 'vueId', 10], [], e, s, gg)
                    _(c8, h9)
                } else {
                    c8.wxVkey = 2
                    var o0 = _v()
                    _(c8, o0)
                    if (_oz(z, 19, e, s, gg)) {
                        o0.wxVkey = 1
                        var cAB = _v()
                        _(o0, cAB)
                        if (_oz(z, 20, e, s, gg)) {
                            cAB.wxVkey = 1
                            var oBB = _v()
                            _(cAB, oBB)
                            if (_oz(z, 21, e, s, gg)) {
                                oBB.wxVkey = 1
                                var lCB = _v()
                                _(oBB, lCB)
                                var aDB = function(eFB, tEB, bGB, gg) {
                                    var xIB = _mz(z, 'paper-item', ['bind:__l', 26, 'class', 1, 'isAuthorized', 2, 'item', 3, 'vueId', 4], [], eFB, tEB, gg)
                                    _(bGB, xIB)
                                    return bGB
                                }
                                lCB.wxXCkey = 4
                                _2z(z, 24, aDB, e, s, gg, lCB, 'item', 'index', 'index')
                            } else {
                                oBB.wxVkey = 2
                                var oJB = _mz(z, 'xk-empty', ['bind:__l', 31, 'class', 1, 'text', 2, 'top', 3, 'type', 4, 'vueId', 5], [], e, s, gg)
                                _(oBB, oJB)
                            }
                            oBB.wxXCkey = 1
                            oBB.wxXCkey = 3
                            oBB.wxXCkey = 3
                        }
                        var fKB = _mz(z, 'load-more', ['bind:__l', 37, 'class', 1, 'data-custom-hidden', 2, 'status', 3, 'vueId', 4], [], e, s, gg)
                        _(o0, fKB)
                        cAB.wxXCkey = 1
                        cAB.wxXCkey = 3
                    }
                    o0.wxXCkey = 1
                    o0.wxXCkey = 3
                }
                c8.wxXCkey = 1
                c8.wxXCkey = 3
                c8.wxXCkey = 3
                _(o4, f7)
            }
            var x5 = _v()
            _(b3, x5)
            if (_oz(z, 42, e, s, gg)) {
                x5.wxVkey = 1
                var cLB = _v()
                _(x5, cLB)
                if (_oz(z, 43, e, s, gg)) {
                    cLB.wxVkey = 1
                }
                cLB.wxXCkey = 1
            }
            var hMB = _mz(z, 'paper-modal', ['bind:__l', 44, 'class', 1, 'data-ref', 2, 'vueId', 3], [], e, s, gg)
            _(b3, hMB)
            var oNB = _mz(z, 'xk-action-sheet', ['bind:__l', 48, 'class', 1, 'data-ref', 2, 'vueId', 3], [], e, s, gg)
            _(b3, oNB)
            var cOB = _mz(z, 'xk-login', ['bind:__l', 52, 'bind:success', 1, 'class', 2, 'data-event-opts', 3, 'data-ref', 4, 'vueId', 5], [], e, s, gg)
            _(b3, cOB)
            var oPB = _mz(z, 'payment', ['allowPayType', 58, 'assistantQrcode', 1, 'bind:__l', 2, 'bind:success', 3, 'class', 4, 'data-event-opts', 5, 'data-ref', 6, 'productId', 7, 'productType', 8, 'vueId', 9], [], e, s, gg)
            _(b3, oPB)
            var lQB = _mz(z, 'open-modal', ['bind:__l', 68, 'class', 1, 'data-ref', 2, 'vueId', 3], [], e, s, gg)
            _(b3, lQB)
            o4.wxXCkey = 1
            o4.wxXCkey = 3
            x5.wxXCkey = 1
            _(r, b3)
            return r
        }
        e_[x[0]] = {
            f: m0,
            j: [],
            i: [],
            ti: [],
            ic: []
        }
        if (path && e_[path]) {
            return function(env, dd, global) {
                $gwxc = 0;
                var root = {
                    "tag": "wx-page"
                };
                root.children = [];
                g = "$gwx5_XC_1";
                var main = e_[path].f
                if (typeof global === "undefined") global = {};
                global.f = $gdc(f_[path], "", 1);
                try {
                    main(env, {}, root, global);
                    _tsd(root)
                } catch (err) {
                    console.log(err)
                };
                g = "";
                return root;
            }
        }
    }
}(__g.a, __g.b, __g.c, __g.d, __g.e, __g.f, __g.g, __g.h, __g.i, __g.j, __g.k, __g.l, __g.m, __g.n, __g.o, __g.p, __g.q, __g.r, __g.s, __g.t, __g.u, __g.v, __g.w, __g.x, __g.y, __g.z, __g.A, __g.B, __g.C, __g.D, __g.E, __g.F, __g.G, __g.H, __g.I, __g.J, __g.K, __g.L, __g.M, __g.N, __g.O, __g.P, __g.Q, __g.R, __g.S, __g.T, __g.U, __g.V, __g.W, __g.X, __g.Y, __g.Z, __g.aa);
if (__vd_version_info__.delayedGwx || false) $gwx5_XC_1();
if (__vd_version_info__.delayedGwx) __wxAppCode__['pages/qbank/child.wxml'] = [$gwx5_XC_1, './pages/qbank/child.wxml'];
else __wxAppCode__['pages/qbank/child.wxml'] = $gwx5_XC_1('./pages/qbank/child.wxml');;
__wxRoute = "pages/qbank/child";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/qbank/child.js";
define("pages/qbank/child.js", function(require, module, exports, window, document, frames, self, location, navigator, localStorage, history, Caches, screen, alert, confirm, prompt, XMLHttpRequest, WebSocket, Reporter, webkit, WeixinJSCore) {
    (global.webpackJsonp = global.webpackJsonp || []).push([
        ["pages/qbank/child"], {
            "199b": function(e, t, n) {},
            "31ef": function(e, t, n) {
                "use strict";
                n.d(t, "b", (function() {
                    return i
                })), n.d(t, "c", (function() {
                    return o
                })), n.d(t, "a", (function() {
                    return a
                }));
                var a = {
                        payment: function() {
                            return Promise.all([n.e("common/vendor"), n.e("components/payment/payment")]).then(n.bind(null, "5d4b"))
                        }
                    },
                    i = function() {
                        var e = this,
                            t = (e.$createElement, e._self._c, e.qbank.id ? ("practise" == e.qbank.actionType || "examPoint" == e.qbank.actionType) && (e.chapters.length || e.examPointChapters.length) : null),
                            n = e.qbank.id && !t && "paper" == e.qbank.actionType && e.paperHasBeenLoaded ? e.paperList.length : null;
                        e.$mp.data = Object.assign({}, {
                            $root: {
                                g0: t,
                                g1: n
                            }
                        })
                    },
                    o = []
            },
            "5b4a": function(e, t, n) {
                "use strict";
                n.r(t);
                var a = n("31ef"),
                    i = n("7b23");
                for (var o in i)["default"].indexOf(o) < 0 && function(e) {
                    n.d(t, e, (function() {
                        return i[e]
                    }))
                }(o);
                n("e94d");
                var r = n("828b"),
                    s = Object(r.a)(i.default, a.b, a.c, !1, null, "0f067932", null, !1, a.a, void 0);
                t.default = s.exports
            },
            6992: function(e, t, n) {
                "use strict";
                (function(e) {
                    var a = n("47a9");
                    Object.defineProperty(t, "__esModule", {
                        value: !0
                    }), t.default = void 0;
                    var i = a(n("7eb4")),
                        o = a(n("7ca3")),
                        r = a(n("ee10")),
                        s = n("88d2"),
                        c = (n("3c3c"), n("2eff")),
                        u = n("62a4"),
                        p = a(n("f462"));

                    function d(e, t) {
                        var n = Object.keys(e);
                        if (Object.getOwnPropertySymbols) {
                            var a = Object.getOwnPropertySymbols(e);
                            t && (a = a.filter((function(t) {
                                return Object.getOwnPropertyDescriptor(e, t).enumerable
                            }))), n.push.apply(n, a)
                        }
                        return n
                    }

                    function l(e) {
                        for (var t = 1; t < arguments.length; t++) {
                            var n = null != arguments[t] ? arguments[t] : {};
                            t % 2 ? d(Object(n), !0).forEach((function(t) {
                                (0, o.default)(e, t, n[t])
                            })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : d(Object(n)).forEach((function(t) {
                                Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                            }))
                        }
                        return e
                    }
                    var f = {
                        components: {
                            xkQbankChapter: function() {
                                Promise.all([n.e("common/vendor"), n.e("components/qbank/chapter")]).then(function() {
                                    return resolve(n("d45e"))
                                }.bind(null, n)).catch(n.oe)
                            },
                            xkQbankPaper: function() {
                                n.e("components/qbank/paper").then(function() {
                                    return resolve(n("8855"))
                                }.bind(null, n)).catch(n.oe)
                            },
                            payment: function() {
                                Promise.all([n.e("common/vendor"), n.e("components/payment/payment")]).then(function() {
                                    return resolve(n("5d4b"))
                                }.bind(null, n)).catch(n.oe)
                            },
                            xkActionSheet: function() {
                                n.e("components/common/action-sheet").then(function() {
                                    return resolve(n("4fe3"))
                                }.bind(null, n)).catch(n.oe)
                            },
                            loadMore: function() {
                                n.e("components/common/load-more").then(function() {
                                    return resolve(n("3dc2"))
                                }.bind(null, n)).catch(n.oe)
                            },
                            paperItem: function() {
                                Promise.all([n.e("common/vendor"), n.e("components/qbank/paper-item")]).then(function() {
                                    return resolve(n("1557"))
                                }.bind(null, n)).catch(n.oe)
                            },
                            paperModal: function() {
                                Promise.all([n.e("common/vendor"), n.e("components/qbank/paper-modal")]).then(function() {
                                    return resolve(n("83b0"))
                                }.bind(null, n)).catch(n.oe)
                            },
                            openModal: function() {
                                n.e("components/common/modal-open").then(function() {
                                    return resolve(n("8ce3"))
                                }.bind(null, n)).catch(n.oe)
                            }
                        },
                        data: function() {
                            return {
                                qbankId: 0,
                                qbank: {},
                                chapters: [],
                                examPointChapters: [],
                                chapterHasBeenLoaded: !1,
                                paperHasBeenLoaded: !1,
                                adminConfig: {},
                                allowPayType: [],
                                paperList: [],
                                currentPage: 1,
                                loadMoreFlag: !0,
                                loadMoreState: 0
                            }
                        },
                        onLoad: function(t) {
                            this.qbankId = t.id || 0, this.agentId = t.agentId || 0, this.qbankId || e.reLaunch({
                                url: "/pages/index?tabType=qbank"
                            }), this.getQbank()
                        },
                        onReachBottom: function() {
                            this.loadMoreFlag && (this.currentPage++, this.getPaperList())
                        },
                        computed: {
                            pageTitle: function() {
                                return this.qbank.id ? this.qbank.title : ""
                            },
                            hiddenPrice: function() {
                                return !this.$api.formatThePrice(1, 3)
                            },
                            assistantQrcode: function() {
                                return this.qbank.optional && this.qbank.optional.assistantQrcode ? this.qbank.optional.assistantQrcode : ""
                            }
                        },
                        methods: {
                            addEventListener: function(e, t) {
                                var n = this;
                                "loginSuccess" == e ? this.loginSuccess() : "updateQbank" == e ? setTimeout((function() {
                                    n.getQbank()
                                }), 200) : "paymented" == e && "questions" == t.type && this.paymentSuccess()
                            },
                            formatImageUrl: function(e, t, n, a) {
                                return this.$api.formatImageUrl(e, t, n, a)
                            },
                            getQbank: function() {
                                var t = this;
                                this.token = (0, u.getToken)(), this.qbankId && this.$http({
                                    url: "/api/q_bank",
                                    data: {
                                        id: this.qbankId
                                    }
                                }).then(function() {
                                    var n = (0, r.default)(i.default.mark((function n(a) {
                                        var o, r, s, u;
                                        return i.default.wrap((function(n) {
                                            for (;;) switch (n.prev = n.next) {
                                                case 0:
                                                    if (0 !== a.errno) {
                                                        n.next = 12;
                                                        break
                                                    }
                                                    return t.qbank = a.data, t.qbankId = t.qbank.id, t.isHave = a.data.isAuthorized, "examPoint" == t.qbank.actionType && (t.qbank.examPointQbank = JSON.parse(JSON.stringify(t.qbank))), "practise" == t.qbank.actionType || "examPoint" == t.qbank.actionType ? t.getQbankChapter() : "paper" == t.qbank.actionType && t.getPaperList(), n.next = 8, (0, c._getAdminConfig)();
                                                case 8:
                                                    t.adminConfig = n.sent, t.isHave || (o = a.data, r = [], r = o.optional ? o.optional.allowPayType || [] : ["pay", "key", "password"], t.adminConfig.payThreeUrl && (r = ["pay"]), "ios" == e.getSystemInfoSync().platform && ((s = r.findIndex((function(e) {
                                                        return "pay" == e
                                                    }))) > -1 && r.splice(s, 1)), t.adminConfig.paySwitch || (u = r.findIndex((function(e) {
                                                        return "pay" == e
                                                    }))) > -1 && r.splice(u, 1), t.allowPayType = r), n.next = 13;
                                                    break;
                                                case 12:
                                                    100143 === a.errno && (t.token = "", t.goLogin());
                                                case 13:
                                                case "end":
                                                    return n.stop()
                                            }
                                        }), n)
                                    })));
                                    return function(e) {
                                        return n.apply(this, arguments)
                                    }
                                }())
                            },
                            getParentQbank: function() {
                                this.$http({
                                    url: "/api/q_bank",
                                    data: {
                                        id: this.qbank.pId
                                    }
                                }).then(function() {
                                    var e = (0, r.default)(i.default.mark((function e(t) {
                                        return i.default.wrap((function(e) {
                                            for (;;) switch (e.prev = e.next) {
                                                case 0:
                                                    t.errno;
                                                case 1:
                                                case "end":
                                                    return e.stop()
                                            }
                                        }), e)
                                    })));
                                    return function(t) {
                                        return e.apply(this, arguments)
                                    }
                                }())
                            },
                            getQbankChapter: function() {
                                var e = this;
                                return (0, r.default)(i.default.mark((function t() {
                                    var n;
                                    return i.default.wrap((function(t) {
                                        for (;;) switch (t.prev = t.next) {
                                            case 0:
                                                if (e.qbankId) {
                                                    t.next = 2;
                                                    break
                                                }
                                                return t.abrupt("return");
                                            case 2:
                                                return t.next = 4, (0, s._getQbankChapter)(e.qbankId);
                                            case 4:
                                                n = t.sent, e.chapterHasBeenLoaded = !0, 0 === n.errno && (e.chapters = n.data, "examPoint" == e.qbank.actionType && (e.examPointChapters = n.data));
                                            case 7:
                                            case "end":
                                                return t.stop()
                                        }
                                    }), t)
                                })))()
                            },
                            getPaperList: function() {
                                var e = this;
                                "paper" == this.qbank.actionType && (this.loadMoreState = 2, this.$http({
                                    url: "/api/paper/search_by_qbank",
                                    data: {
                                        qBankId: this.qbank.id,
                                        page: this.currentPage || 1,
                                        pageSize: 20
                                    }
                                }).then((function(t) {
                                    0 === t.errno && (e.paperHasBeenLoaded = !0, 1 == e.currentPage ? e.paperList = t.data : e.paperList = e.paperList.concat(t.data), t.currentPage >= t.totalPages ? (e.loadMoreFlag = !1, t.count <= 5 ? e.loadMoreState = 0 : e.loadMoreState = 3) : (e.loadMoreFlag = !0, e.loadMoreState = 1))
                                })))
                            },
                            selectPaper: function() {
                                var e = arguments,
                                    t = this;
                                return (0, r.default)(i.default.mark((function n() {
                                    var a;
                                    return i.default.wrap((function(n) {
                                        for (;;) switch (n.prev = n.next) {
                                            case 0:
                                                return a = e.length > 0 && void 0 !== e[0] ? e[0] : {}, n.next = 3, t.interceptLogin();
                                            case 3:
                                                return n.next = 5, t.interceptPhone();
                                            case 5:
                                                return n.next = 7, t.interceptPay();
                                            case 7:
                                                t.$refs.paperModal.open({
                                                    paper: a.paper,
                                                    isChildQbank: 1
                                                });
                                            case 8:
                                            case "end":
                                                return n.stop()
                                        }
                                    }), n)
                                })))()
                            },
                            goLogin: function() {
                                var e = this;
                                1154 != this.$store.state.launchOptions.scene ? (0, p.default)({
                                    component: !0,
                                    openComponent: function(t) {
                                        t.reload ? e.getQbank() : e.$refs.login.show()
                                    },
                                    success: function() {
                                        e.loginSuccess()
                                    }
                                }) : this.$api.toast("请前往小程序使用完整服务")
                            },
                            loginSuccess: function() {
                                this.getQbank()
                            },
                            paymentSuccess: function() {
                                this.getQbank()
                            },
                            interceptLogin: function() {
                                var e = this;
                                return new Promise((function(t, n) {
                                    e.token ? t() : e.goLogin()
                                }))
                            },
                            interceptPhone: function() {
                                var t = this;
                                return new Promise((function(n, a) {
                                    var i = e.getStorageSync("userInfo") || {};
                                    t.adminConfig && t.adminConfig.needPhone && !i.phone ? t.$refs.openModal.open({
                                        content: t.adminConfig.needPhoneTip,
                                        success: function(n) {
                                            n.confirm && (n.errMsg && n.errMsg.indexOf("getPhoneNumber") > -1 ? t.bindWechatPhone(n) : e.navigateTo({
                                                url: "/pages/user/phone"
                                            }))
                                        }
                                    }) : n()
                                }))
                            },
                            interceptPay: function(e) {
                                var t = this;
                                return new Promise((function(n, a) {
                                    t.qbank.isAuthorized || e ? n() : t.joinQbank()
                                }))
                            },
                            joinQbank: function(e) {
                                if (0 != this.qbank.price) {
                                    var t = this.allowPayType;
                                    0 == t.length ? this.$api.toast(this.isMpios ? "暂不支持加入" : "暂不支持购买") : 1 == t.length ? "password" == t[0] ? this.$refs.payment.joinProductByPassword() : "key" == t[0] ? this.$refs.payment.joinProductByKey() : "assistant" == t[0] && this.assistantQrcode ? this.$refs.payment.joinProductByAssistant() : e ? this.goBuy() : this.tipToBuy() : t.includes("pay") ? e ? this.goBuy() : this.tipToBuy() : this.$refs.payment.open()
                                } else this.tipToJoin()
                            },
                            tipToBuy: function() {
                                var e = this;
                                this.$api.confirm({
                                    title: "请先开通权限",
                                    content: "该功能需要开通后才可以使用哦",
                                    confirmText: "去开通",
                                    success: function(t) {
                                        t.confirm && e.goBuy()
                                    }
                                })
                            },
                            tipToJoin: function() {
                                var e = this;
                                this.$api.confirm({
                                    title: "请先开通权限",
                                    content: "该功能需要报名后才可以使用哦",
                                    confirmText: "免费报名",
                                    success: function(t) {
                                        t.confirm && e.join()
                                    }
                                })
                            },
                            join: function() {
                                var e = this;
                                return (0, r.default)(i.default.mark((function t() {
                                    return i.default.wrap((function(t) {
                                        for (;;) switch (t.prev = t.next) {
                                            case 0:
                                                return t.next = 2, e.interceptLogin();
                                            case 2:
                                                return t.next = 4, e.interceptPhone();
                                            case 4:
                                                e.$api.showLoading(), e.$http({
                                                    methods: "POST",
                                                    url: "/api/questions_member",
                                                    data: {
                                                        qBankId: e.qbank.id
                                                    }
                                                }).then((function(t) {
                                                    e.$api.hideLoading(), 0 === t.errno && (e.$api.toast("报名成功"), e.getQbank())
                                                }));
                                            case 6:
                                            case "end":
                                                return t.stop()
                                        }
                                    }), t)
                                })))()
                            },
                            goBuy: function() {
                                if (this.sellOut) this.$api.toast("已售罄，暂无法购买");
                                else {
                                    var e = {
                                        type: "questions",
                                        id: this.qbank.id
                                    };
                                    this.agentId && (e.agentId = this.agentId), this.$api.openWin({
                                        url: "/pages/buy/index",
                                        params: e
                                    })
                                }
                            },
                            selectChapter: function(e, t) {
                                var n = this;
                                return (0, r.default)(i.default.mark((function a() {
                                    return i.default.wrap((function(a) {
                                        for (;;) switch (a.prev = a.next) {
                                            case 0:
                                                return a.next = 2, n.interceptLogin();
                                            case 2:
                                                return a.next = 4, n.interceptPhone();
                                            case 4:
                                                return a.next = 6, n.interceptPay(1 == e.free || 1 == t);
                                            case 6:
                                                "practise" == n.qbank.actionType ? n.goQuestion("chapter", {
                                                    title: e.title,
                                                    cid: e.id,
                                                    allCount: e.questionCount
                                                }) : "examPoint" == n.qbank.actionType && n.goQuestion("examPoint", {
                                                    title: e.title,
                                                    cid: e.id,
                                                    allCount: e.examQueCount
                                                });
                                            case 7:
                                            case "end":
                                                return a.stop()
                                        }
                                    }), a)
                                })))()
                            },
                            goQuestion: function(t, n) {
                                var a, i = this,
                                    r = e.getStorageSync("wrongQuestionPageSize") || 100,
                                    s = l({
                                        type: "practice",
                                        subType: t,
                                        qid: this.qbank.id,
                                        pageSize: r
                                    }, n);
                                if ("chapter" == t) a = n.allCount;
                                else if ("viewWrong" == t) a = this.statistics.wrong_question, s.title = "查看错题", s.subType = "wrong", s.subsubType = "viewWrong";
                                else if ("doWrong" == t) a = this.statistics.wrong_question, s.title = "错题重练", s.subType = "wrong", s.subsubType = "doWrong";
                                else if ("favorite" == t) a = this.statistics.favorite, s.title = "我的收藏";
                                else if ("note" == t) a = this.statistics.notes, s.title = "我的笔记";
                                else if ("random" == t) {
                                    a = e.getStorageSync("randomQuestionCount") || 20, s.title = "随机练习"
                                } else if ("examPoint" == t) {
                                    a = n.allCount, s = l({
                                        qid: this.qbank.id
                                    }, n);
                                    var c = e.getStorageSync("lastExamPoint");
                                    e.setStorageSync("lastExamPoint", l(l({}, c), {}, (0, o.default)({}, this.qbank.id, n.cid)))
                                }
                                if ("wrong" == s.subType && a > r) {
                                    for (var u = Math.ceil(a / r), p = [], d = 0; d < u; d++) p.push(r * d + 1 + "-" + (r * (d + 1) > a ? a : r * (d + 1)));
                                    this.$refs.actionSheet.open({
                                        title: "题目过多，选择下要练习的范围吧！",
                                        itemList: p,
                                        itemSize: "13px",
                                        success: function(e) {
                                            var t = e.tapIndex + 1;
                                            console.log("用户选择了第" + t + "页"), console.log("前往答题页"), s = Object.assign(s, {
                                                page: t
                                            }), i.$api.openWin({
                                                url: "/pages/question/index",
                                                params: s
                                            })
                                        }
                                    })
                                } else {
                                    if (("chapter" == t || "examPoint" == t) && 0 == a) return void setTimeout((function() {
                                        i.$api.toast("题目正在准备中")
                                    }), 200);
                                    "examPoint" == t ? (delete s.allCount, this.$api.openWin({
                                        url: "/pages/knowledge/index",
                                        params: s
                                    })) : (s.pageSize = a, this.$api.openWin({
                                        url: "/pages/question/index",
                                        params: s
                                    }))
                                }
                            }
                        }
                    };
                    t.default = f
                }).call(this, n("df3c").default)
            },
            "7aeb": function(e, t, n) {
                "use strict";
                (function(e, t) {
                    var a = n("47a9");
                    n("8f74"), a(n("3240"));
                    var i = a(n("5b4a"));
                    e.__webpack_require_UNI_MP_PLUGIN__ = n, t(i.default)
                }).call(this, n("3223").default, n("df3c").createPage)
            },
            "7b23": function(e, t, n) {
                "use strict";
                n.r(t);
                var a = n("6992"),
                    i = n.n(a);
                for (var o in a)["default"].indexOf(o) < 0 && function(e) {
                    n.d(t, e, (function() {
                        return a[e]
                    }))
                }(o);
                t.default = i.a
            },
            e94d: function(e, t, n) {
                "use strict";
                var a = n("199b");
                n.n(a).a
            }
        },
        [
            ["7aeb", "common/runtime", "common/vendor"]
        ]
    ]);
}, {
    isPage: true,
    isComponent: true,
    currentFile: 'pages/qbank/child.js'
});
require("pages/qbank/child.js");