var __wxAppData = __wxAppData || {};
var __wxAppCode__ = __wxAppCode__ || {};
var global = global || {};
var __WXML_GLOBAL__ = __WXML_GLOBAL__ || {
    entrys: {},
    defines: {},
    modules: {},
    ops: [],
    wxs_nf_init: undefined,
    total_ops: 0
};
var Component = Component || function() {};
var definePlugin = definePlugin || function() {};
var requirePlugin = requirePlugin || function() {};
var Behavior = Behavior || function() {};
var __vd_version_info__ = __vd_version_info__ || {};
var __GWX_GLOBAL__ = __GWX_GLOBAL__ || {};
if (this && this.__g === undefined) Object.defineProperty(this, "__g", {
    configurable: false,
    enumerable: false,
    writable: false,
    value: function() {
        function D(e, t) {
            if (typeof t != "undefined") e.children.push(t)
        }

        function S(e) {
            if (typeof e != "undefined") return {
                tag: "virtual",
                wxKey: e,
                children: []
            };
            return {
                tag: "virtual",
                children: []
            }
        }

        function v(e) {
            return {
                tag: "wx-" + e,
                attr: {},
                children: [],
                n: [],
                raw: {},
                generics: {}
            }
        }

        function e(e, t) {
            t && e.properities.push(t)
        }

        function t(e, t, r) {
            return typeof e[r] != "undefined" ? e[r] : t[r]
        }

        function u(e) {
            console.warn("WXMLRT_" + g + ":" + e)
        }

        function r(e, t) {
            u(t + ":-1:-1:-1: Template `" + e + "` is being called recursively, will be stop.")
        }
        var s = console.warn;
        var n = console.log;

        function o() {
            function e() {}
            e.prototype = {
                hn: function(e, t) {
                    if (typeof e == "object") {
                        var r = 0;
                        var n = false,
                            o = false;
                        for (var a in e) {
                            n = n | a === "__value__";
                            o = o | a === "__wxspec__";
                            r++;
                            if (r > 2) break
                        }
                        return r == 2 && n && o && (t || e.__wxspec__ !== "m" || this.hn(e.__value__) === "h") ? "h" : "n"
                    }
                    return "n"
                },
                nh: function(e, t) {
                    return {
                        __value__: e,
                        __wxspec__: t ? t : true
                    }
                },
                rv: function(e) {
                    return this.hn(e, true) === "n" ? e : this.rv(e.__value__)
                },
                hm: function(e) {
                    if (typeof e == "object") {
                        var t = 0;
                        var r = false,
                            n = false;
                        for (var o in e) {
                            r = r | o === "__value__";
                            n = n | o === "__wxspec__";
                            t++;
                            if (t > 2) break
                        }
                        return t == 2 && r && n && (e.__wxspec__ === "m" || this.hm(e.__value__))
                    }
                    return false
                }
            };
            return new e
        }
        var A = o();

        function T(e) {
            var t = e.split("\n " + " " + " " + " ");
            for (var r = 0; r < t.length; ++r) {
                if (0 == r) continue;
                if (")" === t[r][t[r].length - 1]) t[r] = t[r].replace(/\s\(.*\)$/, "");
                else t[r] = "at anonymous function"
            }
            return t.join("\n " + " " + " " + " ")
        }

        function a(M) {
            function m(e, t, r, n, o) {
                var a = false;
                var i = e[0][1];
                var p, u, l, f, v, c;
                switch (i) {
                    case "?:":
                        p = x(e[1], t, r, n, o, a);
                        l = M && A.hn(p) === "h";
                        f = A.rv(p) ? x(e[2], t, r, n, o, a) : x(e[3], t, r, n, o, a);
                        f = l && A.hn(f) === "n" ? A.nh(f, "c") : f;
                        return f;
                        break;
                    case "&&":
                        p = x(e[1], t, r, n, o, a);
                        l = M && A.hn(p) === "h";
                        f = A.rv(p) ? x(e[2], t, r, n, o, a) : A.rv(p);
                        f = l && A.hn(f) === "n" ? A.nh(f, "c") : f;
                        return f;
                        break;
                    case "||":
                        p = x(e[1], t, r, n, o, a);
                        l = M && A.hn(p) === "h";
                        f = A.rv(p) ? A.rv(p) : x(e[2], t, r, n, o, a);
                        f = l && A.hn(f) === "n" ? A.nh(f, "c") : f;
                        return f;
                        break;
                    case "+":
                    case "*":
                    case "/":
                    case "%":
                    case "|":
                    case "^":
                    case "&":
                    case "===":
                    case "==":
                    case "!=":
                    case "!==":
                    case ">=":
                    case "<=":
                    case ">":
                    case "<":
                    case "<<":
                    case ">>":
                        p = x(e[1], t, r, n, o, a);
                        u = x(e[2], t, r, n, o, a);
                        l = M && (A.hn(p) === "h" || A.hn(u) === "h");
                        switch (i) {
                            case "+":
                                f = A.rv(p) + A.rv(u);
                                break;
                            case "*":
                                f = A.rv(p) * A.rv(u);
                                break;
                            case "/":
                                f = A.rv(p) / A.rv(u);
                                break;
                            case "%":
                                f = A.rv(p) % A.rv(u);
                                break;
                            case "|":
                                f = A.rv(p) | A.rv(u);
                                break;
                            case "^":
                                f = A.rv(p) ^ A.rv(u);
                                break;
                            case "&":
                                f = A.rv(p) & A.rv(u);
                                break;
                            case "===":
                                f = A.rv(p) === A.rv(u);
                                break;
                            case "==":
                                f = A.rv(p) == A.rv(u);
                                break;
                            case "!=":
                                f = A.rv(p) != A.rv(u);
                                break;
                            case "!==":
                                f = A.rv(p) !== A.rv(u);
                                break;
                            case ">=":
                                f = A.rv(p) >= A.rv(u);
                                break;
                            case "<=":
                                f = A.rv(p) <= A.rv(u);
                                break;
                            case ">":
                                f = A.rv(p) > A.rv(u);
                                break;
                            case "<":
                                f = A.rv(p) < A.rv(u);
                                break;
                            case "<<":
                                f = A.rv(p) << A.rv(u);
                                break;
                            case ">>":
                                f = A.rv(p) >> A.rv(u);
                                break;
                            default:
                                break
                        }
                        return l ? A.nh(f, "c") : f;
                        break;
                    case "-":
                        p = e.length === 3 ? x(e[1], t, r, n, o, a) : 0;
                        u = e.length === 3 ? x(e[2], t, r, n, o, a) : x(e[1], t, r, n, o, a);
                        l = M && (A.hn(p) === "h" || A.hn(u) === "h");
                        f = l ? A.rv(p) - A.rv(u) : p - u;
                        return l ? A.nh(f, "c") : f;
                        break;
                    case "!":
                        p = x(e[1], t, r, n, o, a);
                        l = M && A.hn(p) == "h";
                        f = !A.rv(p);
                        return l ? A.nh(f, "c") : f;
                    case "~":
                        p = x(e[1], t, r, n, o, a);
                        l = M && A.hn(p) == "h";
                        f = ~A.rv(p);
                        return l ? A.nh(f, "c") : f;
                    default:
                        s("unrecognized op" + i)
                }
            }

            function x(e, t, r, n, o, a) {
                var i = e[0];
                var p = false;
                if (typeof a !== "undefined") o.ap = a;
                if (typeof i === "object") {
                    var u = i[0];
                    var l, f, v, c, s, y, b, d, h, _, g;
                    switch (u) {
                        case 2:
                            return m(e, t, r, n, o);
                            break;
                        case 4:
                            return x(e[1], t, r, n, o, p);
                            break;
                        case 5:
                            switch (e.length) {
                                case 2:
                                    l = x(e[1], t, r, n, o, p);
                                    return M ? [l] : [A.rv(l)];
                                    return [l];
                                    break;
                                case 1:
                                    return [];
                                    break;
                                default:
                                    l = x(e[1], t, r, n, o, p);
                                    v = x(e[2], t, r, n, o, p);
                                    l.push(M ? v : A.rv(v));
                                    return l;
                                    break
                            }
                            break;
                        case 6:
                            l = x(e[1], t, r, n, o);
                            var w = o.ap;
                            h = A.hn(l) === "h";
                            f = h ? A.rv(l) : l;
                            o.is_affected |= h;
                            if (M) {
                                if (f === null || typeof f === "undefined") {
                                    return h ? A.nh(undefined, "e") : undefined
                                }
                                v = x(e[2], t, r, n, o, p);
                                _ = A.hn(v) === "h";
                                c = _ ? A.rv(v) : v;
                                o.ap = w;
                                o.is_affected |= _;
                                if (c === null || typeof c === "undefined" || c === "__proto__" || c === "prototype" || c === "caller") {
                                    return h || _ ? A.nh(undefined, "e") : undefined
                                }
                                y = f[c];
                                if (typeof y === "function" && !w) y = undefined;
                                g = A.hn(y) === "h";
                                o.is_affected |= g;
                                return h || _ ? g ? y : A.nh(y, "e") : y
                            } else {
                                if (f === null || typeof f === "undefined") {
                                    return undefined
                                }
                                v = x(e[2], t, r, n, o, p);
                                _ = A.hn(v) === "h";
                                c = _ ? A.rv(v) : v;
                                o.ap = w;
                                o.is_affected |= _;
                                if (c === null || typeof c === "undefined" || c === "__proto__" || c === "prototype" || c === "caller") {
                                    return undefined
                                }
                                y = f[c];
                                if (typeof y === "function" && !w) y = undefined;
                                g = A.hn(y) === "h";
                                o.is_affected |= g;
                                return g ? A.rv(y) : y
                            }
                        case 7:
                            switch (e[1][0]) {
                                case 11:
                                    o.is_affected |= A.hn(n) === "h";
                                    return n;
                                case 3:
                                    b = A.rv(r);
                                    d = A.rv(t);
                                    v = e[1][1];
                                    if (n && n.f && n.f.hasOwnProperty(v)) {
                                        l = n.f;
                                        o.ap = true
                                    } else {
                                        l = b && b.hasOwnProperty(v) ? r : d && d.hasOwnProperty(v) ? t : undefined
                                    }
                                    if (M) {
                                        if (l) {
                                            h = A.hn(l) === "h";
                                            f = h ? A.rv(l) : l;
                                            y = f[v];
                                            g = A.hn(y) === "h";
                                            o.is_affected |= h || g;
                                            y = h && !g ? A.nh(y, "e") : y;
                                            return y
                                        }
                                    } else {
                                        if (l) {
                                            h = A.hn(l) === "h";
                                            f = h ? A.rv(l) : l;
                                            y = f[v];
                                            g = A.hn(y) === "h";
                                            o.is_affected |= h || g;
                                            return A.rv(y)
                                        }
                                    }
                                    return undefined
                            }
                            break;
                        case 8:
                            l = {};
                            l[e[1]] = x(e[2], t, r, n, o, p);
                            return l;
                            break;
                        case 9:
                            l = x(e[1], t, r, n, o, p);
                            v = x(e[2], t, r, n, o, p);

                            function O(e, t, r) {
                                var n, o;
                                h = A.hn(e) === "h";
                                _ = A.hn(t) === "h";
                                f = A.rv(e);
                                c = A.rv(t);
                                for (var a in c) {
                                    if (r || !f.hasOwnProperty(a)) {
                                        f[a] = M ? _ ? A.nh(c[a], "e") : c[a] : A.rv(c[a])
                                    }
                                }
                                return e
                            }
                            var s = l;
                            var j = true;
                            if (typeof e[1][0] === "object" && e[1][0][0] === 10) {
                                l = v;
                                v = s;
                                j = false
                            }
                            if (typeof e[1][0] === "object" && e[1][0][0] === 10) {
                                var P = {};
                                return O(O(P, l, j), v, j)
                            } else return O(l, v, j);
                            break;
                        case 10:
                            l = x(e[1], t, r, n, o, p);
                            l = M ? l : A.rv(l);
                            return l;
                            break;
                        case 12:
                            var P;
                            l = x(e[1], t, r, n, o);
                            if (!o.ap) {
                                return M && A.hn(l) === "h" ? A.nh(P, "f") : P
                            }
                            var w = o.ap;
                            v = x(e[2], t, r, n, o, p);
                            o.ap = w;
                            h = A.hn(l) === "h";
                            _ = N(v);
                            f = A.rv(l);
                            c = A.rv(v);
                            snap_bb = K(c, "nv_");
                            try {
                                P = typeof f === "function" ? K(f.apply(null, snap_bb)) : undefined
                            } catch (t) {
                                t.message = t.message.replace(/nv_/g, "");
                                t.stack = t.stack.substring(0, t.stack.indexOf("\n", t.stack.lastIndexOf("at nv_")));
                                t.stack = t.stack.replace(/\snv_/g, " ");
                                t.stack = T(t.stack);
                                if (n.debugInfo) {
                                    t.stack += "\n " + " " + " " + " at " + n.debugInfo[0] + ":" + n.debugInfo[1] + ":" + n.debugInfo[2];
                                    console.error(t)
                                }
                                P = undefined
                            }
                            return M && (_ || h) ? A.nh(P, "f") : P
                    }
                } else {
                    if (i === 3 || i === 1) return e[1];
                    else if (i === 11) {
                        var l = "";
                        for (var D = 1; D < e.length; D++) {
                            var S = A.rv(x(e[D], t, r, n, o, p));
                            l += typeof S === "undefined" ? "" : S
                        }
                        return l
                    }
                }
            }

            function e(e, t, r, n, o, a) {
                if (e[0] == "11182016") {
                    n.debugInfo = e[2];
                    return x(e[1], t, r, n, o, a)
                } else {
                    n.debugInfo = null;
                    return x(e, t, r, n, o, a)
                }
            }
            return e
        }
        var f = a(true);
        var c = a(false);

        function i(e, t, r, n, o, a, i, p) {
            {
                var u = {
                    is_affected: false
                };
                var l = f(t, r, n, o, u);
                if (JSON.stringify(l) != JSON.stringify(a) || u.is_affected != p) {
                    console.warn("A. " + e + " get result " + JSON.stringify(l) + ", " + u.is_affected + ", but " + JSON.stringify(a) + ", " + p + " is expected")
                }
            } {
                var u = {
                    is_affected: false
                };
                var l = c(t, r, n, o, u);
                if (JSON.stringify(l) != JSON.stringify(i) || u.is_affected != p) {
                    console.warn("B. " + e + " get result " + JSON.stringify(l) + ", " + u.is_affected + ", but " + JSON.stringify(i) + ", " + p + " is expected")
                }
            }
        }

        function y(e, t, r, n, o, a, i, p, u) {
            var l = A.hn(e) === "n";
            var f = A.rv(n);
            var v = f.hasOwnProperty(i);
            var c = f.hasOwnProperty(p);
            var s = f[i];
            var y = f[p];
            var b = Object.prototype.toString.call(A.rv(e));
            var d = b[8];
            if (d === "N" && b[10] === "l") d = "X";
            var h;
            if (l) {
                if (d === "A") {
                    var _;
                    for (var g = 0; g < e.length; g++) {
                        f[i] = e[g];
                        f[p] = l ? g : A.nh(g, "h");
                        _ = A.rv(e[g]);
                        var w = u && _ ? u === "*this" ? _ : A.rv(_[u]) : undefined;
                        h = S(w);
                        D(a, h);
                        t(r, f, h, o)
                    }
                } else if (d === "O") {
                    var g = 0;
                    var _;
                    for (var O in e) {
                        f[i] = e[O];
                        f[p] = l ? O : A.nh(O, "h");
                        _ = A.rv(e[O]);
                        var w = u && _ ? u === "*this" ? _ : A.rv(_[u]) : undefined;
                        h = S(w);
                        D(a, h);
                        t(r, f, h, o);
                        g++
                    }
                } else if (d === "S") {
                    for (var g = 0; g < e.length; g++) {
                        f[i] = e[g];
                        f[p] = l ? g : A.nh(g, "h");
                        h = S(e[g] + g);
                        D(a, h);
                        t(r, f, h, o)
                    }
                } else if (d === "N") {
                    for (var g = 0; g < e; g++) {
                        f[i] = g;
                        f[p] = l ? g : A.nh(g, "h");
                        h = S(g);
                        D(a, h);
                        t(r, f, h, o)
                    }
                } else {}
            } else {
                var j = A.rv(e);
                var _, P;
                if (d === "A") {
                    for (var g = 0; g < j.length; g++) {
                        P = j[g];
                        P = A.hn(P) === "n" ? A.nh(P, "h") : P;
                        _ = A.rv(P);
                        f[i] = P;
                        f[p] = l ? g : A.nh(g, "h");
                        var w = u && _ ? u === "*this" ? _ : A.rv(_[u]) : undefined;
                        h = S(w);
                        D(a, h);
                        t(r, f, h, o)
                    }
                } else if (d === "O") {
                    var g = 0;
                    for (var O in j) {
                        P = j[O];
                        P = A.hn(P) === "n" ? A.nh(P, "h") : P;
                        _ = A.rv(P);
                        f[i] = P;
                        f[p] = l ? O : A.nh(O, "h");
                        var w = u && _ ? u === "*this" ? _ : A.rv(_[u]) : undefined;
                        h = S(w);
                        D(a, h);
                        t(r, f, h, o);
                        g++
                    }
                } else if (d === "S") {
                    for (var g = 0; g < j.length; g++) {
                        P = A.nh(j[g], "h");
                        f[i] = P;
                        f[p] = l ? g : A.nh(g, "h");
                        h = S(e[g] + g);
                        D(a, h);
                        t(r, f, h, o)
                    }
                } else if (d === "N") {
                    for (var g = 0; g < j; g++) {
                        P = A.nh(g, "h");
                        f[i] = P;
                        f[p] = l ? g : A.nh(g, "h");
                        h = S(g);
                        D(a, h);
                        t(r, f, h, o)
                    }
                } else {}
            }
            if (v) {
                f[i] = s
            } else {
                delete f[i]
            }
            if (c) {
                f[p] = y
            } else {
                delete f[p]
            }
        }

        function N(e) {
            if (A.hn(e) == "h") return true;
            if (typeof e !== "object") return false;
            for (var t in e) {
                if (e.hasOwnProperty(t)) {
                    if (N(e[t])) return true
                }
            }
            return false
        }

        function b(e, t, r, n, o) {
            var a = false;
            var i = K(n, "", 2);
            if (o.ap && i && i.constructor === Function) {
                t = "$wxs:" + t;
                e.attr["$gdc"] = K
            }
            if (o.is_affected || N(n)) {
                e.n.push(t);
                e.raw[t] = n
            }
            e.attr[t] = i
        }

        function d(e, t, r, n, o, a) {
            a.opindex = r;
            var i = {},
                p;
            var u = c(z[r], n, o, a, i);
            b(e, t, r, u, i)
        }

        function h(e, t, r, n, o, a, i) {
            i.opindex = n;
            var p = {},
                u;
            var l = c(e[n], o, a, i, p);
            b(t, r, n, l, p)
        }

        function p(e, t, r, n) {
            n.opindex = e;
            var o = {};
            var a = c(z[e], t, r, n, o);
            return a && a.constructor === Function ? undefined : a
        }

        function l(e, t, r, n, o) {
            o.opindex = t;
            var a = {};
            var i = c(e[t], r, n, o, a);
            return i && i.constructor === Function ? undefined : i
        }

        function _(e, t, r, n, o) {
            var o = o || {};
            n.opindex = e;
            return f(z[e], t, r, n, o)
        }

        function w(e, t, r, n, o, a) {
            var a = a || {};
            o.opindex = t;
            return f(e[t], r, n, o, a)
        }

        function O(e, t, r, n, o, a, i, p, u) {
            var l = {};
            var f = _(e, r, n, o);
            y(f, t, r, n, o, a, i, p, u)
        }

        function j(e, t, r, n, o, a, i, p, u, l) {
            var f = {};
            var v = w(e, t, n, o, a);
            y(v, r, n, o, a, i, p, u, l)
        }

        function P(e, t, r, n, o, a) {
            var i = v(e);
            var p = 0;
            for (var u = 0; u < t.length; u += 2) {
                if (p + t[u + 1] < 0) {
                    i.attr[t[u]] = true
                } else {
                    d(i, t[u], p + t[u + 1], n, o, a);
                    if (p === 0) p = t[u + 1]
                }
            }
            for (var u = 0; u < r.length; u += 2) {
                if (p + r[u + 1] < 0) {
                    i.generics[r[u]] = ""
                } else {
                    var l = c(z[p + r[u + 1]], n, o, a);
                    if (l != "") l = "wx-" + l;
                    i.generics[r[u]] = l;
                    if (p === 0) p = r[u + 1]
                }
            }
            return i
        }

        function M(e, t, r, n, o, a, i) {
            var p = v(t);
            var u = 0;
            for (var l = 0; l < r.length; l += 2) {
                if (u + r[l + 1] < 0) {
                    p.attr[r[l]] = true
                } else {
                    h(e, p, r[l], u + r[l + 1], o, a, i);
                    if (u === 0) u = r[l + 1]
                }
            }
            for (var l = 0; l < n.length; l += 2) {
                if (u + n[l + 1] < 0) {
                    p.generics[n[l]] = ""
                } else {
                    var f = c(e[u + n[l + 1]], o, a, i);
                    if (f != "") f = "wx-" + f;
                    p.generics[n[l]] = f;
                    if (u === 0) u = n[l + 1]
                }
            }
            return p
        }
        var m = function() {
            if (typeof __WXML_GLOBAL__ === "undefined" || undefined === __WXML_GLOBAL__.wxs_nf_init) {
                x();
                C();
                k();
                U();
                I();
                L();
                E();
                R();
                F()
            }
            if (typeof __WXML_GLOBAL__ !== "undefined") __WXML_GLOBAL__.wxs_nf_init = true
        };
        var x = function() {
            Object.defineProperty(Object.prototype, "nv_constructor", {
                writable: true,
                value: "Object"
            });
            Object.defineProperty(Object.prototype, "nv_toString", {
                writable: true,
                value: function() {
                    return "[object Object]"
                }
            })
        };
        var C = function() {
            Object.defineProperty(Function.prototype, "nv_constructor", {
                writable: true,
                value: "Function"
            });
            Object.defineProperty(Function.prototype, "nv_length", {get: function() {
                    return this.length
                },
                set: function() {}
            });
            Object.defineProperty(Function.prototype, "nv_toString", {
                writable: true,
                value: function() {
                    return "[function Function]"
                }
            })
        };
        var k = function() {
            Object.defineProperty(Array.prototype, "nv_toString", {
                writable: true,
                value: function() {
                    return this.nv_join()
                }
            });
            Object.defineProperty(Array.prototype, "nv_join", {
                writable: true,
                value: function(e) {
                    e = undefined == e ? "," : e;
                    var t = "";
                    for (var r = 0; r < this.length; ++r) {
                        if (0 != r) t += e;
                        if (null == this[r] || undefined == this[r]) t += "";
                        else if (typeof this[r] == "function") t += this[r].nv_toString();
                        else if (typeof this[r] == "object" && this[r].nv_constructor === "Array") t += this[r].nv_join();
                        else t += this[r].toString()
                    }
                    return t
                }
            });
            Object.defineProperty(Array.prototype, "nv_constructor", {
                writable: true,
                value: "Array"
            });
            Object.defineProperty(Array.prototype, "nv_concat", {
                writable: true,
                value: Array.prototype.concat
            });
            Object.defineProperty(Array.prototype, "nv_pop", {
                writable: true,
                value: Array.prototype.pop
            });
            Object.defineProperty(Array.prototype, "nv_push", {
                writable: true,
                value: Array.prototype.push
            });
            Object.defineProperty(Array.prototype, "nv_reverse", {
                writable: true,
                value: Array.prototype.reverse
            });
            Object.defineProperty(Array.prototype, "nv_shift", {
                writable: true,
                value: Array.prototype.shift
            });
            Object.defineProperty(Array.prototype, "nv_slice", {
                writable: true,
                value: Array.prototype.slice
            });
            Object.defineProperty(Array.prototype, "nv_sort", {
                writable: true,
                value: Array.prototype.sort
            });
            Object.defineProperty(Array.prototype, "nv_splice", {
                writable: true,
                value: Array.prototype.splice
            });
            Object.defineProperty(Array.prototype, "nv_unshift", {
                writable: true,
                value: Array.prototype.unshift
            });
            Object.defineProperty(Array.prototype, "nv_indexOf", {
                writable: true,
                value: Array.prototype.indexOf
            });
            Object.defineProperty(Array.prototype, "nv_lastIndexOf", {
                writable: true,
                value: Array.prototype.lastIndexOf
            });
            Object.defineProperty(Array.prototype, "nv_every", {
                writable: true,
                value: Array.prototype.every
            });
            Object.defineProperty(Array.prototype, "nv_some", {
                writable: true,
                value: Array.prototype.some
            });
            Object.defineProperty(Array.prototype, "nv_forEach", {
                writable: true,
                value: Array.prototype.forEach
            });
            Object.defineProperty(Array.prototype, "nv_map", {
                writable: true,
                value: Array.prototype.map
            });
            Object.defineProperty(Array.prototype, "nv_filter", {
                writable: true,
                value: Array.prototype.filter
            });
            Object.defineProperty(Array.prototype, "nv_reduce", {
                writable: true,
                value: Array.prototype.reduce
            });
            Object.defineProperty(Array.prototype, "nv_reduceRight", {
                writable: true,
                value: Array.prototype.reduceRight
            });
            Object.defineProperty(Array.prototype, "nv_length", {get: function() {
                    return this.length
                },
                set: function(e) {
                    this.length = e
                }
            })
        };
        var U = function() {
            Object.defineProperty(String.prototype, "nv_constructor", {
                writable: true,
                value: "String"
            });
            Object.defineProperty(String.prototype, "nv_toString", {
                writable: true,
                value: String.prototype.toString
            });
            Object.defineProperty(String.prototype, "nv_valueOf", {
                writable: true,
                value: String.prototype.valueOf
            });
            Object.defineProperty(String.prototype, "nv_charAt", {
                writable: true,
                value: String.prototype.charAt
            });
            Object.defineProperty(String.prototype, "nv_charCodeAt", {
                writable: true,
                value: String.prototype.charCodeAt
            });
            Object.defineProperty(String.prototype, "nv_concat", {
                writable: true,
                value: String.prototype.concat
            });
            Object.defineProperty(String.prototype, "nv_indexOf", {
                writable: true,
                value: String.prototype.indexOf
            });
            Object.defineProperty(String.prototype, "nv_lastIndexOf", {
                writable: true,
                value: String.prototype.lastIndexOf
            });
            Object.defineProperty(String.prototype, "nv_localeCompare", {
                writable: true,
                value: String.prototype.localeCompare
            });
            Object.defineProperty(String.prototype, "nv_match", {
                writable: true,
                value: String.prototype.match
            });
            Object.defineProperty(String.prototype, "nv_replace", {
                writable: true,
                value: String.prototype.replace
            });
            Object.defineProperty(String.prototype, "nv_search", {
                writable: true,
                value: String.prototype.search
            });
            Object.defineProperty(String.prototype, "nv_slice", {
                writable: true,
                value: String.prototype.slice
            });
            Object.defineProperty(String.prototype, "nv_split", {
                writable: true,
                value: String.prototype.split
            });
            Object.defineProperty(String.prototype, "nv_substring", {
                writable: true,
                value: String.prototype.substring
            });
            Object.defineProperty(String.prototype, "nv_toLowerCase", {
                writable: true,
                value: String.prototype.toLowerCase
            });
            Object.defineProperty(String.prototype, "nv_toLocaleLowerCase", {
                writable: true,
                value: String.prototype.toLocaleLowerCase
            });
            Object.defineProperty(String.prototype, "nv_toUpperCase", {
                writable: true,
                value: String.prototype.toUpperCase
            });
            Object.defineProperty(String.prototype, "nv_toLocaleUpperCase", {
                writable: true,
                value: String.prototype.toLocaleUpperCase
            });
            Object.defineProperty(String.prototype, "nv_trim", {
                writable: true,
                value: String.prototype.trim
            });
            Object.defineProperty(String.prototype, "nv_length", {get: function() {
                    return this.length
                },
                set: function(e) {
                    this.length = e
                }
            })
        };
        var I = function() {
            Object.defineProperty(Boolean.prototype, "nv_constructor", {
                writable: true,
                value: "Boolean"
            });
            Object.defineProperty(Boolean.prototype, "nv_toString", {
                writable: true,
                value: Boolean.prototype.toString
            });
            Object.defineProperty(Boolean.prototype, "nv_valueOf", {
                writable: true,
                value: Boolean.prototype.valueOf
            })
        };
        var L = function() {
            Object.defineProperty(Number, "nv_MAX_VALUE", {
                writable: false,
                value: Number.MAX_VALUE
            });
            Object.defineProperty(Number, "nv_MIN_VALUE", {
                writable: false,
                value: Number.MIN_VALUE
            });
            Object.defineProperty(Number, "nv_NEGATIVE_INFINITY", {
                writable: false,
                value: Number.NEGATIVE_INFINITY
            });
            Object.defineProperty(Number, "nv_POSITIVE_INFINITY", {
                writable: false,
                value: Number.POSITIVE_INFINITY
            });
            Object.defineProperty(Number.prototype, "nv_constructor", {
                writable: true,
                value: "Number"
            });
            Object.defineProperty(Number.prototype, "nv_toString", {
                writable: true,
                value: Number.prototype.toString
            });
            Object.defineProperty(Number.prototype, "nv_toLocaleString", {
                writable: true,
                value: Number.prototype.toLocaleString
            });
            Object.defineProperty(Number.prototype, "nv_valueOf", {
                writable: true,
                value: Number.prototype.valueOf
            });
            Object.defineProperty(Number.prototype, "nv_toFixed", {
                writable: true,
                value: Number.prototype.toFixed
            });
            Object.defineProperty(Number.prototype, "nv_toExponential", {
                writable: true,
                value: Number.prototype.toExponential
            });
            Object.defineProperty(Number.prototype, "nv_toPrecision", {
                writable: true,
                value: Number.prototype.toPrecision
            })
        };
        var E = function() {
            Object.defineProperty(Math, "nv_E", {
                writable: false,
                value: Math.E
            });
            Object.defineProperty(Math, "nv_LN10", {
                writable: false,
                value: Math.LN10
            });
            Object.defineProperty(Math, "nv_LN2", {
                writable: false,
                value: Math.LN2
            });
            Object.defineProperty(Math, "nv_LOG2E", {
                writable: false,
                value: Math.LOG2E
            });
            Object.defineProperty(Math, "nv_LOG10E", {
                writable: false,
                value: Math.LOG10E
            });
            Object.defineProperty(Math, "nv_PI", {
                writable: false,
                value: Math.PI
            });
            Object.defineProperty(Math, "nv_SQRT1_2", {
                writable: false,
                value: Math.SQRT1_2
            });
            Object.defineProperty(Math, "nv_SQRT2", {
                writable: false,
                value: Math.SQRT2
            });
            Object.defineProperty(Math, "nv_abs", {
                writable: false,
                value: Math.abs
            });
            Object.defineProperty(Math, "nv_acos", {
                writable: false,
                value: Math.acos
            });
            Object.defineProperty(Math, "nv_asin", {
                writable: false,
                value: Math.asin
            });
            Object.defineProperty(Math, "nv_atan", {
                writable: false,
                value: Math.atan
            });
            Object.defineProperty(Math, "nv_atan2", {
                writable: false,
                value: Math.atan2
            });
            Object.defineProperty(Math, "nv_ceil", {
                writable: false,
                value: Math.ceil
            });
            Object.defineProperty(Math, "nv_cos", {
                writable: false,
                value: Math.cos
            });
            Object.defineProperty(Math, "nv_exp", {
                writable: false,
                value: Math.exp
            });
            Object.defineProperty(Math, "nv_floor", {
                writable: false,
                value: Math.floor
            });
            Object.defineProperty(Math, "nv_log", {
                writable: false,
                value: Math.log
            });
            Object.defineProperty(Math, "nv_max", {
                writable: false,
                value: Math.max
            });
            Object.defineProperty(Math, "nv_min", {
                writable: false,
                value: Math.min
            });
            Object.defineProperty(Math, "nv_pow", {
                writable: false,
                value: Math.pow
            });
            Object.defineProperty(Math, "nv_random", {
                writable: false,
                value: Math.random
            });
            Object.defineProperty(Math, "nv_round", {
                writable: false,
                value: Math.round
            });
            Object.defineProperty(Math, "nv_sin", {
                writable: false,
                value: Math.sin
            });
            Object.defineProperty(Math, "nv_sqrt", {
                writable: false,
                value: Math.sqrt
            });
            Object.defineProperty(Math, "nv_tan", {
                writable: false,
                value: Math.tan
            })
        };
        var R = function() {
            Object.defineProperty(Date.prototype, "nv_constructor", {
                writable: true,
                value: "Date"
            });
            Object.defineProperty(Date, "nv_parse", {
                writable: true,
                value: Date.parse
            });
            Object.defineProperty(Date, "nv_UTC", {
                writable: true,
                value: Date.UTC
            });
            Object.defineProperty(Date, "nv_now", {
                writable: true,
                value: Date.now
            });
            Object.defineProperty(Date.prototype, "nv_toString", {
                writable: true,
                value: Date.prototype.toString
            });
            Object.defineProperty(Date.prototype, "nv_toDateString", {
                writable: true,
                value: Date.prototype.toDateString
            });
            Object.defineProperty(Date.prototype, "nv_toTimeString", {
                writable: true,
                value: Date.prototype.toTimeString
            });
            Object.defineProperty(Date.prototype, "nv_toLocaleString", {
                writable: true,
                value: Date.prototype.toLocaleString
            });
            Object.defineProperty(Date.prototype, "nv_toLocaleDateString", {
                writable: true,
                value: Date.prototype.toLocaleDateString
            });
            Object.defineProperty(Date.prototype, "nv_toLocaleTimeString", {
                writable: true,
                value: Date.prototype.toLocaleTimeString
            });
            Object.defineProperty(Date.prototype, "nv_valueOf", {
                writable: true,
                value: Date.prototype.valueOf
            });
            Object.defineProperty(Date.prototype, "nv_getTime", {
                writable: true,
                value: Date.prototype.getTime
            });
            Object.defineProperty(Date.prototype, "nv_getFullYear", {
                writable: true,
                value: Date.prototype.getFullYear
            });
            Object.defineProperty(Date.prototype, "nv_getUTCFullYear", {
                writable: true,
                value: Date.prototype.getUTCFullYear
            });
            Object.defineProperty(Date.prototype, "nv_getMonth", {
                writable: true,
                value: Date.prototype.getMonth
            });
            Object.defineProperty(Date.prototype, "nv_getUTCMonth", {
                writable: true,
                value: Date.prototype.getUTCMonth
            });
            Object.defineProperty(Date.prototype, "nv_getDate", {
                writable: true,
                value: Date.prototype.getDate
            });
            Object.defineProperty(Date.prototype, "nv_getUTCDate", {
                writable: true,
                value: Date.prototype.getUTCDate
            });
            Object.defineProperty(Date.prototype, "nv_getDay", {
                writable: true,
                value: Date.prototype.getDay
            });
            Object.defineProperty(Date.prototype, "nv_getUTCDay", {
                writable: true,
                value: Date.prototype.getUTCDay
            });
            Object.defineProperty(Date.prototype, "nv_getHours", {
                writable: true,
                value: Date.prototype.getHours
            });
            Object.defineProperty(Date.prototype, "nv_getUTCHours", {
                writable: true,
                value: Date.prototype.getUTCHours
            });
            Object.defineProperty(Date.prototype, "nv_getMinutes", {
                writable: true,
                value: Date.prototype.getMinutes
            });
            Object.defineProperty(Date.prototype, "nv_getUTCMinutes", {
                writable: true,
                value: Date.prototype.getUTCMinutes
            });
            Object.defineProperty(Date.prototype, "nv_getSeconds", {
                writable: true,
                value: Date.prototype.getSeconds
            });
            Object.defineProperty(Date.prototype, "nv_getUTCSeconds", {
                writable: true,
                value: Date.prototype.getUTCSeconds
            });
            Object.defineProperty(Date.prototype, "nv_getMilliseconds", {
                writable: true,
                value: Date.prototype.getMilliseconds
            });
            Object.defineProperty(Date.prototype, "nv_getUTCMilliseconds", {
                writable: true,
                value: Date.prototype.getUTCMilliseconds
            });
            Object.defineProperty(Date.prototype, "nv_getTimezoneOffset", {
                writable: true,
                value: Date.prototype.getTimezoneOffset
            });
            Object.defineProperty(Date.prototype, "nv_setTime", {
                writable: true,
                value: Date.prototype.setTime
            });
            Object.defineProperty(Date.prototype, "nv_setMilliseconds", {
                writable: true,
                value: Date.prototype.setMilliseconds
            });
            Object.defineProperty(Date.prototype, "nv_setUTCMilliseconds", {
                writable: true,
                value: Date.prototype.setUTCMilliseconds
            });
            Object.defineProperty(Date.prototype, "nv_setSeconds", {
                writable: true,
                value: Date.prototype.setSeconds
            });
            Object.defineProperty(Date.prototype, "nv_setUTCSeconds", {
                writable: true,
                value: Date.prototype.setUTCSeconds
            });
            Object.defineProperty(Date.prototype, "nv_setMinutes", {
                writable: true,
                value: Date.prototype.setMinutes
            });
            Object.defineProperty(Date.prototype, "nv_setUTCMinutes", {
                writable: true,
                value: Date.prototype.setUTCMinutes
            });
            Object.defineProperty(Date.prototype, "nv_setHours", {
                writable: true,
                value: Date.prototype.setHours
            });
            Object.defineProperty(Date.prototype, "nv_setUTCHours", {
                writable: true,
                value: Date.prototype.setUTCHours
            });
            Object.defineProperty(Date.prototype, "nv_setDate", {
                writable: true,
                value: Date.prototype.setDate
            });
            Object.defineProperty(Date.prototype, "nv_setUTCDate", {
                writable: true,
                value: Date.prototype.setUTCDate
            });
            Object.defineProperty(Date.prototype, "nv_setMonth", {
                writable: true,
                value: Date.prototype.setMonth
            });
            Object.defineProperty(Date.prototype, "nv_setUTCMonth", {
                writable: true,
                value: Date.prototype.setUTCMonth
            });
            Object.defineProperty(Date.prototype, "nv_setFullYear", {
                writable: true,
                value: Date.prototype.setFullYear
            });
            Object.defineProperty(Date.prototype, "nv_setUTCFullYear", {
                writable: true,
                value: Date.prototype.setUTCFullYear
            });
            Object.defineProperty(Date.prototype, "nv_toUTCString", {
                writable: true,
                value: Date.prototype.toUTCString
            });
            Object.defineProperty(Date.prototype, "nv_toISOString", {
                writable: true,
                value: Date.prototype.toISOString
            });
            Object.defineProperty(Date.prototype, "nv_toJSON", {
                writable: true,
                value: Date.prototype.toJSON
            })
        };
        var F = function() {
            Object.defineProperty(RegExp.prototype, "nv_constructor", {
                writable: true,
                value: "RegExp"
            });
            Object.defineProperty(RegExp.prototype, "nv_exec", {
                writable: true,
                value: RegExp.prototype.exec
            });
            Object.defineProperty(RegExp.prototype, "nv_test", {
                writable: true,
                value: RegExp.prototype.test
            });
            Object.defineProperty(RegExp.prototype, "nv_toString", {
                writable: true,
                value: RegExp.prototype.toString
            });
            Object.defineProperty(RegExp.prototype, "nv_source", {get: function() {
                    return this.source
                },
                set: function() {}
            });
            Object.defineProperty(RegExp.prototype, "nv_global", {get: function() {
                    return this.global
                },
                set: function() {}
            });
            Object.defineProperty(RegExp.prototype, "nv_ignoreCase", {get: function() {
                    return this.ignoreCase
                },
                set: function() {}
            });
            Object.defineProperty(RegExp.prototype, "nv_multiline", {get: function() {
                    return this.multiline
                },
                set: function() {}
            });
            Object.defineProperty(RegExp.prototype, "nv_lastIndex", {get: function() {
                    return this.lastIndex
                },
                set: function(e) {
                    this.lastIndex = e
                }
            })
        };
        m();
        var J = function() {
            var e = Array.prototype.slice.call(arguments);
            e.unshift(Date);
            return new(Function.prototype.bind.apply(Date, e))
        };
        var B = function() {
            var e = Array.prototype.slice.call(arguments);
            e.unshift(RegExp);
            return new(Function.prototype.bind.apply(RegExp, e))
        };
        var Y = {};
        Y.nv_log = function() {
            var e = "WXSRT:";
            for (var t = 0; t < arguments.length; ++t) e += arguments[t] + " ";
            console.log(e)
        };
        var G = parseInt,
            X = parseFloat,
            H = isNaN,
            V = isFinite,
            $ = decodeURI,
            W = decodeURIComponent,
            Q = encodeURI,
            q = encodeURIComponent;

        function K(e, t, r) {
            e = A.rv(e);
            if (e === null || e === undefined) return e;
            if (typeof e === "string" || typeof e === "boolean" || typeof e === "number") return e;
            if (e.constructor === Object) {
                var n = {};
                for (var o in e)
                    if (Object.prototype.hasOwnProperty.call(e, o))
                        if (undefined === t) n[o.substring(3)] = K(e[o], t, r);
                        else n[t + o] = K(e[o], t, r);
                return n
            }
            if (e.constructor === Array) {
                var n = [];
                for (var a = 0; a < e.length; a++) n.push(K(e[a], t, r));
                return n
            }
            if (e.constructor === Date) {
                var n = new Date;
                n.setTime(e.getTime());
                return n
            }
            if (e.constructor === RegExp) {
                var i = "";
                if (e.global) i += "g";
                if (e.ignoreCase) i += "i";
                if (e.multiline) i += "m";
                return new RegExp(e.source, i)
            }
            if (r && typeof e === "function") {
                if (r == 1) return K(e(), undefined, 2);
                if (r == 2) return e
            }
            return null
        }
        var Z = {};
        Z.nv_stringify = function(e) {
            JSON.stringify(e);
            return JSON.stringify(K(e))
        };
        Z.nv_parse = function(e) {
            if (e === undefined) return undefined;
            var t = JSON.parse(e);
            return K(t, "nv_")
        };

        function ee(e, t, r, n) {
            e.extraAttr = {
                t_action: t,
                t_rawid: r
            };
            if (typeof n != "undefined") e.extraAttr.t_cid = n
        }

        function te() {
            if (typeof __globalThis.__webview_engine_version__ == "undefined") return 0;
            return __globalThis.__webview_engine_version__
        }

        function re(e, t, r, n, o, a) {
            var i = ne(t, r, n);
            if (i) e.push(i);
            else {
                e.push("");
                u(n + ":import:" + o + ":" + a + ": Path `" + t + "` not found from `" + n + "`.")
            }
        }

        function ne(e, t, r) {
            if (e[0] != "/") {
                var n = r.split("/");
                n.pop();
                var o = e.split("/");
                for (var a = 0; a < o.length; a++) {
                    if (o[a] == "..") n.pop();
                    else if (!o[a] || o[a] == ".") continue;
                    else n.push(o[a])
                }
                e = n.join("/")
            }
            if (r[0] == "." && e[0] == "/") e = "." + e;
            if (t[e]) return e;
            if (t[e + ".wxml"]) return e + ".wxml"
        }

        function oe(e, t, r, n) {
            if (!t) return;
            if (n[e][t]) return n[e][t];
            for (var o = r[e].i.length - 1; o >= 0; o--) {
                if (r[e].i[o] && n[r[e].i[o]][t]) return n[r[e].i[o]][t]
            }
            for (var o = r[e].ti.length - 1; o >= 0; o--) {
                var a = ne(r[e].ti[o], r, e);
                if (a && n[a][t]) return n[a][t]
            }
            var i = ae(r, e);
            for (var o = 0; o < i.length; o++) {
                if (i[o] && n[i[o]][t]) return n[i[o]][t]
            }
            for (var p = r[e].j.length - 1; p >= 0; p--)
                if (r[e].j[p]) {
                    for (var a = r[r[e].j[p]].ti.length - 1; a >= 0; a--) {
                        var u = ne(r[r[e].j[p]].ti[a], r, e);
                        if (u && n[u][t]) {
                            return n[u][t]
                        }
                    }
                }
        }

        function ae(e, t) {
            if (!t) return [];
            if ($gaic[t]) {
                return $gaic[t]
            }
            var r = [],
                n = [],
                o = 0,
                a = 0,
                i = {},
                p = {};
            n.push(t);
            p[t] = true;
            a++;
            while (o < a) {
                var u = n[o++];
                for (var l = 0; l < e[u].ic.length; l++) {
                    var f = e[u].ic[l];
                    var v = ne(f, e, u);
                    if (v && !p[v]) {
                        p[v] = true;
                        n.push(v);
                        a++
                    }
                }
                for (var l = 0; u != t && l < e[u].ti.length; l++) {
                    var c = e[u].ti[l];
                    var s = ne(c, e, u);
                    if (s && !i[s]) {
                        i[s] = true;
                        r.push(s)
                    }
                }
            }
            $gaic[t] = r;
            return r
        }
        var ie = {};

        function pe(e, t, r, n, o, a, i) {
            var p = ne(e, t, r);
            t[r].j.push(p);
            if (p) {
                if (ie[p]) {
                    u("-1:include:-1:-1: `" + e + "` is being included in a loop, will be stop.");
                    return
                }
                ie[p] = true;
                try {
                    t[p].f(n, o, a, i)
                } catch (n) {}
                ie[p] = false
            } else {
                u(r + ":include:-1:-1: Included path `" + e + "` not found from `" + r + "`.")
            }
        }

        function ue(e, t, r, n) {
            u(t + ":template:" + r + ":" + n + ": Template `" + e + "` not found.")
        }

        function le(e) {
            var t = false;
            delete e.properities;
            delete e.n;
            if (e.children) {
                do {
                    t = false;
                    var r = [];
                    for (var n = 0; n < e.children.length; n++) {
                        var o = e.children[n];
                        if (o.tag == "virtual") {
                            t = true;
                            for (var a = 0; o.children && a < o.children.length; a++) {
                                r.push(o.children[a])
                            }
                        } else {
                            r.push(o)
                        }
                    }
                    e.children = r
                } while (t);
                for (var n = 0; n < e.children.length; n++) {
                    le(e.children[n])
                }
            }
            return e
        }

        function fe(e) {
            if (e.tag == "wx-wx-scope") {
                e.tag = "virtual";
                e.wxCkey = "11";
                e["wxScopeData"] = e.attr["wx:scope-data"];
                delete e.n;
                delete e.raw;
                delete e.generics;
                delete e.attr
            }
            for (var t = 0; e.children && t < e.children.length; t++) {
                fe(e.children[t])
            }
            return e
        }
        return {
            a: D,
            b: S,
            c: v,
            d: e,
            e: t,
            f: u,
            g: r,
            h: s,
            i: n,
            j: o,
            k: A,
            l: T,
            m: a,
            n: f,
            o: c,
            p: i,
            q: y,
            r: N,
            s: b,
            t: d,
            u: h,
            v: p,
            w: l,
            x: _,
            y: w,
            z: O,
            A: j,
            B: P,
            C: M,
            D: J,
            E: B,
            F: Y,
            G: G,
            H: X,
            I: H,
            J: V,
            K: $,
            L: W,
            M: Q,
            N: q,
            O: K,
            P: Z,
            Q: ee,
            R: te,
            S: re,
            T: ne,
            U: oe,
            V: ae,
            W: ie,
            X: pe,
            Y: ue,
            Z: le,
            aa: fe
        }
    }()
});
Object.freeze(__g);
g = "";
__wxAppCode__['pages/qbank/chapter.json'] = {
    "usingComponents": {
        "xk-action-sheet": "/components/common/action-sheet",
        "modal": "/components/common/modal-pro",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/qbank/child.json'] = {
    "usingComponents": {
        "payment": "/components/payment/payment",
        "xk-qbank-chapter": "/components/qbank/chapter",
        "xk-qbank-paper": "/components/qbank/paper",
        "xk-action-sheet": "/components/common/action-sheet",
        "load-more": "/components/common/load-more",
        "paper-item": "/components/qbank/paper-item",
        "paper-modal": "/components/qbank/paper-modal",
        "open-modal": "/components/common/modal-open",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/qbank/dayPractice/list.json'] = {
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/qbank/dayPractice/result.json'] = {
    "usingComponents": {
        "arc-bar": "/components/wx-charts/arcbar",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/qbank/detail.json'] = {
    "usingComponents": {
        "payment": "/components/payment/payment",
        "xk-share": "/components/share/share",
        "pintuan": "/components/common/pintuan",
        "time-discount": "/components/common/time-discount",
        "price-cut": "/components/common/price-cut",
        "open-modal": "/components/common/modal-open",
        "add-group": "/components/common/add-group",
        "skus": "/components/common/skus",
        "html-parse": "/components/htmlParse/index",
        "gift-list": "/components/common/gift-list",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/qbank/history.json'] = {
    "usingComponents": {
        "paper-modal": "/components/qbank/paper-modal",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/qbank/index.json'] = {
    "navigationBarTextStyle": "black",
    "usingComponents": {
        "tab-qbank": "/components/nav/qbank",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/qbank/list.json'] = {
    "usingComponents": {
        "xk-load-more": "/components/common/load-more",
        "xk-share": "/components/share/share",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/qbank/notes.json'] = {
    "usingComponents": {
        "xk-load-more": "/components/common/load-more",
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};
__wxAppCode__['pages/qbank/rank.json'] = {
    "usingComponents": {
        "mp-privacy": "/components/mp/privacy",
        "nav-bar": "/components/base/nav-bar",
        "xk-empty": "/components/common/empty",
        "xk-empty-inline": "/components/common/empty-inline",
        "xk-list-title": "/components/common/list-title",
        "xk-login": "/components/common/login"
    }
};;
var __WXML_DEP__ = __WXML_DEP__ || {}; /*v0.5vv_20211229_syb_scopedata*/
global.__wcc_version__ = 'v0.5vv_20211229_syb_scopedata';
global.__wcc_version_info__ = {
    "customComponents": true,
    "fixZeroRpx": true,
    "propValueDeepCopy": false
};
var $gwxc
var $gaic = {}
$gwx5 = function(_, _v, _n, _p, _s, _wp, _wl, $gwn, $gwl, $gwh, wh, $gstack, $gwrt, gra, grb, TestTest, wfor, _ca, _da, _r, _rz, _o, _oz, _1, _1z, _2, _2z, _m, _mz, nv_getDate, nv_getRegExp, nv_console, nv_parseInt, nv_parseFloat, nv_isNaN, nv_isFinite, nv_decodeURI, nv_decodeURIComponent, nv_encodeURI, nv_encodeURIComponent, $gdc, nv_JSON, _af, _gv, _ai, _grp, _gd, _gapi, $ixc, _ic, _w, _ev, _tsd) {
    return function(path, global) {
        if (typeof global === 'undefined') {
            if (typeof __GWX_GLOBAL__ === 'undefined') global = {};
            else global = __GWX_GLOBAL__;
        }
        if (typeof __WXML_GLOBAL__ === 'undefined') {
            __WXML_GLOBAL__ = {};
        }
        __WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
        if (typeof $gwx === 'function') $gwx('init', global);
        var e_ = {}
        if (typeof(global.entrys) === 'undefined') global.entrys = {};
        e_ = global.entrys;
        var d_ = {}
        if (typeof(global.defines) === 'undefined') global.defines = {};
        d_ = global.defines;
        var f_ = {}
        if (typeof(global.modules) === 'undefined') global.modules = {};
        f_ = global.modules || {};
        var p_ = {}
        __WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
        __WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
        __WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
        var z = __WXML_GLOBAL__.ops_set.$gwx5 || [];
        __WXML_GLOBAL__.ops_set.$gwx5 = z;
        __WXML_GLOBAL__.ops_init.$gwx5 = true;
        var nv_require = function() {
            var nnm = {};
            var nom = {};
            return function(n) {
                if (n[0] === 'p' && n[1] === '_' && f_[n.slice(2)]) return f_[n.slice(2)];
                return function() {
                    if (!nnm[n]) return undefined;
                    try {
                        if (!nom[n]) nom[n] = nnm[n]();
                        return nom[n];
                    } catch (e) {
                        e.message = e.message.replace(/nv_/g, '');
                        var tmp = e.stack.substring(0, e.stack.lastIndexOf(n));
                        e.stack = tmp.substring(0, tmp.lastIndexOf('\n'));
                        e.stack = e.stack.replace(/\snv_/g, ' ');
                        e.stack = $gstack(e.stack);
                        e.stack += '\n    at ' + n.substring(2);
                        console.error(e);
                    }
                }
            }
        }()
        var x = [];
        if (path && e_[path]) {
            return function(env, dd, global) {
                $gwxc = 0;
                var root = {
                    "tag": "wx-page"
                };
                root.children = [];
                g = "$gwx5";
                var main = e_[path].f
                if (typeof global === "undefined") global = {};
                global.f = $gdc(f_[path], "", 1);
                try {
                    main(env, {}, root, global);
                    _tsd(root)
                } catch (err) {
                    console.log(err)
                };
                g = "";
                return root;
            }
        }
    }
}(__g.a, __g.b, __g.c, __g.d, __g.e, __g.f, __g.g, __g.h, __g.i, __g.j, __g.k, __g.l, __g.m, __g.n, __g.o, __g.p, __g.q, __g.r, __g.s, __g.t, __g.u, __g.v, __g.w, __g.x, __g.y, __g.z, __g.A, __g.B, __g.C, __g.D, __g.E, __g.F, __g.G, __g.H, __g.I, __g.J, __g.K, __g.L, __g.M, __g.N, __g.O, __g.P, __g.Q, __g.R, __g.S, __g.T, __g.U, __g.V, __g.W, __g.X, __g.Y, __g.Z, __g.aa);
if (__vd_version_info__.delayedGwx || true) $gwx5();
$gwx5_XC_0 = function(_, _v, _n, _p, _s, _wp, _wl, $gwn, $gwl, $gwh, wh, $gstack, $gwrt, gra, grb, TestTest, wfor, _ca, _da, _r, _rz, _o, _oz, _1, _1z, _2, _2z, _m, _mz, nv_getDate, nv_getRegExp, nv_console, nv_parseInt, nv_parseFloat, nv_isNaN, nv_isFinite, nv_decodeURI, nv_decodeURIComponent, nv_encodeURI, nv_encodeURIComponent, $gdc, nv_JSON, _af, _gv, _ai, _grp, _gd, _gapi, $ixc, _ic, _w, _ev, _tsd) {
    return function(path, global) {
        if (typeof global === 'undefined') {
            if (typeof __GWX_GLOBAL__ === 'undefined') global = {};
            else global = __GWX_GLOBAL__;
        }
        if (typeof __WXML_GLOBAL__ === 'undefined') {
            __WXML_GLOBAL__ = {};
        }
        __WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
        var e_ = {}
        if (typeof(global.entrys) === 'undefined') global.entrys = {};
        e_ = global.entrys;
        var d_ = {}
        if (typeof(global.defines) === 'undefined') global.defines = {};
        d_ = global.defines;
        var f_ = {}
        if (typeof(global.modules) === 'undefined') global.modules = {};
        f_ = global.modules || {};
        var p_ = {}
        __WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
        __WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
        __WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
        var z = __WXML_GLOBAL__.ops_set.$gwx5_XC_0 || [];

        function gz$gwx5_XC_0_1() {
            if (__WXML_GLOBAL__.ops_cached.$gwx5_XC_0_1) return __WXML_GLOBAL__.ops_cached.$gwx5_XC_0_1
            __WXML_GLOBAL__.ops_cached.$gwx5_XC_0_1 = [];
            (function(z) {
                var a = 11;

                function Z(ops) {
                    z.push(ops)
                }
                Z([3, 'data-v-f15109f2'])
                Z([
                    [2, '+'],
                    [
                        [2, '+'],
                        [1, '--single:'],
                        [
                            [7],
                            [3, 'singleColor']
                        ]
                    ],
                    [1, ';']
                ])
                Z([3, '__l'])
                Z(z[0])
                Z([
                    [7],
                    [3, 'pageTitle']
                ])
                Z([3, '9488cff8-1'])
                Z([
                    [7],
                    [3, 'dataHasBeenLoaded']
                ])
                Z([
                    [2, '>'],
                    [
                        [6],
                        [
                            [7],
                            [3, '$root']
                        ],
                        [3, 'g0']
                    ],
                    [1, 1]
                ])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'g1']
                ])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [5],
                            [
                                [5],
                                [1, 'chapter-list']
                            ],
                            [1, 'data-v-f15109f2']
                        ],
                        [
                            [7],
                            [3, 'type']
                        ]
                    ]
                ])
                Z([3, 'i'])
                Z([3, 'chapter'])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'l0']
                ])
                Z(z[10])
                Z([3, '__e'])
                Z([3, 'chapter-item bottom-1px data-v-f15109f2'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, 'tap']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [
                                                    [5],
                                                    [
                                                        [5],
                                                        [1, 'selectItem']
                                                    ],
                                                    [
                                                        [4],
                                                        [
                                                            [5],
                                                            [1, '$0']
                                                        ]
                                                    ]
                                                ],
                                                [
                                                    [4],
                                                    [
                                                        [5],
                                                        [
                                                            [4],
                                                            [
                                                                [5],
                                                                [
                                                                    [4],
                                                                    [
                                                                        [5],
                                                                        [
                                                                            [5],
                                                                            [
                                                                                [5],
                                                                                [1, 'list']
                                                                            ],
                                                                            [1, '']
                                                                        ],
                                                                        [
                                                                            [7],
                                                                            [3, 'i']
                                                                        ]
                                                                    ]
                                                                ]
                                                            ]
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([
                    [2, '=='],
                    [
                        [6],
                        [
                            [6],
                            [
                                [7],
                                [3, 'chapter']
                            ],
                            [3, '$orig']
                        ],
                        [3, 'type']
                    ],
                    [1, 'classify']
                ])
                Z([
                    [2, '=='],
                    [
                        [7],
                        [3, 'type']
                    ],
                    [1, 'chapter']
                ])
                Z([3, 'chapter-right data-v-f15109f2'])
                Z(z[18])
                Z([
                    [2, '!'],
                    [
                        [7],
                        [3, 'isAuthorized']
                    ]
                ])
                Z([
                    [2, '=='],
                    [
                        [7],
                        [3, 'type']
                    ],
                    [1, 'favorite']
                ])
                Z([
                    [2, '=='],
                    [
                        [7],
                        [3, 'type']
                    ],
                    [1, 'wrong']
                ])
                Z(z[23])
                Z([
                    [2, '&&'],
                    [
                        [2, '=='],
                        [
                            [6],
                            [
                                [7],
                                [3, 'wrongQAutoRemove']
                            ],
                            [3, 'switch']
                        ],
                        [1, 1]
                    ],
                    [
                        [2, '>'],
                        [
                            [6],
                            [
                                [7],
                                [3, 'wrongQAutoRemove']
                            ],
                            [3, 'consecRightCount']
                        ],
                        [1, 0]
                    ]
                ])
                Z(z[2])
                Z(z[0])
                Z([3, '暂无相关内容'])
                Z([3, 'empty2'])
                Z([3, '9488cff8-2'])
                Z(z[2])
                Z([3, 'data-v-f15109f2 vue-ref'])
                Z([3, 'wrongModal'])
                Z([3, '9488cff8-3'])
                Z([
                    [4],
                    [
                        [5],
                        [1, 'default']
                    ]
                ])
                Z(z[2])
                Z(z[32])
                Z([3, 'actionSheet'])
                Z([3, '9488cff8-4'])
                Z(z[2])
                Z(z[14])
                Z(z[32])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^success']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'loginSuccess']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'login'])
                Z([3, '9488cff8-5'])
            })(__WXML_GLOBAL__.ops_cached.$gwx5_XC_0_1);
            return __WXML_GLOBAL__.ops_cached.$gwx5_XC_0_1
        }
        __WXML_GLOBAL__.ops_set.$gwx5_XC_0 = z;
        __WXML_GLOBAL__.ops_init.$gwx5_XC_0 = true;
        var x = ['./pages/qbank/chapter.wxml'];
        d_[x[0]] = {}
        var m0 = function(e, s, r, gg) {
            var z = gz$gwx5_XC_0_1()
            var oB = _mz(z, 'view', ['class', 0, 'style', 1], [], e, s, gg)
            var oD = _mz(z, 'nav-bar', ['bind:__l', 2, 'class', 1, 'title', 2, 'vueId', 3], [], e, s, gg)
            _(oB, oD)
            var xC = _v()
            _(oB, xC)
            if (_oz(z, 6, e, s, gg)) {
                xC.wxVkey = 1
                var fE = _v()
                _(xC, fE)
                if (_oz(z, 7, e, s, gg)) {
                    fE.wxVkey = 1
                }
                var cF = _v()
                _(xC, cF)
                if (_oz(z, 8, e, s, gg)) {
                    cF.wxVkey = 1
                    var hG = _n('view')
                    _rz(z, hG, 'class', 9, e, s, gg)
                    var cI = _v()
                    _(hG, cI)
                    var oJ = function(aL, lK, tM, gg) {
                        var bO = _mz(z, 'view', ['bindtap', 14, 'class', 1, 'data-event-opts', 2], [], aL, lK, gg)
                        var oP = _v()
                        _(bO, oP)
                        if (_oz(z, 17, aL, lK, gg)) {
                            oP.wxVkey = 1
                        } else {
                            oP.wxVkey = 2
                            var xQ = _v()
                            _(oP, xQ)
                            if (_oz(z, 18, aL, lK, gg)) {
                                xQ.wxVkey = 1
                            }
                            var cT = _n('view')
                            _rz(z, cT, 'class', 19, aL, lK, gg)
                            var hU = _v()
                            _(cT, hU)
                            if (_oz(z, 20, aL, lK, gg)) {
                                hU.wxVkey = 1
                                var oV = _v()
                                _(hU, oV)
                                if (_oz(z, 21, aL, lK, gg)) {
                                    oV.wxVkey = 1
                                }
                                oV.wxXCkey = 1
                            } else {
                                hU.wxVkey = 2
                            }
                            hU.wxXCkey = 1
                            _(oP, cT)
                            var oR = _v()
                            _(oP, oR)
                            if (_oz(z, 22, aL, lK, gg)) {
                                oR.wxVkey = 1
                            }
                            var fS = _v()
                            _(oP, fS)
                            if (_oz(z, 23, aL, lK, gg)) {
                                fS.wxVkey = 1
                            }
                            xQ.wxXCkey = 1
                            oR.wxXCkey = 1
                            fS.wxXCkey = 1
                        }
                        oP.wxXCkey = 1
                        _(tM, bO)
                        return tM
                    }
                    cI.wxXCkey = 2
                    _2z(z, 12, oJ, e, s, gg, cI, 'chapter', 'i', 'i')
                    var oH = _v()
                    _(hG, oH)
                    if (_oz(z, 24, e, s, gg)) {
                        oH.wxVkey = 1
                        var cW = _v()
                        _(oH, cW)
                        if (_oz(z, 25, e, s, gg)) {
                            cW.wxVkey = 1
                        }
                        cW.wxXCkey = 1
                    }
                    oH.wxXCkey = 1
                    _(cF, hG)
                } else {
                    cF.wxVkey = 2
                    var oX = _mz(z, 'xk-empty', ['bind:__l', 26, 'class', 1, 'text', 2, 'type', 3, 'vueId', 4], [], e, s, gg)
                    _(cF, oX)
                }
                fE.wxXCkey = 1
                cF.wxXCkey = 1
                cF.wxXCkey = 3
            }
            var lY = _mz(z, 'modal', ['bind:__l', 31, 'class', 1, 'data-ref', 2, 'vueId', 3, 'vueSlots', 4], [], e, s, gg)
            _(oB, lY)
            var aZ = _mz(z, 'xk-action-sheet', ['bind:__l', 36, 'class', 1, 'data-ref', 2, 'vueId', 3], [], e, s, gg)
            _(oB, aZ)
            var t1 = _mz(z, 'xk-login', ['bind:__l', 40, 'bind:success', 1, 'class', 2, 'data-event-opts', 3, 'data-ref', 4, 'vueId', 5], [], e, s, gg)
            _(oB, t1)
            xC.wxXCkey = 1
            xC.wxXCkey = 3
            _(r, oB)
            return r
        }
        e_[x[0]] = {
            f: m0,
            j: [],
            i: [],
            ti: [],
            ic: []
        }
        if (path && e_[path]) {
            return function(env, dd, global) {
                $gwxc = 0;
                var root = {
                    "tag": "wx-page"
                };
                root.children = [];
                g = "$gwx5_XC_0";
                var main = e_[path].f
                if (typeof global === "undefined") global = {};
                global.f = $gdc(f_[path], "", 1);
                try {
                    main(env, {}, root, global);
                    _tsd(root)
                } catch (err) {
                    console.log(err)
                };
                g = "";
                return root;
            }
        }
    }
}(__g.a, __g.b, __g.c, __g.d, __g.e, __g.f, __g.g, __g.h, __g.i, __g.j, __g.k, __g.l, __g.m, __g.n, __g.o, __g.p, __g.q, __g.r, __g.s, __g.t, __g.u, __g.v, __g.w, __g.x, __g.y, __g.z, __g.A, __g.B, __g.C, __g.D, __g.E, __g.F, __g.G, __g.H, __g.I, __g.J, __g.K, __g.L, __g.M, __g.N, __g.O, __g.P, __g.Q, __g.R, __g.S, __g.T, __g.U, __g.V, __g.W, __g.X, __g.Y, __g.Z, __g.aa);
if (__vd_version_info__.delayedGwx || false) $gwx5_XC_0();
if (__vd_version_info__.delayedGwx) __wxAppCode__['pages/qbank/chapter.wxml'] = [$gwx5_XC_0, './pages/qbank/chapter.wxml'];
else __wxAppCode__['pages/qbank/chapter.wxml'] = $gwx5_XC_0('./pages/qbank/chapter.wxml');;
__wxRoute = "pages/qbank/chapter";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/qbank/chapter.js";
define("pages/qbank/chapter.js", function(require, module, exports, window, document, frames, self, location, navigator, localStorage, history, Caches, screen, alert, confirm, prompt, XMLHttpRequest, WebSocket, Reporter, webkit, WeixinJSCore) {
    (global.webpackJsonp = global.webpackJsonp || []).push([
        ["pages/qbank/chapter"], {
            1135: function(t, e, n) {
                "use strict";
                var i = n("e179");
                n.n(i).a
            },
            "280e": function(t, e, n) {
                "use strict";
                var i = n("3e07");
                n.n(i).a
            },
            3287: function(t, e, n) {
                "use strict";
                (function(t) {
                    var i = n("47a9");
                    Object.defineProperty(e, "__esModule", {
                        value: !0
                    }), e.default = void 0;
                    var a = i(n("7eb4")),
                        o = i(n("ee10")),
                        s = i(n("af34")),
                        r = n("88d2"),
                        c = i(n("f462")),
                        u = n("62a4"),
                        h = n("7203"),
                        p = n("0938"),
                        d = n("2eff"),
                        f = {
                            components: {
                                xkActionSheet: function() {
                                    n.e("components/common/action-sheet").then(function() {
                                        return resolve(n("4fe3"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                modal: function() {
                                    n.e("components/common/modal-pro").then(function() {
                                        return resolve(n("76ee"))
                                    }.bind(null, n)).catch(n.oe)
                                }
                            },
                            data: function() {
                                return {
                                    navBarHeight: this.$statusBar + this.$customBar - 1,
                                    dataHasBeenLoaded: !1,
                                    scrollLeft: 0,
                                    qbankId: 0,
                                    chapterId: 0,
                                    chapters: [],
                                    type: "",
                                    tabs: [],
                                    activeTab: 0,
                                    statistics: {},
                                    isAuthorized: !0,
                                    parentFree: 0,
                                    wrongCount: 1,
                                    wrongRange: [
                                        ["100道/页", "200道/页", "300道/页", "400道/页", "500道/页"],
                                        [1]
                                    ],
                                    wrongChecked: !1,
                                    wrongPageSize: 100,
                                    wrongPage: 1,
                                    wrongTitle: "",
                                    exportType: "xlsx",
                                    iconList: []
                                }
                            },
                            computed: {
                                pageTitle: function() {
                                    if ("wrong" == this.type) {
                                        var t = this.iconList.find((function(t) {
                                            return "错题集" == t.name
                                        }));
                                        return t ? t.alias || t.name : "错题集"
                                    }
                                    if ("favorite" == this.type) {
                                        var e = this.iconList.find((function(t) {
                                            return "我的收藏" == t.name
                                        }));
                                        return e ? e.alias || e.name : "我的收藏"
                                    }
                                    if ("note" == this.type) {
                                        var n = this.iconList.find((function(t) {
                                            return "我的笔记" == t.name
                                        }));
                                        return n ? n.alias || n.name : "我的笔记"
                                    }
                                    if ("chapter" == this.type) {
                                        var i = this.iconList.find((function(t) {
                                            return "章节练习" == t.name
                                        }));
                                        return i ? i.alias || i.name : "章节练习"
                                    }
                                    return ""
                                },
                                allChapterTitle: function() {
                                    return "wrong" == this.type ? "全部错题" : "favorite" == this.type ? "全部收藏" : "note" == this.type ? "全部笔记" : ""
                                },
                                list: function() {
                                    if ("chapter" == this.type) {
                                        for (var t = this.chapters, e = 0; e < t.length; e++) "classify" == t[e].type && t[e].children && t[e].children.length && t.splice.apply(t, [e + 1, 0].concat((0, s.default)(t[e].children)));
                                        return t
                                    }
                                    var n = {
                                            id: 0,
                                            title: this.allChapterTitle
                                        },
                                        i = "";
                                    return "wrong" == this.type ? i = this.statistics.wrong_question || 0 : "favorite" == this.type ? i = this.statistics.favorite || 0 : "note" == this.type && (i = this.statistics.notes || 0), n[this.type + "Count"] = i, [n].concat(this.chapters)
                                },
                                currentQid: function() {
                                    return this.tabs.length ? this.tabs[this.activeTab].id : this.qbankId
                                },
                                wrongQAutoRemove: function() {
                                    return this.$store.state.adminConfig && this.$store.state.adminConfig.qbankSettings && this.$store.state.adminConfig.qbankSettings.wrongQAutoRemove || {}
                                },
                                exportStudentBool: function() {
                                    return this.$store.state.adminConfig && this.$store.state.adminConfig.qbankSettings && this.$store.state.adminConfig.qbankSettings.exportStudentBool || 0
                                },
                                wrongAllPage: function() {
                                    return Math.ceil(this.wrongCount / this.wrongPageSize)
                                },
                                qbankHomeStatType: function() {
                                    try {
                                        return this.$store.state.adminConfig.qbankSettings.qbankHomeStatType || 1
                                    } catch (t) {
                                        return 1
                                    }
                                }
                            },
                            filters: {
                                wrongChecked: function(t) {
                                    return t ? "打开" : "关闭"
                                }
                            },
                            onLoad: function(t) {
                                this.qbankId = t.qid, this.chapterId = t.cid, this.type = t.type, this.getTabs(), this.getChapter(), this.getPageTitle()
                            },
                            onShow: function() {
                                var t = this;
                                this.dataHasBeenLoaded && (this.$api.showLoading(), setTimeout((function() {
                                    t.getChapter()
                                }), 500))
                            },
                            methods: {
                                percent: function(t, e, n) {
                                    return (0, p.formartPercent)(t, e, n)
                                },
                                getPageTitle: function() {
                                    var t = this;
                                    (0, d._getAppConfig)(1).then((function(e) {
                                        if (0 === e.errno) {
                                            var n = (e.data.qbankLayout || []).find((function(t) {
                                                return "icon" == t.type
                                            }));
                                            t.iconList = n.data
                                        }
                                    }))
                                },
                                getChapter: function(t) {
                                    var e = this;
                                    this.$api.showLoading();
                                    var n = {
                                        qBankId: this.currentQid
                                    };
                                    "wrong" == this.type ? n.wrongQChapter = !0 : "favorite" == this.type ? n.favoriteChapter = !0 : "note" == this.type ? n.noteChapter = !0 : "chapter" == this.type && this.chapterId && (n.id = this.chapterId), this.$http({
                                        url: "/api/chapter",
                                        data: n,
                                        token: t || 1
                                    }).then((function(t) {
                                        e.dataHasBeenLoaded = !0, e.$api.hideLoading(), e.getStatistics(e.currentQid), 0 === t.errno ? "chapter" == e.type && e.chapterId && t.data[0] ? (e.parentFree = t.data[0].free, e.chapters = t.data[0].children) : e.chapters = t.data : 100143 === t.errno && ("chapter" == e.type ? e.getChapter(-1) : e.goLogin())
                                    }))
                                },
                                goLogin: function(t) {
                                    var e = this;
                                    (0, c.default)({
                                        component: !0,
                                        openComponent: function(t) {
                                            e.$refs.login.show()
                                        },
                                        success: function() {
                                            e.loginSuccess()
                                        }
                                    })
                                },
                                loginSuccess: function() {
                                    this.getTabs(), this.getChapter()
                                },
                                getTabs: function() {
                                    var t = this;
                                    this.qbankId && this.$http({
                                        url: "/api/q_bank",
                                        data: {
                                            id: this.qbankId
                                        }
                                    }).then(function() {
                                        var e = (0, o.default)(a.default.mark((function e(n) {
                                            var i, o;
                                            return a.default.wrap((function(e) {
                                                for (;;) switch (e.prev = e.next) {
                                                    case 0:
                                                        if (0 !== n.errno) {
                                                            e.next = 8;
                                                            break
                                                        }
                                                        if (t.isAuthorized = n.data.isAuthorized, "chapter" != t.type) {
                                                            e.next = 4;
                                                            break
                                                        }
                                                        return e.abrupt("return");
                                                    case 4:
                                                        i = n.data, o = [{
                                                            id: i.id,
                                                            title: "当前题库",
                                                            actionType: "common"
                                                        }], i.children && i.children.length && i.children.forEach((function(t) {
                                                            o.push({
                                                                id: t.id,
                                                                title: t.title,
                                                                actionType: t.actionType
                                                            })
                                                        })), t.tabs = o;
                                                    case 8:
                                                    case "end":
                                                        return e.stop()
                                                }
                                            }), e)
                                        })));
                                        return function(t) {
                                            return e.apply(this, arguments)
                                        }
                                    }())
                                },
                                changeTab: function(t) {
                                    this.activeTab = t, this.getChapter()
                                },
                                getStatistics: function(t) {
                                    var e = this;
                                    (0, r._getStatistics)(t).then((function(t) {
                                        0 === t.errno && (e.statistics = t.data)
                                    }))
                                },
                                selectItem: function(t) {
                                    if ("classify" != t.type && "wrong" != this.type && "favorite" != this.type)
                                        if ("chapter" != this.type)
                                            if ("note" != this.type) this.goQuestion(t);
                                            else {
                                                if (0 == t.noteCount) return;
                                                this.$api.openWin({
                                                    url: "/pages/qbank/notes",
                                                    params: {
                                                        qid: this.currentQid,
                                                        cid: t.id
                                                    }
                                                })
                                            } else this.goChapter(t)
                                },
                                goChapter: function(e) {
                                    var n = this;
                                    if (e)
                                        if (e.children && e.children.length) this.$api.openWin({
                                            url: "/pages/qbank/chapter",
                                            params: {
                                                qid: this.qbankId,
                                                cid: e.id,
                                                type: "chapter"
                                            }
                                        });
                                        else if ((0, u.getToken)())
                                        if (e.free || this.parentFree || this.isAuthorized) {
                                            if (0 == e.questionCount) return void setTimeout((function() {
                                                n.$api.toast("题目正在准备中")
                                            }), 200);
                                            this.goQuestion(e)
                                        } else this.$api.confirm({
                                            title: "请先开通题库",
                                            content: "该章节需要开通题库后才可以学习哦",
                                            confirmText: "开通题库",
                                            success: function(e) {
                                                e.confirm && (n.$store.commit("setData", {
                                                    type: "replace",
                                                    key: "tipBuyQbank",
                                                    data: !0
                                                }), t.reLaunch({
                                                    url: "/pages/index?tabType=qbank&qbankId=" + n.qbankId
                                                }))
                                            }
                                        });
                                    else this.goLogin()
                                },
                                doFavorite: function(t) {
                                    if ("favorite" != this.type || "examPoint" != this.tabs[this.activeTab].actionType) this.goQuestion(t);
                                    else {
                                        if (0 == t.favoriteCount) return;
                                        this.$api.openWin({
                                            url: "/pages/knowledge/index",
                                            params: {
                                                qid: this.currentQid,
                                                cid: t.id,
                                                isfavorite: 1
                                            }
                                        })
                                    }
                                },
                                viewWrong: function(t) {
                                    t.subsubType = "viewWrong", this.goQuestion(t)
                                },
                                doWrong: function(t) {
                                    t.subsubType = "doWrong", this.goQuestion(t)
                                },
                                goQuestion: function(e) {
                                    var n = this;
                                    if (0 != e[this.type + "Count"]) {
                                        var i = {
                                            type: "practice",
                                            subType: this.type,
                                            qid: this.currentQid,
                                            pageSize: e[this.type + "Count"],
                                            title: this.pageTitle
                                        };
                                        if (e.id && (i.cid = e.id), "wrong" == this.type) {
                                            i.subsubType = e.subsubType, "doWrong" == i.subsubType ? i.title = "错题重练" : "viewWrong" == i.subsubType && (i.title = "查看错题");
                                            var a = t.getStorageSync("wrongQuestionPageSize") || 100,
                                                o = e.wrongCount;
                                            if (o > a) {
                                                i.pageSize = a;
                                                for (var s = Math.ceil(o / a), r = [], c = 0; c < s; c++) r.push(a * c + 1 + "-" + (a * (c + 1) > o ? o : a * (c + 1)));
                                                this.$refs.actionSheet.open({
                                                    title: "题目过多，选择下要练习的范围吧！",
                                                    itemList: r,
                                                    itemSize: "13px",
                                                    success: function(t) {
                                                        var e = t.tapIndex + 1;
                                                        console.log("用户选择了第" + e + "页"), console.log("前往答题页"), i = Object.assign(i, {
                                                            page: e
                                                        }), n.$api.openWin({
                                                            url: "/pages/question/index",
                                                            params: i
                                                        })
                                                    }
                                                })
                                            } else this.$api.openWin({
                                                url: "/pages/question/index",
                                                params: i
                                            })
                                        } else this.$api.openWin({
                                            url: "/pages/question/index",
                                            params: i
                                        })
                                    }
                                },
                                showMoreAction: function(t, e) {
                                    var n = this;
                                    if ("fav" == e) {
                                        this.chapterId = t.id;
                                        var i;
                                        i = 0 == this.chapterId ? ["清空收藏题目"] : ["清空本章收藏题目"], this.$refs.actionSheet.open({
                                            itemList: i,
                                            success: function(e) {
                                                if (0 != t[n.type + "Count"] && (n.$api.showLoading(), 0 == e.tapIndex)) {
                                                    console.log(n.currentQid);
                                                    var i = {
                                                        type: 1
                                                    };
                                                    0 == n.chapterId ? i.qBankId = n.currentQid : i.chapterId = n.chapterId, n.$http({
                                                        method: "DELETE",
                                                        url: "/api/favorite",
                                                        data: i,
                                                        token: 1
                                                    }).then((function(t) {
                                                        n.$api.hideLoading(), 0 === t.errno && (n.$api.toast("该章节收藏题目已清空"), n.$api.sendEvent({
                                                            name: "updateQbank"
                                                        }), setTimeout((function() {
                                                            n.getChapter()
                                                        }), 300))
                                                    }))
                                                }
                                            }
                                        })
                                    } else {
                                        this.chapterId = t.id;
                                        var a = [];
                                        0 == this.chapterId ? this.exportStudentBool && (a = ["导出错题"]) : a = this.exportStudentBool ? ["导出本章错题", "清空本章错题"] : ["清空本章错题"], this.$refs.actionSheet.open({
                                            itemList: a,
                                            success: function(e) {
                                                if (0 == e.tapIndex)
                                                    if (n.exportStudentBool) {
                                                        console.log("导出"), n.wrongCount = t.wrongCount, n.wrongTitle = t.title;
                                                        for (var i = [], a = 1; a <= Math.ceil(n.wrongCount / n.wrongPageSize); a++) i.push(a);
                                                        n.wrongRange[1] = i, n.$refs.wrongModal.open({
                                                            type: "other"
                                                        })
                                                    } else o();
                                                else 1 == e.tapIndex && o()
                                            }
                                        });
                                        var o = function() {
                                            console.log("清空"), n.$api.showLoading(), n.$http({
                                                method: "DELETE",
                                                url: "/api/wrong_question",
                                                data: {
                                                    chapterId: n.chapterId
                                                },
                                                token: 1
                                            }).then((function(t) {
                                                n.$api.hideLoading(), 0 === t.errno && (n.$api.toast("该章节错题已清空"), n.$api.sendEvent({
                                                    name: "updateQbank"
                                                }), n.getChapter())
                                            }))
                                        }
                                    }
                                },
                                wrongColumnchange: function(t) {
                                    if (0 == t.detail.column) {
                                        var e = [];
                                        this.wrongPageSize = 100 * (t.detail.value + 1);
                                        for (var n = 1; n <= Math.ceil(this.wrongCount / this.wrongPageSize); n++) e.push(n);
                                        this.wrongRange[1] = e
                                    }
                                },
                                wrongChange: function(t) {
                                    this.wrongPageSize = 100 * (t.detail.value[0] + 1), this.wrongPage = t.detail.value[1] + 1
                                },
                                radioChange: function(t) {
                                    this.exportType = t.detail.value
                                },
                                changeSwitch: function(t) {
                                    this.wrongChecked = t.detail.value
                                },
                                exportWrongQuestion: function() {
                                    var e = this,
                                        n = {
                                            qBankId: this.qbankId,
                                            chapterId: this.chapterId,
                                            page: this.wrongPage,
                                            pageSize: this.wrongPageSize,
                                            exportType: this.exportType,
                                            app: !0,
                                            token: (0, u.encodeToken)()
                                        };
                                    this.wrongChecked && (n.correctRate = this.wrongChecked);
                                    var i = "".concat(this.HOST, "/api/v1/question/wrong_export_question?").concat((0, h.parsePageParams)(n));
                                    i = i.replace("http://", "https://"), t.downloadFile({
                                        url: i,
                                        success: function(n) {
                                            if (200 === n.statusCode) {
                                                var a = function() {
                                                    t.setClipboardData({
                                                        data: i,
                                                        success: function() {
                                                            e.$api.showModal({
                                                                title: "导出成功",
                                                                content: "文件路径已复制，请打开浏览器粘贴下载",
                                                                showCancel: !1,
                                                                success: function() {}
                                                            })
                                                        }
                                                    })
                                                };
                                                "windows" == t.getSystemInfoSync().platform ? a() : t.openDocument({
                                                    filePath: n.tempFilePath,
                                                    showMenu: !0,
                                                    success: function(t) {
                                                        console.log("打开文档成功")
                                                    },
                                                    fail: function() {
                                                        a()
                                                    }
                                                })
                                            } else t.showToast({
                                                title: "导出失败，请稍后重试",
                                                icon: "none"
                                            })
                                        },
                                        fail: function(e) {
                                            t.showToast({
                                                title: "导出失败，请稍后重试",
                                                icon: "none"
                                            })
                                        }
                                    })
                                },
                                deleteWrongQuestions: function() {
                                    var e = this;
                                    this.$api.confirm({
                                        title: "温馨提示",
                                        content: "是否要清空当前题库下的所有错题？",
                                        success: function(n) {
                                            n.confirm && (e.$api.showLoading(), e.$http({
                                                method: "DELETE",
                                                url: "/api/wrong_question",
                                                data: {
                                                    qBankId: e.qbankId
                                                },
                                                token: 1
                                            }).then((function(n) {
                                                e.$api.showLoading(), 0 === n.errno && (e.$api.toast("已清空，请稍候"), e.$api.sendEvent({
                                                    name: "updateQbank"
                                                }), setTimeout((function() {
                                                    t.navigateBack()
                                                }), 1e3))
                                            })))
                                        }
                                    })
                                }
                            }
                        };
                    e.default = f
                }).call(this, n("df3c").default)
            },
            "3e07": function(t, e, n) {},
            a6f0: function(t, e, n) {
                "use strict";
                n.d(e, "b", (function() {
                    return i
                })), n.d(e, "c", (function() {
                    return a
                })), n.d(e, "a", (function() {}));
                var i = function() {
                        var t = this,
                            e = (t.$createElement, t._self._c, t.dataHasBeenLoaded ? t.tabs.length : null),
                            n = t.dataHasBeenLoaded ? t.list.length : null,
                            i = t.dataHasBeenLoaded && n ? t.__map(t.list, (function(e, n) {
                                return {
                                    $orig: t.__get_orig(e),
                                    m0: "classify" != e.type && "chapter" == t.type && 1 != t.qbankHomeStatType ? t.percent(e.rightCount, e.didCount, 2) : null,
                                    m1: "classify" != e.type && "chapter" == t.type && 1 == t.qbankHomeStatType ? t.percent(e.didCount, e.questionCount, 2) : null
                                }
                            })) : null;
                        t.$mp.data = Object.assign({}, {
                            $root: {
                                g0: e,
                                g1: n,
                                l0: i
                            }
                        })
                    },
                    a = []
            },
            b271: function(t, e, n) {
                "use strict";
                n.r(e);
                var i = n("a6f0"),
                    a = n("c822");
                for (var o in a)["default"].indexOf(o) < 0 && function(t) {
                    n.d(e, t, (function() {
                        return a[t]
                    }))
                }(o);
                n("1135"), n("280e");
                var s = n("828b"),
                    r = Object(s.a)(a.default, i.b, i.c, !1, null, "f15109f2", null, !1, i.a, void 0);
                e.default = r.exports
            },
            c822: function(t, e, n) {
                "use strict";
                n.r(e);
                var i = n("3287"),
                    a = n.n(i);
                for (var o in i)["default"].indexOf(o) < 0 && function(t) {
                    n.d(e, t, (function() {
                        return i[t]
                    }))
                }(o);
                e.default = a.a
            },
            d9c4: function(t, e, n) {
                "use strict";
                (function(t, e) {
                    var i = n("47a9");
                    n("8f74"), i(n("3240"));
                    var a = i(n("b271"));
                    t.__webpack_require_UNI_MP_PLUGIN__ = n, e(a.default)
                }).call(this, n("3223").default, n("df3c").createPage)
            },
            e179: function(t, e, n) {}
        },
        [
            ["d9c4", "common/runtime", "common/vendor"]
        ]
    ]);
}, {
    isPage: true,
    isComponent: true,
    currentFile: 'pages/qbank/chapter.js'
});
require("pages/qbank/chapter.js");
$gwx5_XC_1 = function(_, _v, _n, _p, _s, _wp, _wl, $gwn, $gwl, $gwh, wh, $gstack, $gwrt, gra, grb, TestTest, wfor, _ca, _da, _r, _rz, _o, _oz, _1, _1z, _2, _2z, _m, _mz, nv_getDate, nv_getRegExp, nv_console, nv_parseInt, nv_parseFloat, nv_isNaN, nv_isFinite, nv_decodeURI, nv_decodeURIComponent, nv_encodeURI, nv_encodeURIComponent, $gdc, nv_JSON, _af, _gv, _ai, _grp, _gd, _gapi, $ixc, _ic, _w, _ev, _tsd) {
    return function(path, global) {
        if (typeof global === 'undefined') {
            if (typeof __GWX_GLOBAL__ === 'undefined') global = {};
            else global = __GWX_GLOBAL__;
        }
        if (typeof __WXML_GLOBAL__ === 'undefined') {
            __WXML_GLOBAL__ = {};
        }
        __WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
        var e_ = {}
        if (typeof(global.entrys) === 'undefined') global.entrys = {};
        e_ = global.entrys;
        var d_ = {}
        if (typeof(global.defines) === 'undefined') global.defines = {};
        d_ = global.defines;
        var f_ = {}
        if (typeof(global.modules) === 'undefined') global.modules = {};
        f_ = global.modules || {};
        var p_ = {}
        __WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
        __WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
        __WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
        var z = __WXML_GLOBAL__.ops_set.$gwx5_XC_1 || [];

        function gz$gwx5_XC_1_1() {
            if (__WXML_GLOBAL__.ops_cached.$gwx5_XC_1_1) return __WXML_GLOBAL__.ops_cached.$gwx5_XC_1_1
            __WXML_GLOBAL__.ops_cached.$gwx5_XC_1_1 = [];
            (function(z) {
                var a = 11;

                function Z(ops) {
                    z.push(ops)
                }
                Z([3, 'data-v-0f067932'])
                Z([3, '__l'])
                Z(z[0])
                Z([
                    [7],
                    [3, 'pageTitle']
                ])
                Z([3, '0c941d55-1'])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'qbank']
                    ],
                    [3, 'id']
                ])
                Z([3, 'page-content data-v-0f067932'])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'g0']
                ])
                Z([
                    [7],
                    [3, 'adminConfig']
                ])
                Z(z[1])
                Z([3, '__e'])
                Z([
                    [7],
                    [3, 'chapters']
                ])
                Z(z[0])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^select']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'selectChapter']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([
                    [7],
                    [3, 'examPointChapters']
                ])
                Z([
                    [7],
                    [3, 'chapterHasBeenLoaded']
                ])
                Z([
                    [7],
                    [3, 'qbank']
                ])
                Z([
                    [9],
                    [
                        [8], 'fullScreen', [1, true]
                    ],
                    [
                        [8], 'radius', [1, 10]
                    ]
                ])
                Z([3, '0c941d55-2'])
                Z([
                    [2, '=='],
                    [
                        [6],
                        [
                            [7],
                            [3, 'qbank']
                        ],
                        [3, 'actionType']
                    ],
                    [1, 'paper']
                ])
                Z([
                    [7],
                    [3, 'paperHasBeenLoaded']
                ])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'g1']
                ])
                Z([3, 'index'])
                Z([3, 'item'])
                Z([
                    [7],
                    [3, 'paperList']
                ])
                Z(z[22])
                Z(z[1])
                Z(z[0])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'qbank']
                    ],
                    [3, 'isAuthorized']
                ])
                Z([
                    [7],
                    [3, 'item']
                ])
                Z([
                    [2, '+'],
                    [1, '0c941d55-3-'],
                    [
                        [7],
                        [3, 'index']
                    ]
                ])
                Z(z[1])
                Z(z[0])
                Z([3, '暂无相关试卷'])
                Z([3, '30%'])
                Z([3, 'order'])
                Z([3, '0c941d55-4'])
                Z(z[1])
                Z(z[0])
                Z([
                    [2, '!'],
                    [
                        [7],
                        [3, 'loadMoreState']
                    ]
                ])
                Z([
                    [7],
                    [3, 'loadMoreState']
                ])
                Z([3, '0c941d55-5'])
                Z(z[5])
                Z([
                    [2, '!'],
                    [
                        [6],
                        [
                            [7],
                            [3, 'qbank']
                        ],
                        [3, 'isAuthorized']
                    ]
                ])
                Z(z[1])
                Z([3, 'data-v-0f067932 vue-ref'])
                Z([3, 'paperModal'])
                Z([3, '0c941d55-6'])
                Z(z[1])
                Z(z[45])
                Z([3, 'actionSheet'])
                Z([3, '0c941d55-7'])
                Z(z[1])
                Z(z[10])
                Z(z[45])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^success']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'loginSuccess']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'login'])
                Z([3, '0c941d55-8'])
                Z([
                    [7],
                    [3, 'allowPayType']
                ])
                Z([
                    [7],
                    [3, 'assistantQrcode']
                ])
                Z(z[1])
                Z(z[10])
                Z(z[45])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^success']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'paymentSuccess']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'payment'])
                Z(z[5])
                Z([3, 'questions'])
                Z([3, '0c941d55-9'])
                Z(z[1])
                Z(z[45])
                Z([3, 'openModal'])
                Z([3, '0c941d55-10'])
            })(__WXML_GLOBAL__.ops_cached.$gwx5_XC_1_1);
            return __WXML_GLOBAL__.ops_cached.$gwx5_XC_1_1
        }
        __WXML_GLOBAL__.ops_set.$gwx5_XC_1 = z;
        __WXML_GLOBAL__.ops_init.$gwx5_XC_1 = true;
        var x = ['./pages/qbank/child.wxml'];
        d_[x[0]] = {}
        var m0 = function(e, s, r, gg) {
            var z = gz$gwx5_XC_1_1()
            var b3 = _n('view')
            _rz(z, b3, 'class', 0, e, s, gg)
            var o6 = _mz(z, 'nav-bar', ['bind:__l', 1, 'class', 1, 'title', 2, 'vueId', 3], [], e, s, gg)
            _(b3, o6)
            var o4 = _v()
            _(b3, o4)
            if (_oz(z, 5, e, s, gg)) {
                o4.wxVkey = 1
                var f7 = _n('view')
                _rz(z, f7, 'class', 6, e, s, gg)
                var c8 = _v()
                _(f7, c8)
                if (_oz(z, 7, e, s, gg)) {
                    c8.wxVkey = 1
                    var h9 = _mz(z, 'xk-qbank-chapter', ['adminConfig', 8, 'bind:__l', 1, 'bind:select', 2, 'chapters', 3, 'class', 4, 'data-event-opts', 5, 'examPointChapters', 6, 'loaded', 7, 'qbank', 8, 'styles', 9, 'vueId', 10], [], e, s, gg)
                    _(c8, h9)
                } else {
                    c8.wxVkey = 2
                    var o0 = _v()
                    _(c8, o0)
                    if (_oz(z, 19, e, s, gg)) {
                        o0.wxVkey = 1
                        var cAB = _v()
                        _(o0, cAB)
                        if (_oz(z, 20, e, s, gg)) {
                            cAB.wxVkey = 1
                            var oBB = _v()
                            _(cAB, oBB)
                            if (_oz(z, 21, e, s, gg)) {
                                oBB.wxVkey = 1
                                var lCB = _v()
                                _(oBB, lCB)
                                var aDB = function(eFB, tEB, bGB, gg) {
                                    var xIB = _mz(z, 'paper-item', ['bind:__l', 26, 'class', 1, 'isAuthorized', 2, 'item', 3, 'vueId', 4], [], eFB, tEB, gg)
                                    _(bGB, xIB)
                                    return bGB
                                }
                                lCB.wxXCkey = 4
                                _2z(z, 24, aDB, e, s, gg, lCB, 'item', 'index', 'index')
                            } else {
                                oBB.wxVkey = 2
                                var oJB = _mz(z, 'xk-empty', ['bind:__l', 31, 'class', 1, 'text', 2, 'top', 3, 'type', 4, 'vueId', 5], [], e, s, gg)
                                _(oBB, oJB)
                            }
                            oBB.wxXCkey = 1
                            oBB.wxXCkey = 3
                            oBB.wxXCkey = 3
                        }
                        var fKB = _mz(z, 'load-more', ['bind:__l', 37, 'class', 1, 'data-custom-hidden', 2, 'status', 3, 'vueId', 4], [], e, s, gg)
                        _(o0, fKB)
                        cAB.wxXCkey = 1
                        cAB.wxXCkey = 3
                    }
                    o0.wxXCkey = 1
                    o0.wxXCkey = 3
                }
                c8.wxXCkey = 1
                c8.wxXCkey = 3
                c8.wxXCkey = 3
                _(o4, f7)
            }
            var x5 = _v()
            _(b3, x5)
            if (_oz(z, 42, e, s, gg)) {
                x5.wxVkey = 1
                var cLB = _v()
                _(x5, cLB)
                if (_oz(z, 43, e, s, gg)) {
                    cLB.wxVkey = 1
                }
                cLB.wxXCkey = 1
            }
            var hMB = _mz(z, 'paper-modal', ['bind:__l', 44, 'class', 1, 'data-ref', 2, 'vueId', 3], [], e, s, gg)
            _(b3, hMB)
            var oNB = _mz(z, 'xk-action-sheet', ['bind:__l', 48, 'class', 1, 'data-ref', 2, 'vueId', 3], [], e, s, gg)
            _(b3, oNB)
            var cOB = _mz(z, 'xk-login', ['bind:__l', 52, 'bind:success', 1, 'class', 2, 'data-event-opts', 3, 'data-ref', 4, 'vueId', 5], [], e, s, gg)
            _(b3, cOB)
            var oPB = _mz(z, 'payment', ['allowPayType', 58, 'assistantQrcode', 1, 'bind:__l', 2, 'bind:success', 3, 'class', 4, 'data-event-opts', 5, 'data-ref', 6, 'productId', 7, 'productType', 8, 'vueId', 9], [], e, s, gg)
            _(b3, oPB)
            var lQB = _mz(z, 'open-modal', ['bind:__l', 68, 'class', 1, 'data-ref', 2, 'vueId', 3], [], e, s, gg)
            _(b3, lQB)
            o4.wxXCkey = 1
            o4.wxXCkey = 3
            x5.wxXCkey = 1
            _(r, b3)
            return r
        }
        e_[x[0]] = {
            f: m0,
            j: [],
            i: [],
            ti: [],
            ic: []
        }
        if (path && e_[path]) {
            return function(env, dd, global) {
                $gwxc = 0;
                var root = {
                    "tag": "wx-page"
                };
                root.children = [];
                g = "$gwx5_XC_1";
                var main = e_[path].f
                if (typeof global === "undefined") global = {};
                global.f = $gdc(f_[path], "", 1);
                try {
                    main(env, {}, root, global);
                    _tsd(root)
                } catch (err) {
                    console.log(err)
                };
                g = "";
                return root;
            }
        }
    }
}(__g.a, __g.b, __g.c, __g.d, __g.e, __g.f, __g.g, __g.h, __g.i, __g.j, __g.k, __g.l, __g.m, __g.n, __g.o, __g.p, __g.q, __g.r, __g.s, __g.t, __g.u, __g.v, __g.w, __g.x, __g.y, __g.z, __g.A, __g.B, __g.C, __g.D, __g.E, __g.F, __g.G, __g.H, __g.I, __g.J, __g.K, __g.L, __g.M, __g.N, __g.O, __g.P, __g.Q, __g.R, __g.S, __g.T, __g.U, __g.V, __g.W, __g.X, __g.Y, __g.Z, __g.aa);
if (__vd_version_info__.delayedGwx || false) $gwx5_XC_1();
if (__vd_version_info__.delayedGwx) __wxAppCode__['pages/qbank/child.wxml'] = [$gwx5_XC_1, './pages/qbank/child.wxml'];
else __wxAppCode__['pages/qbank/child.wxml'] = $gwx5_XC_1('./pages/qbank/child.wxml');;
__wxRoute = "pages/qbank/child";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/qbank/child.js";
define("pages/qbank/child.js", function(require, module, exports, window, document, frames, self, location, navigator, localStorage, history, Caches, screen, alert, confirm, prompt, XMLHttpRequest, WebSocket, Reporter, webkit, WeixinJSCore) {
    (global.webpackJsonp = global.webpackJsonp || []).push([
        ["pages/qbank/child"], {
            "199b": function(e, t, n) {},
            "31ef": function(e, t, n) {
                "use strict";
                n.d(t, "b", (function() {
                    return i
                })), n.d(t, "c", (function() {
                    return o
                })), n.d(t, "a", (function() {
                    return a
                }));
                var a = {
                        payment: function() {
                            return Promise.all([n.e("common/vendor"), n.e("components/payment/payment")]).then(n.bind(null, "5d4b"))
                        }
                    },
                    i = function() {
                        var e = this,
                            t = (e.$createElement, e._self._c, e.qbank.id ? ("practise" == e.qbank.actionType || "examPoint" == e.qbank.actionType) && (e.chapters.length || e.examPointChapters.length) : null),
                            n = e.qbank.id && !t && "paper" == e.qbank.actionType && e.paperHasBeenLoaded ? e.paperList.length : null;
                        e.$mp.data = Object.assign({}, {
                            $root: {
                                g0: t,
                                g1: n
                            }
                        })
                    },
                    o = []
            },
            "5b4a": function(e, t, n) {
                "use strict";
                n.r(t);
                var a = n("31ef"),
                    i = n("7b23");
                for (var o in i)["default"].indexOf(o) < 0 && function(e) {
                    n.d(t, e, (function() {
                        return i[e]
                    }))
                }(o);
                n("e94d");
                var r = n("828b"),
                    s = Object(r.a)(i.default, a.b, a.c, !1, null, "0f067932", null, !1, a.a, void 0);
                t.default = s.exports
            },
            6992: function(e, t, n) {
                "use strict";
                (function(e) {
                    var a = n("47a9");
                    Object.defineProperty(t, "__esModule", {
                        value: !0
                    }), t.default = void 0;
                    var i = a(n("7eb4")),
                        o = a(n("7ca3")),
                        r = a(n("ee10")),
                        s = n("88d2"),
                        c = (n("3c3c"), n("2eff")),
                        u = n("62a4"),
                        p = a(n("f462"));

                    function d(e, t) {
                        var n = Object.keys(e);
                        if (Object.getOwnPropertySymbols) {
                            var a = Object.getOwnPropertySymbols(e);
                            t && (a = a.filter((function(t) {
                                return Object.getOwnPropertyDescriptor(e, t).enumerable
                            }))), n.push.apply(n, a)
                        }
                        return n
                    }

                    function l(e) {
                        for (var t = 1; t < arguments.length; t++) {
                            var n = null != arguments[t] ? arguments[t] : {};
                            t % 2 ? d(Object(n), !0).forEach((function(t) {
                                (0, o.default)(e, t, n[t])
                            })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : d(Object(n)).forEach((function(t) {
                                Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
                            }))
                        }
                        return e
                    }
                    var f = {
                        components: {
                            xkQbankChapter: function() {
                                Promise.all([n.e("common/vendor"), n.e("components/qbank/chapter")]).then(function() {
                                    return resolve(n("d45e"))
                                }.bind(null, n)).catch(n.oe)
                            },
                            xkQbankPaper: function() {
                                n.e("components/qbank/paper").then(function() {
                                    return resolve(n("8855"))
                                }.bind(null, n)).catch(n.oe)
                            },
                            payment: function() {
                                Promise.all([n.e("common/vendor"), n.e("components/payment/payment")]).then(function() {
                                    return resolve(n("5d4b"))
                                }.bind(null, n)).catch(n.oe)
                            },
                            xkActionSheet: function() {
                                n.e("components/common/action-sheet").then(function() {
                                    return resolve(n("4fe3"))
                                }.bind(null, n)).catch(n.oe)
                            },
                            loadMore: function() {
                                n.e("components/common/load-more").then(function() {
                                    return resolve(n("3dc2"))
                                }.bind(null, n)).catch(n.oe)
                            },
                            paperItem: function() {
                                Promise.all([n.e("common/vendor"), n.e("components/qbank/paper-item")]).then(function() {
                                    return resolve(n("1557"))
                                }.bind(null, n)).catch(n.oe)
                            },
                            paperModal: function() {
                                Promise.all([n.e("common/vendor"), n.e("components/qbank/paper-modal")]).then(function() {
                                    return resolve(n("83b0"))
                                }.bind(null, n)).catch(n.oe)
                            },
                            openModal: function() {
                                n.e("components/common/modal-open").then(function() {
                                    return resolve(n("8ce3"))
                                }.bind(null, n)).catch(n.oe)
                            }
                        },
                        data: function() {
                            return {
                                qbankId: 0,
                                qbank: {},
                                chapters: [],
                                examPointChapters: [],
                                chapterHasBeenLoaded: !1,
                                paperHasBeenLoaded: !1,
                                adminConfig: {},
                                allowPayType: [],
                                paperList: [],
                                currentPage: 1,
                                loadMoreFlag: !0,
                                loadMoreState: 0
                            }
                        },
                        onLoad: function(t) {
                            this.qbankId = t.id || 0, this.agentId = t.agentId || 0, this.qbankId || e.reLaunch({
                                url: "/pages/index?tabType=qbank"
                            }), this.getQbank()
                        },
                        onReachBottom: function() {
                            this.loadMoreFlag && (this.currentPage++, this.getPaperList())
                        },
                        computed: {
                            pageTitle: function() {
                                return this.qbank.id ? this.qbank.title : ""
                            },
                            hiddenPrice: function() {
                                return !this.$api.formatThePrice(1, 3)
                            },
                            assistantQrcode: function() {
                                return this.qbank.optional && this.qbank.optional.assistantQrcode ? this.qbank.optional.assistantQrcode : ""
                            }
                        },
                        methods: {
                            addEventListener: function(e, t) {
                                var n = this;
                                "loginSuccess" == e ? this.loginSuccess() : "updateQbank" == e ? setTimeout((function() {
                                    n.getQbank()
                                }), 200) : "paymented" == e && "questions" == t.type && this.paymentSuccess()
                            },
                            formatImageUrl: function(e, t, n, a) {
                                return this.$api.formatImageUrl(e, t, n, a)
                            },
                            getQbank: function() {
                                var t = this;
                                this.token = (0, u.getToken)(), this.qbankId && this.$http({
                                    url: "/api/q_bank",
                                    data: {
                                        id: this.qbankId
                                    }
                                }).then(function() {
                                    var n = (0, r.default)(i.default.mark((function n(a) {
                                        var o, r, s, u;
                                        return i.default.wrap((function(n) {
                                            for (;;) switch (n.prev = n.next) {
                                                case 0:
                                                    if (0 !== a.errno) {
                                                        n.next = 12;
                                                        break
                                                    }
                                                    return t.qbank = a.data, t.qbankId = t.qbank.id, t.isHave = a.data.isAuthorized, "examPoint" == t.qbank.actionType && (t.qbank.examPointQbank = JSON.parse(JSON.stringify(t.qbank))), "practise" == t.qbank.actionType || "examPoint" == t.qbank.actionType ? t.getQbankChapter() : "paper" == t.qbank.actionType && t.getPaperList(), n.next = 8, (0, c._getAdminConfig)();
                                                case 8:
                                                    t.adminConfig = n.sent, t.isHave || (o = a.data, r = [], r = o.optional ? o.optional.allowPayType || [] : ["pay", "key", "password"], t.adminConfig.payThreeUrl && (r = ["pay"]), "ios" == e.getSystemInfoSync().platform && ((s = r.findIndex((function(e) {
                                                        return "pay" == e
                                                    }))) > -1 && r.splice(s, 1)), t.adminConfig.paySwitch || (u = r.findIndex((function(e) {
                                                        return "pay" == e
                                                    }))) > -1 && r.splice(u, 1), t.allowPayType = r), n.next = 13;
                                                    break;
                                                case 12:
                                                    100143 === a.errno && (t.token = "", t.goLogin());
                                                case 13:
                                                case "end":
                                                    return n.stop()
                                            }
                                        }), n)
                                    })));
                                    return function(e) {
                                        return n.apply(this, arguments)
                                    }
                                }())
                            },
                            getParentQbank: function() {
                                this.$http({
                                    url: "/api/q_bank",
                                    data: {
                                        id: this.qbank.pId
                                    }
                                }).then(function() {
                                    var e = (0, r.default)(i.default.mark((function e(t) {
                                        return i.default.wrap((function(e) {
                                            for (;;) switch (e.prev = e.next) {
                                                case 0:
                                                    t.errno;
                                                case 1:
                                                case "end":
                                                    return e.stop()
                                            }
                                        }), e)
                                    })));
                                    return function(t) {
                                        return e.apply(this, arguments)
                                    }
                                }())
                            },
                            getQbankChapter: function() {
                                var e = this;
                                return (0, r.default)(i.default.mark((function t() {
                                    var n;
                                    return i.default.wrap((function(t) {
                                        for (;;) switch (t.prev = t.next) {
                                            case 0:
                                                if (e.qbankId) {
                                                    t.next = 2;
                                                    break
                                                }
                                                return t.abrupt("return");
                                            case 2:
                                                return t.next = 4, (0, s._getQbankChapter)(e.qbankId);
                                            case 4:
                                                n = t.sent, e.chapterHasBeenLoaded = !0, 0 === n.errno && (e.chapters = n.data, "examPoint" == e.qbank.actionType && (e.examPointChapters = n.data));
                                            case 7:
                                            case "end":
                                                return t.stop()
                                        }
                                    }), t)
                                })))()
                            },
                            getPaperList: function() {
                                var e = this;
                                "paper" == this.qbank.actionType && (this.loadMoreState = 2, this.$http({
                                    url: "/api/paper/search_by_qbank",
                                    data: {
                                        qBankId: this.qbank.id,
                                        page: this.currentPage || 1,
                                        pageSize: 20
                                    }
                                }).then((function(t) {
                                    0 === t.errno && (e.paperHasBeenLoaded = !0, 1 == e.currentPage ? e.paperList = t.data : e.paperList = e.paperList.concat(t.data), t.currentPage >= t.totalPages ? (e.loadMoreFlag = !1, t.count <= 5 ? e.loadMoreState = 0 : e.loadMoreState = 3) : (e.loadMoreFlag = !0, e.loadMoreState = 1))
                                })))
                            },
                            selectPaper: function() {
                                var e = arguments,
                                    t = this;
                                return (0, r.default)(i.default.mark((function n() {
                                    var a;
                                    return i.default.wrap((function(n) {
                                        for (;;) switch (n.prev = n.next) {
                                            case 0:
                                                return a = e.length > 0 && void 0 !== e[0] ? e[0] : {}, n.next = 3, t.interceptLogin();
                                            case 3:
                                                return n.next = 5, t.interceptPhone();
                                            case 5:
                                                return n.next = 7, t.interceptPay();
                                            case 7:
                                                t.$refs.paperModal.open({
                                                    paper: a.paper,
                                                    isChildQbank: 1
                                                });
                                            case 8:
                                            case "end":
                                                return n.stop()
                                        }
                                    }), n)
                                })))()
                            },
                            goLogin: function() {
                                var e = this;
                                1154 != this.$store.state.launchOptions.scene ? (0, p.default)({
                                    component: !0,
                                    openComponent: function(t) {
                                        t.reload ? e.getQbank() : e.$refs.login.show()
                                    },
                                    success: function() {
                                        e.loginSuccess()
                                    }
                                }) : this.$api.toast("请前往小程序使用完整服务")
                            },
                            loginSuccess: function() {
                                this.getQbank()
                            },
                            paymentSuccess: function() {
                                this.getQbank()
                            },
                            interceptLogin: function() {
                                var e = this;
                                return new Promise((function(t, n) {
                                    e.token ? t() : e.goLogin()
                                }))
                            },
                            interceptPhone: function() {
                                var t = this;
                                return new Promise((function(n, a) {
                                    var i = e.getStorageSync("userInfo") || {};
                                    t.adminConfig && t.adminConfig.needPhone && !i.phone ? t.$refs.openModal.open({
                                        content: t.adminConfig.needPhoneTip,
                                        success: function(n) {
                                            n.confirm && (n.errMsg && n.errMsg.indexOf("getPhoneNumber") > -1 ? t.bindWechatPhone(n) : e.navigateTo({
                                                url: "/pages/user/phone"
                                            }))
                                        }
                                    }) : n()
                                }))
                            },
                            interceptPay: function(e) {
                                var t = this;
                                return new Promise((function(n, a) {
                                    t.qbank.isAuthorized || e ? n() : t.joinQbank()
                                }))
                            },
                            joinQbank: function(e) {
                                if (0 != this.qbank.price) {
                                    var t = this.allowPayType;
                                    0 == t.length ? this.$api.toast(this.isMpios ? "暂不支持加入" : "暂不支持购买") : 1 == t.length ? "password" == t[0] ? this.$refs.payment.joinProductByPassword() : "key" == t[0] ? this.$refs.payment.joinProductByKey() : "assistant" == t[0] && this.assistantQrcode ? this.$refs.payment.joinProductByAssistant() : e ? this.goBuy() : this.tipToBuy() : t.includes("pay") ? e ? this.goBuy() : this.tipToBuy() : this.$refs.payment.open()
                                } else this.tipToJoin()
                            },
                            tipToBuy: function() {
                                var e = this;
                                this.$api.confirm({
                                    title: "请先开通权限",
                                    content: "该功能需要开通后才可以使用哦",
                                    confirmText: "去开通",
                                    success: function(t) {
                                        t.confirm && e.goBuy()
                                    }
                                })
                            },
                            tipToJoin: function() {
                                var e = this;
                                this.$api.confirm({
                                    title: "请先开通权限",
                                    content: "该功能需要报名后才可以使用哦",
                                    confirmText: "免费报名",
                                    success: function(t) {
                                        t.confirm && e.join()
                                    }
                                })
                            },
                            join: function() {
                                var e = this;
                                return (0, r.default)(i.default.mark((function t() {
                                    return i.default.wrap((function(t) {
                                        for (;;) switch (t.prev = t.next) {
                                            case 0:
                                                return t.next = 2, e.interceptLogin();
                                            case 2:
                                                return t.next = 4, e.interceptPhone();
                                            case 4:
                                                e.$api.showLoading(), e.$http({
                                                    methods: "POST",
                                                    url: "/api/questions_member",
                                                    data: {
                                                        qBankId: e.qbank.id
                                                    }
                                                }).then((function(t) {
                                                    e.$api.hideLoading(), 0 === t.errno && (e.$api.toast("报名成功"), e.getQbank())
                                                }));
                                            case 6:
                                            case "end":
                                                return t.stop()
                                        }
                                    }), t)
                                })))()
                            },
                            goBuy: function() {
                                if (this.sellOut) this.$api.toast("已售罄，暂无法购买");
                                else {
                                    var e = {
                                        type: "questions",
                                        id: this.qbank.id
                                    };
                                    this.agentId && (e.agentId = this.agentId), this.$api.openWin({
                                        url: "/pages/buy/index",
                                        params: e
                                    })
                                }
                            },
                            selectChapter: function(e, t) {
                                var n = this;
                                return (0, r.default)(i.default.mark((function a() {
                                    return i.default.wrap((function(a) {
                                        for (;;) switch (a.prev = a.next) {
                                            case 0:
                                                return a.next = 2, n.interceptLogin();
                                            case 2:
                                                return a.next = 4, n.interceptPhone();
                                            case 4:
                                                return a.next = 6, n.interceptPay(1 == e.free || 1 == t);
                                            case 6:
                                                "practise" == n.qbank.actionType ? n.goQuestion("chapter", {
                                                    title: e.title,
                                                    cid: e.id,
                                                    allCount: e.questionCount
                                                }) : "examPoint" == n.qbank.actionType && n.goQuestion("examPoint", {
                                                    title: e.title,
                                                    cid: e.id,
                                                    allCount: e.examQueCount
                                                });
                                            case 7:
                                            case "end":
                                                return a.stop()
                                        }
                                    }), a)
                                })))()
                            },
                            goQuestion: function(t, n) {
                                var a, i = this,
                                    r = e.getStorageSync("wrongQuestionPageSize") || 100,
                                    s = l({
                                        type: "practice",
                                        subType: t,
                                        qid: this.qbank.id,
                                        pageSize: r
                                    }, n);
                                if ("chapter" == t) a = n.allCount;
                                else if ("viewWrong" == t) a = this.statistics.wrong_question, s.title = "查看错题", s.subType = "wrong", s.subsubType = "viewWrong";
                                else if ("doWrong" == t) a = this.statistics.wrong_question, s.title = "错题重练", s.subType = "wrong", s.subsubType = "doWrong";
                                else if ("favorite" == t) a = this.statistics.favorite, s.title = "我的收藏";
                                else if ("note" == t) a = this.statistics.notes, s.title = "我的笔记";
                                else if ("random" == t) {
                                    a = e.getStorageSync("randomQuestionCount") || 20, s.title = "随机练习"
                                } else if ("examPoint" == t) {
                                    a = n.allCount, s = l({
                                        qid: this.qbank.id
                                    }, n);
                                    var c = e.getStorageSync("lastExamPoint");
                                    e.setStorageSync("lastExamPoint", l(l({}, c), {}, (0, o.default)({}, this.qbank.id, n.cid)))
                                }
                                if ("wrong" == s.subType && a > r) {
                                    for (var u = Math.ceil(a / r), p = [], d = 0; d < u; d++) p.push(r * d + 1 + "-" + (r * (d + 1) > a ? a : r * (d + 1)));
                                    this.$refs.actionSheet.open({
                                        title: "题目过多，选择下要练习的范围吧！",
                                        itemList: p,
                                        itemSize: "13px",
                                        success: function(e) {
                                            var t = e.tapIndex + 1;
                                            console.log("用户选择了第" + t + "页"), console.log("前往答题页"), s = Object.assign(s, {
                                                page: t
                                            }), i.$api.openWin({
                                                url: "/pages/question/index",
                                                params: s
                                            })
                                        }
                                    })
                                } else {
                                    if (("chapter" == t || "examPoint" == t) && 0 == a) return void setTimeout((function() {
                                        i.$api.toast("题目正在准备中")
                                    }), 200);
                                    "examPoint" == t ? (delete s.allCount, this.$api.openWin({
                                        url: "/pages/knowledge/index",
                                        params: s
                                    })) : (s.pageSize = a, this.$api.openWin({
                                        url: "/pages/question/index",
                                        params: s
                                    }))
                                }
                            }
                        }
                    };
                    t.default = f
                }).call(this, n("df3c").default)
            },
            "7aeb": function(e, t, n) {
                "use strict";
                (function(e, t) {
                    var a = n("47a9");
                    n("8f74"), a(n("3240"));
                    var i = a(n("5b4a"));
                    e.__webpack_require_UNI_MP_PLUGIN__ = n, t(i.default)
                }).call(this, n("3223").default, n("df3c").createPage)
            },
            "7b23": function(e, t, n) {
                "use strict";
                n.r(t);
                var a = n("6992"),
                    i = n.n(a);
                for (var o in a)["default"].indexOf(o) < 0 && function(e) {
                    n.d(t, e, (function() {
                        return a[e]
                    }))
                }(o);
                t.default = i.a
            },
            e94d: function(e, t, n) {
                "use strict";
                var a = n("199b");
                n.n(a).a
            }
        },
        [
            ["7aeb", "common/runtime", "common/vendor"]
        ]
    ]);
}, {
    isPage: true,
    isComponent: true,
    currentFile: 'pages/qbank/child.js'
});
require("pages/qbank/child.js");
$gwx5_XC_2 = function(_, _v, _n, _p, _s, _wp, _wl, $gwn, $gwl, $gwh, wh, $gstack, $gwrt, gra, grb, TestTest, wfor, _ca, _da, _r, _rz, _o, _oz, _1, _1z, _2, _2z, _m, _mz, nv_getDate, nv_getRegExp, nv_console, nv_parseInt, nv_parseFloat, nv_isNaN, nv_isFinite, nv_decodeURI, nv_decodeURIComponent, nv_encodeURI, nv_encodeURIComponent, $gdc, nv_JSON, _af, _gv, _ai, _grp, _gd, _gapi, $ixc, _ic, _w, _ev, _tsd) {
    return function(path, global) {
        if (typeof global === 'undefined') {
            if (typeof __GWX_GLOBAL__ === 'undefined') global = {};
            else global = __GWX_GLOBAL__;
        }
        if (typeof __WXML_GLOBAL__ === 'undefined') {
            __WXML_GLOBAL__ = {};
        }
        __WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
        var e_ = {}
        if (typeof(global.entrys) === 'undefined') global.entrys = {};
        e_ = global.entrys;
        var d_ = {}
        if (typeof(global.defines) === 'undefined') global.defines = {};
        d_ = global.defines;
        var f_ = {}
        if (typeof(global.modules) === 'undefined') global.modules = {};
        f_ = global.modules || {};
        var p_ = {}
        __WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
        __WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
        __WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
        var z = __WXML_GLOBAL__.ops_set.$gwx5_XC_2 || [];

        function gz$gwx5_XC_2_1() {
            if (__WXML_GLOBAL__.ops_cached.$gwx5_XC_2_1) return __WXML_GLOBAL__.ops_cached.$gwx5_XC_2_1
            __WXML_GLOBAL__.ops_cached.$gwx5_XC_2_1 = [];
            (function(z) {
                var a = 11;

                function Z(ops) {
                    z.push(ops)
                }
                Z([3, '__l'])
                Z([3, 'data-v-7c6e717a'])
                Z([3, '每日一练'])
                Z([3, '2ff8e5d6-1'])
            })(__WXML_GLOBAL__.ops_cached.$gwx5_XC_2_1);
            return __WXML_GLOBAL__.ops_cached.$gwx5_XC_2_1
        }
        __WXML_GLOBAL__.ops_set.$gwx5_XC_2 = z;
        __WXML_GLOBAL__.ops_init.$gwx5_XC_2 = true;
        var x = ['./pages/qbank/dayPractice/list.wxml'];
        d_[x[0]] = {}
        var m0 = function(e, s, r, gg) {
            var z = gz$gwx5_XC_2_1()
            var tSB = _mz(z, 'nav-bar', ['bind:__l', 0, 'class', 1, 'title', 1, 'vueId', 2], [], e, s, gg)
            _(r, tSB)
            return r
        }
        e_[x[0]] = {
            f: m0,
            j: [],
            i: [],
            ti: [],
            ic: []
        }
        if (path && e_[path]) {
            return function(env, dd, global) {
                $gwxc = 0;
                var root = {
                    "tag": "wx-page"
                };
                root.children = [];
                g = "$gwx5_XC_2";
                var main = e_[path].f
                if (typeof global === "undefined") global = {};
                global.f = $gdc(f_[path], "", 1);
                try {
                    main(env, {}, root, global);
                    _tsd(root)
                } catch (err) {
                    console.log(err)
                };
                g = "";
                return root;
            }
        }
    }
}(__g.a, __g.b, __g.c, __g.d, __g.e, __g.f, __g.g, __g.h, __g.i, __g.j, __g.k, __g.l, __g.m, __g.n, __g.o, __g.p, __g.q, __g.r, __g.s, __g.t, __g.u, __g.v, __g.w, __g.x, __g.y, __g.z, __g.A, __g.B, __g.C, __g.D, __g.E, __g.F, __g.G, __g.H, __g.I, __g.J, __g.K, __g.L, __g.M, __g.N, __g.O, __g.P, __g.Q, __g.R, __g.S, __g.T, __g.U, __g.V, __g.W, __g.X, __g.Y, __g.Z, __g.aa);
if (__vd_version_info__.delayedGwx || false) $gwx5_XC_2();
if (__vd_version_info__.delayedGwx) __wxAppCode__['pages/qbank/dayPractice/list.wxml'] = [$gwx5_XC_2, './pages/qbank/dayPractice/list.wxml'];
else __wxAppCode__['pages/qbank/dayPractice/list.wxml'] = $gwx5_XC_2('./pages/qbank/dayPractice/list.wxml');;
__wxRoute = "pages/qbank/dayPractice/list";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/qbank/dayPractice/list.js";
define("pages/qbank/dayPractice/list.js", function(require, module, exports, window, document, frames, self, location, navigator, localStorage, history, Caches, screen, alert, confirm, prompt, XMLHttpRequest, WebSocket, Reporter, webkit, WeixinJSCore) {
    (global.webpackJsonp = global.webpackJsonp || []).push([
        ["pages/qbank/dayPractice/list"], {
            "0151": function(t, a, n) {
                "use strict";
                n.r(a);
                var e = n("8db0"),
                    i = n.n(e);
                for (var r in e)["default"].indexOf(r) < 0 && function(t) {
                    n.d(a, t, (function() {
                        return e[t]
                    }))
                }(r);
                a.default = i.a
            },
            "2a2c": function(t, a, n) {
                "use strict";
                n.d(a, "b", (function() {
                    return e
                })), n.d(a, "c", (function() {
                    return i
                })), n.d(a, "a", (function() {}));
                var e = function() {
                        this.$createElement;
                        this._self._c
                    },
                    i = []
            },
            "42f1": function(t, a, n) {},
            8718: function(t, a, n) {
                "use strict";
                n.r(a);
                var e = n("2a2c"),
                    i = n("0151");
                for (var r in i)["default"].indexOf(r) < 0 && function(t) {
                    n.d(a, t, (function() {
                        return i[t]
                    }))
                }(r);
                n("cc7e");
                var u = n("828b"),
                    o = Object(u.a)(i.default, e.b, e.c, !1, null, "7c6e717a", null, !1, e.a, void 0);
                a.default = o.exports
            },
            "8db0": function(t, a, n) {
                "use strict";
                var e = n("47a9");
                Object.defineProperty(a, "__esModule", {
                    value: !0
                }), a.default = void 0;
                var i = e(n("7eb4")),
                    r = e(n("ee10")),
                    u = n("0938"),
                    o = n("88d2"),
                    s = {
                        data: function() {
                            return {
                                qbankTitle: "",
                                days: [],
                                daysCount: 7,
                                questionsCount: 10
                            }
                        },
                        onLoad: function(t) {
                            this.qbankId = t.qid, this.questionsCount = t.qcount || 10, getCurrentPages().length < 2 || !t.qid ? this.goQbank() : this.getQbank()
                        },
                        methods: {
                            goQbank: function() {
                                var t = {
                                    tabType: "qbank"
                                };
                                this.qbankId && (t.qbankId = this.qbankId), this.$api.openWin({
                                    type: "reLaunch",
                                    url: "/pages/index",
                                    params: t
                                })
                            },
                            getQbank: function() {
                                var t = this;
                                return (0, r.default)(i.default.mark((function a() {
                                    var n;
                                    return i.default.wrap((function(a) {
                                        for (;;) switch (a.prev = a.next) {
                                            case 0:
                                                return t.$api.showLoading(), a.next = 3, (0, o._getQbankDetail)(t.qbankId);
                                            case 3:
                                                0 === (n = a.sent).errno ? (t.qbank = n.data, t.qbankTitle = t.qbank.title, n.data.isAuthorized || n.data.freeDayPractise ? t.getList() : t.goQbank()) : 100143 === n.errno && t.goQbank();
                                            case 5:
                                            case "end":
                                                return a.stop()
                                        }
                                    }), a)
                                })))()
                            },
                            getList: function() {
                                var t = this;
                                this.$api.showLoading();
                                var a = Date.now() + 864e5;
                                this.$http({
                                    url: "/api/v1/question/day_practise_list",
                                    data: {
                                        qBankId: this.qbankId,
                                        date: (0, u.dateFormat)(a, "yyyy-MM-dd"),
                                        num: this.daysCount
                                    }
                                }).then((function(a) {
                                    t.$api.hideLoading(), 0 === a.errno && (t.days = a.data)
                                }))
                            },
                            goDayPracticeDetail: function(t) {
                                this.$api.openWin({
                                    url: "/pages/question/index",
                                    params: {
                                        type: "paper",
                                        subType: "dayPractice",
                                        qid: this.qbankId,
                                        date: t.date,
                                        isDid: t.joinState,
                                        title: this.qbank.title
                                    }
                                })
                            }
                        }
                    };
                a.default = s
            },
            bb8a: function(t, a, n) {
                "use strict";
                (function(t, a) {
                    var e = n("47a9");
                    n("8f74"), e(n("3240"));
                    var i = e(n("8718"));
                    t.__webpack_require_UNI_MP_PLUGIN__ = n, a(i.default)
                }).call(this, n("3223").default, n("df3c").createPage)
            },
            cc7e: function(t, a, n) {
                "use strict";
                var e = n("42f1");
                n.n(e).a
            }
        },
        [
            ["bb8a", "common/runtime", "common/vendor"]
        ]
    ]);
}, {
    isPage: true,
    isComponent: true,
    currentFile: 'pages/qbank/dayPractice/list.js'
});
require("pages/qbank/dayPractice/list.js");
$gwx5_XC_3 = function(_, _v, _n, _p, _s, _wp, _wl, $gwn, $gwl, $gwh, wh, $gstack, $gwrt, gra, grb, TestTest, wfor, _ca, _da, _r, _rz, _o, _oz, _1, _1z, _2, _2z, _m, _mz, nv_getDate, nv_getRegExp, nv_console, nv_parseInt, nv_parseFloat, nv_isNaN, nv_isFinite, nv_decodeURI, nv_decodeURIComponent, nv_encodeURI, nv_encodeURIComponent, $gdc, nv_JSON, _af, _gv, _ai, _grp, _gd, _gapi, $ixc, _ic, _w, _ev, _tsd) {
    return function(path, global) {
        if (typeof global === 'undefined') {
            if (typeof __GWX_GLOBAL__ === 'undefined') global = {};
            else global = __GWX_GLOBAL__;
        }
        if (typeof __WXML_GLOBAL__ === 'undefined') {
            __WXML_GLOBAL__ = {};
        }
        __WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
        var e_ = {}
        if (typeof(global.entrys) === 'undefined') global.entrys = {};
        e_ = global.entrys;
        var d_ = {}
        if (typeof(global.defines) === 'undefined') global.defines = {};
        d_ = global.defines;
        var f_ = {}
        if (typeof(global.modules) === 'undefined') global.modules = {};
        f_ = global.modules || {};
        var p_ = {}
        __WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
        __WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
        __WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
        var z = __WXML_GLOBAL__.ops_set.$gwx5_XC_3 || [];

        function gz$gwx5_XC_3_1() {
            if (__WXML_GLOBAL__.ops_cached.$gwx5_XC_3_1) return __WXML_GLOBAL__.ops_cached.$gwx5_XC_3_1
            __WXML_GLOBAL__.ops_cached.$gwx5_XC_3_1 = [];
            (function(z) {
                var a = 11;

                function Z(ops) {
                    z.push(ops)
                }
                Z([3, 'data-v-7bb1be34'])
                Z([3, 'transparent'])
                Z([3, '__l'])
                Z(z[0])
                Z([3, '练习报告'])
                Z([3, '5ffc0094-1'])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'g0']
                ])
                Z([3, '#f6f9fa'])
                Z([3, '#00cda2'])
                Z([3, 'circle'])
                Z(z[2])
                Z([
                    [2, '*'],
                    [1, 180],
                    [1, 1.2]
                ])
                Z(z[0])
                Z([1, 1.5])
                Z([3, '#acafb2'])
                Z([1, 20])
                Z([3, '正确率'])
                Z(z[8])
                Z([1, 34])
                Z([
                    [7],
                    [3, 'rightCountPer']
                ])
                Z([3, '5ffc0094-2'])
            })(__WXML_GLOBAL__.ops_cached.$gwx5_XC_3_1);
            return __WXML_GLOBAL__.ops_cached.$gwx5_XC_3_1
        }
        __WXML_GLOBAL__.ops_set.$gwx5_XC_3 = z;
        __WXML_GLOBAL__.ops_init.$gwx5_XC_3 = true;
        var x = ['./pages/qbank/dayPractice/result.wxml'];
        d_[x[0]] = {}
        var m0 = function(e, s, r, gg) {
            var z = gz$gwx5_XC_3_1()
            var bUB = _n('view')
            _rz(z, bUB, 'class', 0, e, s, gg)
            var xWB = _mz(z, 'nav-bar', ['bgColor', 1, 'bind:__l', 1, 'class', 2, 'title', 3, 'vueId', 4], [], e, s, gg)
            _(bUB, xWB)
            var oVB = _v()
            _(bUB, oVB)
            if (_oz(z, 6, e, s, gg)) {
                oVB.wxVkey = 1
                var oXB = _mz(z, 'arc-bar', ['arcbarBackground', 7, 'arcbarDoneBackground', 1, 'arcbarType', 2, 'bind:__l', 3, 'cWidth', 4, 'class', 5, 'startAngle', 6, 'subtitleColor', 7, 'subtitleFontSize', 8, 'subtitleName', 9, 'titleColor', 10, 'titleFontSize', 11, 'titleName', 12, 'vueId', 13], [], e, s, gg)
                _(oVB, oXB)
            }
            oVB.wxXCkey = 1
            oVB.wxXCkey = 3
            _(r, bUB)
            return r
        }
        e_[x[0]] = {
            f: m0,
            j: [],
            i: [],
            ti: [],
            ic: []
        }
        if (path && e_[path]) {
            return function(env, dd, global) {
                $gwxc = 0;
                var root = {
                    "tag": "wx-page"
                };
                root.children = [];
                g = "$gwx5_XC_3";
                var main = e_[path].f
                if (typeof global === "undefined") global = {};
                global.f = $gdc(f_[path], "", 1);
                try {
                    main(env, {}, root, global);
                    _tsd(root)
                } catch (err) {
                    console.log(err)
                };
                g = "";
                return root;
            }
        }
    }
}(__g.a, __g.b, __g.c, __g.d, __g.e, __g.f, __g.g, __g.h, __g.i, __g.j, __g.k, __g.l, __g.m, __g.n, __g.o, __g.p, __g.q, __g.r, __g.s, __g.t, __g.u, __g.v, __g.w, __g.x, __g.y, __g.z, __g.A, __g.B, __g.C, __g.D, __g.E, __g.F, __g.G, __g.H, __g.I, __g.J, __g.K, __g.L, __g.M, __g.N, __g.O, __g.P, __g.Q, __g.R, __g.S, __g.T, __g.U, __g.V, __g.W, __g.X, __g.Y, __g.Z, __g.aa);
if (__vd_version_info__.delayedGwx || false) $gwx5_XC_3();
if (__vd_version_info__.delayedGwx) __wxAppCode__['pages/qbank/dayPractice/result.wxml'] = [$gwx5_XC_3, './pages/qbank/dayPractice/result.wxml'];
else __wxAppCode__['pages/qbank/dayPractice/result.wxml'] = $gwx5_XC_3('./pages/qbank/dayPractice/result.wxml');;
__wxRoute = "pages/qbank/dayPractice/result";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/qbank/dayPractice/result.js";
define("pages/qbank/dayPractice/result.js", function(require, module, exports, window, document, frames, self, location, navigator, localStorage, history, Caches, screen, alert, confirm, prompt, XMLHttpRequest, WebSocket, Reporter, webkit, WeixinJSCore) {
    (global.webpackJsonp = global.webpackJsonp || []).push([
        ["pages/qbank/dayPractice/result"], {
            "0a2a": function(t, n, i) {
                "use strict";
                i.r(n);
                var a = i("2a62"),
                    e = i("3d24");
                for (var o in e)["default"].indexOf(o) < 0 && function(t) {
                    i.d(n, t, (function() {
                        return e[t]
                    }))
                }(o);
                i("f04d");
                var s = i("828b"),
                    c = Object(s.a)(e.default, a.b, a.c, !1, null, "7bb1be34", null, !1, a.a, void 0);
                n.default = c.exports
            },
            "0d9c": function(t, n, i) {},
            "2a62": function(t, n, i) {
                "use strict";
                i.d(n, "b", (function() {
                    return a
                })), i.d(n, "c", (function() {
                    return e
                })), i.d(n, "a", (function() {}));
                var a = function() {
                        this.$createElement;
                        var t = (this._self._c, this.list.length);
                        this.$mp.data = Object.assign({}, {
                            $root: {
                                g0: t
                            }
                        })
                    },
                    e = []
            },
            "3d24": function(t, n, i) {
                "use strict";
                i.r(n);
                var a = i("db59"),
                    e = i.n(a);
                for (var o in a)["default"].indexOf(o) < 0 && function(t) {
                    i.d(n, t, (function() {
                        return a[t]
                    }))
                }(o);
                n.default = e.a
            },
            c5ca: function(t, n, i) {
                "use strict";
                (function(t, n) {
                    var a = i("47a9");
                    i("8f74"), a(i("3240"));
                    var e = a(i("0a2a"));
                    t.__webpack_require_UNI_MP_PLUGIN__ = i, n(e.default)
                }).call(this, i("3223").default, i("df3c").createPage)
            },
            db59: function(t, n, i) {
                "use strict";
                Object.defineProperty(n, "__esModule", {
                    value: !0
                }), n.default = void 0;
                var a = i("7203"),
                    e = {
                        components: {
                            arcBar: function() {
                                Promise.all([i.e("common/vendor"), i.e("components/wx-charts/arcbar")]).then(function() {
                                    return resolve(i("c440"))
                                }.bind(null, i)).catch(i.oe)
                            }
                        },
                        data: function() {
                            return {
                                dayPracticeDate: "",
                                qbankTitle: "",
                                list: [],
                                rightCount: 0
                            }
                        },
                        computed: {
                            rightCountPer: function() {
                                return this.list.length ? Math.round(this.rightCount / this.list.length * 1e4) / 100 : 0
                            }
                        },
                        onLoad: function(t) {
                            this.qbankId = t.qid, this.dayPracticeDate = t.dpDate, this.getDetail()
                        },
                        methods: {
                            getDetail: function() {
                                var t = this;
                                this.$api.showLoading(), this.$http({
                                    url: "/api/v1/question/day_practise_nestification",
                                    data: {
                                        qBankId: this.qbankId,
                                        date: this.dayPracticeDate,
                                        studentAnswer: 1
                                    }
                                }).then((function(n) {
                                    if (t.$api.hideLoading(), 0 === n.errno) {
                                        t.qbankTitle = n.data.title;
                                        var i = n.data;
                                        if ("string" == typeof i.questions) {
                                            t.pa = [126, 225, 237, 227, 217, 208, 196], t.pb = [192, 195, 197, 148, 99, 101, 146], t.pc = [215, 237, 227, 226, 221, 212, 224];
                                            var e = t.pa.concat(t.pb).concat(t.pc);
                                            if (i.optional && i.optional.qEncrypt && i.optional.qEncrypt.key) {
                                                var o = (0, a.decryptQuestion)(i.optional.qEncrypt.key, (0, a.decryptKey)(e)),
                                                    s = (0, a.decryptQuestion)(i.questions, o);
                                                i.questions = JSON.parse(s)
                                            } else i.questions = []
                                        }
                                        var c = [],
                                            r = 0;
                                        n.data.questions.forEach((function(t) {
                                            6 == t.type && t.subQuestion ? t.subQuestion.forEach((function(t) {
                                                1 == t.isRight && r++, void 0 === t.isRight ? c.push("-undo") : c.push(t.isRight)
                                            })) : (1 == t.isRight && r++, void 0 === t.isRight ? c.push("-undo") : c.push(t.isRight))
                                        })), t.list = c, t.rightCount = r
                                    }
                                }))
                            },
                            goDayPracticeAnalysis: function() {
                                this.$api.openWin({
                                    url: "/pages/question/index",
                                    params: {
                                        type: "paper",
                                        subType: "dayPractice",
                                        qid: this.qbankId,
                                        date: this.dayPracticeDate,
                                        isAnalysis: 1
                                    }
                                })
                            },
                            goQabnkHome: function() {
                                this.$api.openWin({
                                    type: "relanuch",
                                    url: "/pages/index",
                                    params: {
                                        tabType: "qbank",
                                        qbankId: this.qbankId
                                    }
                                })
                            }
                        }
                    };
                n.default = e
            },
            f04d: function(t, n, i) {
                "use strict";
                var a = i("0d9c");
                i.n(a).a
            }
        },
        [
            ["c5ca", "common/runtime", "common/vendor"]
        ]
    ]);
}, {
    isPage: true,
    isComponent: true,
    currentFile: 'pages/qbank/dayPractice/result.js'
});
require("pages/qbank/dayPractice/result.js");
$gwx5_XC_4 = function(_, _v, _n, _p, _s, _wp, _wl, $gwn, $gwl, $gwh, wh, $gstack, $gwrt, gra, grb, TestTest, wfor, _ca, _da, _r, _rz, _o, _oz, _1, _1z, _2, _2z, _m, _mz, nv_getDate, nv_getRegExp, nv_console, nv_parseInt, nv_parseFloat, nv_isNaN, nv_isFinite, nv_decodeURI, nv_decodeURIComponent, nv_encodeURI, nv_encodeURIComponent, $gdc, nv_JSON, _af, _gv, _ai, _grp, _gd, _gapi, $ixc, _ic, _w, _ev, _tsd) {
    return function(path, global) {
        if (typeof global === 'undefined') {
            if (typeof __GWX_GLOBAL__ === 'undefined') global = {};
            else global = __GWX_GLOBAL__;
        }
        if (typeof __WXML_GLOBAL__ === 'undefined') {
            __WXML_GLOBAL__ = {};
        }
        __WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
        var e_ = {}
        if (typeof(global.entrys) === 'undefined') global.entrys = {};
        e_ = global.entrys;
        var d_ = {}
        if (typeof(global.defines) === 'undefined') global.defines = {};
        d_ = global.defines;
        var f_ = {}
        if (typeof(global.modules) === 'undefined') global.modules = {};
        f_ = global.modules || {};
        var p_ = {}
        __WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
        __WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
        __WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
        var z = __WXML_GLOBAL__.ops_set.$gwx5_XC_4 || [];

        function gz$gwx5_XC_4_1() {
            if (__WXML_GLOBAL__.ops_cached.$gwx5_XC_4_1) return __WXML_GLOBAL__.ops_cached.$gwx5_XC_4_1
            __WXML_GLOBAL__.ops_cached.$gwx5_XC_4_1 = [];
            (function(z) {
                var a = 11;

                function Z(ops) {
                    z.push(ops)
                }
                Z([3, 'data-v-5bf1ac2e'])
                Z([
                    [2, '||'],
                    [
                        [7],
                        [3, 'themeColor']
                    ],
                    [1, '#fff']
                ])
                Z([3, '__l'])
                Z(z[0])
                Z([
                    [2, '||'],
                    [
                        [6],
                        [
                            [7],
                            [3, 'qbank']
                        ],
                        [3, 'title']
                    ],
                    [1, '在线题库']
                ])
                Z([3, 'd6fb36cc-1'])
                Z([
                    [7],
                    [3, 'pageReady']
                ])
                Z([3, 'page-wrap data-v-5bf1ac2e'])
                Z([3, 'pro-price-box data-v-5bf1ac2e'])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'g0']
                ])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'qbank']
                    ],
                    [3, 'marketPrice']
                ])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'qbank']
                    ],
                    [3, 'id']
                ])
                Z([
                    [2, '||'],
                    [
                        [7],
                        [3, 'isShowActivity']
                    ],
                    [
                        [7],
                        [3, 'isHaveXuDiscount']
                    ]
                ])
                Z([1, true])
                Z(z[13])
                Z([3, 'activity-swiper data-v-5bf1ac2e'])
                Z([1, 400])
                Z([1, false])
                Z([1, 3000])
                Z([
                    [2, '||'],
                    [
                        [7],
                        [3, 'isHaveDiscount']
                    ],
                    [
                        [7],
                        [3, 'isHaveXuDiscount']
                    ]
                ])
                Z(z[2])
                Z([3, '__e'])
                Z(z[21])
                Z([3, 'data-v-5bf1ac2e vue-ref'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [5],
                            [
                                [4],
                                [
                                    [5],
                                    [
                                        [5],
                                        [1, '^timeEnd']
                                    ],
                                    [
                                        [4],
                                        [
                                            [5],
                                            [
                                                [4],
                                                [
                                                    [5],
                                                    [1, 'getQbank']
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^xfEvent']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [
                                                    [5],
                                                    [1, 'intercept']
                                                ],
                                                [
                                                    [4],
                                                    [
                                                        [5],
                                                        [
                                                            [4],
                                                            [
                                                                [5],
                                                                [
                                                                    [5],
                                                                    [
                                                                        [5],
                                                                        [1, 'o']
                                                                    ],
                                                                    [
                                                                        [4],
                                                                        [
                                                                            [5],
                                                                            [
                                                                                [5],
                                                                                [1, 'methods']
                                                                            ],
                                                                            [1, 'clickBottomButton']
                                                                        ]
                                                                    ]
                                                                ],
                                                                [
                                                                    [4],
                                                                    [
                                                                        [5],
                                                                        [
                                                                            [5],
                                                                            [1, 'renew']
                                                                        ],
                                                                        [1, true]
                                                                    ]
                                                                ]
                                                            ]
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'timeDiscount'])
                Z([
                    [7],
                    [3, 'qbank']
                ])
                Z([
                    [6],
                    [
                        [6],
                        [
                            [7],
                            [3, 'qbank']
                        ],
                        [3, 'activity']
                    ],
                    [3, 'discount']
                ])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'qbank']
                    ],
                    [3, 'price']
                ])
                Z([3, 'd6fb36cc-2'])
                Z([
                    [7],
                    [3, 'isHaveTuan']
                ])
                Z(z[2])
                Z(z[21])
                Z(z[23])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^success']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'pintuanSuccessCallback']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'tuan'])
                Z(z[11])
                Z([3, 'q_bank'])
                Z([3, 'd6fb36cc-3'])
                Z([
                    [7],
                    [3, 'isHaveCut']
                ])
                Z(z[2])
                Z(z[23])
                Z([3, 'priceCut'])
                Z([
                    [6],
                    [
                        [6],
                        [
                            [6],
                            [
                                [7],
                                [3, 'qbank']
                            ],
                            [3, 'activity']
                        ],
                        [3, 'price_cut']
                    ],
                    [3, 'setId']
                ])
                Z([3, 'd6fb36cc-4'])
                Z([
                    [2, '&&'],
                    [
                        [2, '&&'],
                        [
                            [2, '=='],
                            [
                                [7],
                                [3, 'showUserSwitch']
                            ],
                            [1, 1]
                        ],
                        [
                            [7],
                            [3, 'memberHasBeenLoaded']
                        ]
                    ],
                    [
                        [7],
                        [3, 'allMemberCount']
                    ]
                ])
                Z([3, 'member-box data-v-5bf1ac2e'])
                Z([3, 'member-box-top data-v-5bf1ac2e'])
                Z([3, 'hover'])
                Z([3, 'user-avatar-list data-v-5bf1ac2e'])
                Z([3, 'index'])
                Z([3, 'item'])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'l0']
                ])
                Z(z[50])
                Z([
                    [2, '<'],
                    [
                        [7],
                        [3, 'index']
                    ],
                    [1, 3]
                ])
                Z([
                    [2, '>'],
                    [
                        [7],
                        [3, 'allMemberCount']
                    ],
                    [1, 3]
                ])
                Z([
                    [2, '>'],
                    [
                        [7],
                        [3, 'allMemberCount']
                    ],
                    [1, 10]
                ])
                Z([
                    [7],
                    [3, 'memberLoading']
                ])
                Z([
                    [7],
                    [3, 'memberAutoplay']
                ])
                Z(z[21])
                Z(z[13])
                Z([3, 'member-box-swiper data-v-5bf1ac2e'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, 'change']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [
                                                    [5],
                                                    [1, 'memberSwiperChange']
                                                ],
                                                [
                                                    [4],
                                                    [
                                                        [5],
                                                        [1, '$event']
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([1, 300])
                Z(z[17])
                Z(z[18])
                Z(z[13])
                Z(z[50])
                Z(z[51])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'l1']
                ])
                Z([3, 'id'])
                Z([
                    [2, '=='],
                    [
                        [2, '%'],
                        [
                            [7],
                            [3, 'index']
                        ],
                        [1, 2]
                    ],
                    [1, 0]
                ])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'memberList']
                    ],
                    [
                        [2, '+'],
                        [
                            [7],
                            [3, 'index']
                        ],
                        [1, 1]
                    ]
                ])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'g1']
                ])
                Z([3, 'detail card-common data-v-5bf1ac2e'])
                Z([
                    [7],
                    [3, 'isHaveWechatGroup']
                ])
                Z([
                    [7],
                    [3, 'detail']
                ])
                Z(z[2])
                Z([3, 'detail-content data-v-5bf1ac2e'])
                Z(z[76])
                Z([3, 'd6fb36cc-5'])
                Z([3, 'page-bottom data-v-5bf1ac2e'])
                Z([
                    [2, '+'],
                    [
                        [2, '+'],
                        [1, 'padding-bottom:'],
                        [
                            [2, '+'],
                            [
                                [7],
                                [3, 'safeAreaBottom']
                            ],
                            [1, 'px']
                        ]
                    ],
                    [1, ';']
                ])
                Z([
                    [7],
                    [3, 'showBuyVip']
                ])
                Z([
                    [2, '||'],
                    [
                        [7],
                        [3, 'showKefu']
                    ],
                    [
                        [7],
                        [3, 'useCart']
                    ]
                ])
                Z([3, 'bottom-left data-v-5bf1ac2e'])
                Z([
                    [7],
                    [3, 'showKefu']
                ])
                Z([
                    [7],
                    [3, 'useCart']
                ])
                Z(z[21])
                Z([3, 'item data-v-5bf1ac2e'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, 'tap']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [
                                                    [5],
                                                    [1, 'intercept']
                                                ],
                                                [
                                                    [4],
                                                    [
                                                        [5],
                                                        [
                                                            [4],
                                                            [
                                                                [5],
                                                                [
                                                                    [5],
                                                                    [1, 'o']
                                                                ],
                                                                [
                                                                    [4],
                                                                    [
                                                                        [5],
                                                                        [
                                                                            [5],
                                                                            [1, 'methods']
                                                                        ],
                                                                        [1, 'goCart']
                                                                    ]
                                                                ]
                                                            ]
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([
                    [7],
                    [3, 'cartGoodsNum']
                ])
                Z(z[21])
                Z([3, 'button data-v-5bf1ac2e'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, 'tap']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [
                                                    [5],
                                                    [1, 'intercept']
                                                ],
                                                [
                                                    [4],
                                                    [
                                                        [5],
                                                        [
                                                            [4],
                                                            [
                                                                [5],
                                                                [
                                                                    [5],
                                                                    [1, 'o']
                                                                ],
                                                                [
                                                                    [4],
                                                                    [
                                                                        [5],
                                                                        [
                                                                            [5],
                                                                            [1, 'methods']
                                                                        ],
                                                                        [1, 'clickBottomButton']
                                                                    ]
                                                                ]
                                                            ]
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([
                    [2, '!'],
                    [
                        [7],
                        [3, 'isHave']
                    ]
                ])
                Z(z[0])
                Z(z[28])
                Z([
                    [2, '&&'],
                    [
                        [7],
                        [3, 'adminConfig']
                    ],
                    [
                        [6],
                        [
                            [7],
                            [3, 'adminConfig']
                        ],
                        [3, 'loaded']
                    ]
                ])
                Z(z[0])
                Z([
                    [7],
                    [3, 'hiddenPrice']
                ])
                Z([
                    [7],
                    [3, 'sellOut']
                ])
                Z([
                    [7],
                    [3, 'isCanUseDiscount']
                ])
                Z([
                    [2, '||'],
                    [
                        [7],
                        [3, 'isHaveTuan']
                    ],
                    [
                        [7],
                        [3, 'isHaveCut']
                    ]
                ])
                Z([3, 'button-tuan data-v-5bf1ac2e'])
                Z(z[30])
                Z(z[39])
                Z(z[2])
                Z(z[23])
                Z([3, 'skus'])
                Z([3, 'd6fb36cc-6'])
                Z(z[2])
                Z(z[21])
                Z(z[23])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^selectItem']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'clickShareMenuItem']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'shareMenu'])
                Z([
                    [7],
                    [3, 'shareMenuPushData']
                ])
                Z([
                    [7],
                    [3, 'shareInfo']
                ])
                Z([3, 'd6fb36cc-7'])
                Z(z[2])
                Z(z[21])
                Z(z[23])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^success']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'loginSuccess']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'login'])
                Z([3, 'd6fb36cc-8'])
                Z([
                    [7],
                    [3, 'allowPayType']
                ])
                Z([
                    [7],
                    [3, 'assistantQrcode']
                ])
                Z(z[2])
                Z(z[21])
                Z(z[23])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^success']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'paymentSuccess']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'payment'])
                Z([
                    [7],
                    [3, 'qbankId']
                ])
                Z([3, 'questions'])
                Z([3, 'd6fb36cc-9'])
                Z(z[2])
                Z(z[23])
                Z([3, 'openModal'])
                Z([3, 'd6fb36cc-10'])
                Z([
                    [7],
                    [3, 'isShowWechatGroup']
                ])
                Z(z[21])
                Z([3, 'add-group-modal data-v-5bf1ac2e'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, 'tap']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [
                                                    [5],
                                                    [1, 'e0']
                                                ],
                                                [
                                                    [4],
                                                    [
                                                        [5],
                                                        [1, '$event']
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z(z[21])
                Z([3, 'add-group-panel data-v-5bf1ac2e'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, 'tap']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [
                                                    [5],
                                                    [1, '']
                                                ],
                                                [
                                                    [4],
                                                    [
                                                        [5],
                                                        [1, '$event']
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z(z[2])
                Z(z[0])
                Z(z[11])
                Z(z[133])
                Z([3, 'd6fb36cc-11'])
                Z(z[2])
                Z(z[23])
                Z([3, 'giftList'])
                Z([3, 'd6fb36cc-12'])
                Z(z[2])
                Z(z[0])
                Z([3, 'd6fb36cc-13'])
            })(__WXML_GLOBAL__.ops_cached.$gwx5_XC_4_1);
            return __WXML_GLOBAL__.ops_cached.$gwx5_XC_4_1
        }
        __WXML_GLOBAL__.ops_set.$gwx5_XC_4 = z;
        __WXML_GLOBAL__.ops_init.$gwx5_XC_4 = true;
        var x = ['./pages/qbank/detail.wxml'];
        d_[x[0]] = {}
        var m0 = function(e, s, r, gg) {
            var z = gz$gwx5_XC_4_1()
            var cZB = _n('view')
            _rz(z, cZB, 'class', 0, e, s, gg)
            var c3B = _mz(z, 'nav-bar', ['bgColor', 1, 'bind:__l', 1, 'class', 2, 'title', 3, 'vueId', 4], [], e, s, gg)
            _(cZB, c3B)
            var h1B = _v()
            _(cZB, h1B)
            if (_oz(z, 6, e, s, gg)) {
                h1B.wxVkey = 1
                var o4B = _n('view')
                _rz(z, o4B, 'class', 7, e, s, gg)
                var e8B = _n('view')
                _rz(z, e8B, 'class', 8, e, s, gg)
                var b9B = _v()
                _(e8B, b9B)
                if (_oz(z, 9, e, s, gg)) {
                    b9B.wxVkey = 1
                }
                var o0B = _v()
                _(e8B, o0B)
                if (_oz(z, 10, e, s, gg)) {
                    o0B.wxVkey = 1
                }
                var xAC = _v()
                _(e8B, xAC)
                if (_oz(z, 11, e, s, gg)) {
                    xAC.wxVkey = 1
                }
                b9B.wxXCkey = 1
                o0B.wxXCkey = 1
                xAC.wxXCkey = 1
                _(o4B, e8B)
                var l5B = _v()
                _(o4B, l5B)
                if (_oz(z, 12, e, s, gg)) {
                    l5B.wxVkey = 1
                    var oBC = _mz(z, 'swiper', ['autoplay', 13, 'circular', 1, 'class', 2, 'duration', 3, 'indicatorDots', 4, 'interval', 5], [], e, s, gg)
                    var fCC = _v()
                    _(oBC, fCC)
                    if (_oz(z, 19, e, s, gg)) {
                        fCC.wxVkey = 1
                        var oFC = _mz(z, 'time-discount', ['bind:__l', 20, 'bind:timeEnd', 1, 'bind:xfEvent', 2, 'class', 3, 'data-event-opts', 4, 'data-ref', 5, 'goodsInfo', 6, 'info', 7, 'price', 8, 'vueId', 9], [], e, s, gg)
                        _(fCC, oFC)
                    }
                    var cDC = _v()
                    _(oBC, cDC)
                    if (_oz(z, 30, e, s, gg)) {
                        cDC.wxVkey = 1
                        var cGC = _mz(z, 'pintuan', ['bind:__l', 31, 'bind:success', 1, 'class', 2, 'data-event-opts', 3, 'data-ref', 4, 'goodsId', 5, 'goodsType', 6, 'vueId', 7], [], e, s, gg)
                        _(cDC, cGC)
                    }
                    var hEC = _v()
                    _(oBC, hEC)
                    if (_oz(z, 39, e, s, gg)) {
                        hEC.wxVkey = 1
                        var oHC = _mz(z, 'price-cut', ['bind:__l', 40, 'class', 1, 'data-ref', 2, 'setId', 3, 'vueId', 4], [], e, s, gg)
                        _(hEC, oHC)
                    }
                    fCC.wxXCkey = 1
                    fCC.wxXCkey = 3
                    cDC.wxXCkey = 1
                    cDC.wxXCkey = 3
                    hEC.wxXCkey = 1
                    hEC.wxXCkey = 3
                    _(l5B, oBC)
                }
                var a6B = _v()
                _(o4B, a6B)
                if (_oz(z, 45, e, s, gg)) {
                    a6B.wxVkey = 1
                    var lIC = _n('view')
                    _rz(z, lIC, 'class', 46, e, s, gg)
                    var tKC = _mz(z, 'view', ['class', 47, 'hoverClass', 1], [], e, s, gg)
                    var bMC = _n('view')
                    _rz(z, bMC, 'class', 49, e, s, gg)
                    var xOC = _v()
                    _(bMC, xOC)
                    var oPC = function(cRC, fQC, hSC, gg) {
                        var cUC = _v()
                        _(hSC, cUC)
                        if (_oz(z, 54, cRC, fQC, gg)) {
                            cUC.wxVkey = 1
                        }
                        cUC.wxXCkey = 1
                        return hSC
                    }
                    xOC.wxXCkey = 2
                    _2z(z, 52, oPC, e, s, gg, xOC, 'item', 'index', 'index')
                    var oNC = _v()
                    _(bMC, oNC)
                    if (_oz(z, 55, e, s, gg)) {
                        oNC.wxVkey = 1
                    }
                    oNC.wxXCkey = 1
                    _(tKC, bMC)
                    var eLC = _v()
                    _(tKC, eLC)
                    if (_oz(z, 56, e, s, gg)) {
                        eLC.wxVkey = 1
                    }
                    eLC.wxXCkey = 1
                    _(lIC, tKC)
                    var aJC = _v()
                    _(lIC, aJC)
                    if (_oz(z, 57, e, s, gg)) {
                        aJC.wxVkey = 1
                        var oVC = _mz(z, 'swiper', ['autoplay', 58, 'bindchange', 1, 'circular', 2, 'class', 3, 'data-event-opts', 4, 'duration', 5, 'indicatorDots', 6, 'interval', 7, 'vertical', 8], [], e, s, gg)
                        var lWC = _v()
                        _(oVC, lWC)
                        var aXC = function(eZC, tYC, b1C, gg) {
                            var x3C = _v()
                            _(b1C, x3C)
                            if (_oz(z, 71, eZC, tYC, gg)) {
                                x3C.wxVkey = 1
                                var o4C = _v()
                                _(x3C, o4C)
                                if (_oz(z, 72, eZC, tYC, gg)) {
                                    o4C.wxVkey = 1
                                }
                                o4C.wxXCkey = 1
                            }
                            x3C.wxXCkey = 1
                            return b1C
                        }
                        lWC.wxXCkey = 2
                        _2z(z, 69, aXC, e, s, gg, lWC, 'item', 'index', 'id')
                        _(aJC, oVC)
                    }
                    aJC.wxXCkey = 1
                    _(a6B, lIC)
                }
                var t7B = _v()
                _(o4B, t7B)
                if (_oz(z, 73, e, s, gg)) {
                    t7B.wxVkey = 1
                }
                var f5C = _n('view')
                _rz(z, f5C, 'class', 74, e, s, gg)
                var c6C = _v()
                _(f5C, c6C)
                if (_oz(z, 75, e, s, gg)) {
                    c6C.wxVkey = 1
                }
                var h7C = _v()
                _(f5C, h7C)
                if (_oz(z, 76, e, s, gg)) {
                    h7C.wxVkey = 1
                    var o8C = _mz(z, 'html-parse', ['bind:__l', 77, 'class', 1, 'html', 2, 'vueId', 3], [], e, s, gg)
                    _(h7C, o8C)
                }
                c6C.wxXCkey = 1
                h7C.wxXCkey = 1
                h7C.wxXCkey = 3
                _(o4B, f5C)
                var c9C = _mz(z, 'view', ['class', 81, 'style', 1], [], e, s, gg)
                var o0C = _v()
                _(c9C, o0C)
                if (_oz(z, 83, e, s, gg)) {
                    o0C.wxVkey = 1
                }
                var lAD = _v()
                _(c9C, lAD)
                if (_oz(z, 84, e, s, gg)) {
                    lAD.wxVkey = 1
                    var aBD = _n('view')
                    _rz(z, aBD, 'class', 85, e, s, gg)
                    var tCD = _v()
                    _(aBD, tCD)
                    if (_oz(z, 86, e, s, gg)) {
                        tCD.wxVkey = 1
                    }
                    var eDD = _v()
                    _(aBD, eDD)
                    if (_oz(z, 87, e, s, gg)) {
                        eDD.wxVkey = 1
                        var bED = _mz(z, 'view', ['bindtap', 88, 'class', 1, 'data-event-opts', 2], [], e, s, gg)
                        var oFD = _v()
                        _(bED, oFD)
                        if (_oz(z, 91, e, s, gg)) {
                            oFD.wxVkey = 1
                        }
                        oFD.wxXCkey = 1
                        _(eDD, bED)
                    }
                    tCD.wxXCkey = 1
                    eDD.wxXCkey = 1
                    _(lAD, aBD)
                }
                var xGD = _mz(z, 'view', ['bindtap', 92, 'class', 1, 'data-event-opts', 2], [], e, s, gg)
                var oHD = _v()
                _(xGD, oHD)
                if (_oz(z, 95, e, s, gg)) {
                    oHD.wxVkey = 1
                    var fID = _v()
                    _(oHD, fID)
                    if (_oz(z, 97, e, s, gg)) {
                        fID.wxVkey = 1
                        var cJD = _v()
                        _(fID, cJD)
                        if (_oz(z, 98, e, s, gg)) {
                            cJD.wxVkey = 1
                            var hKD = _v()
                            _(cJD, hKD)
                            if (_oz(z, 100, e, s, gg)) {
                                hKD.wxVkey = 1
                            } else {
                                hKD.wxVkey = 2
                                var oLD = _v()
                                _(hKD, oLD)
                                if (_oz(z, 101, e, s, gg)) {
                                    oLD.wxVkey = 1
                                } else {
                                    oLD.wxVkey = 2
                                    var cMD = _v()
                                    _(oLD, cMD)
                                    if (_oz(z, 102, e, s, gg)) {
                                        cMD.wxVkey = 1
                                    } else {
                                        cMD.wxVkey = 2
                                        var oND = _v()
                                        _(cMD, oND)
                                        if (_oz(z, 103, e, s, gg)) {
                                            oND.wxVkey = 1
                                            var lOD = _n('view')
                                            _rz(z, lOD, 'class', 104, e, s, gg)
                                            var aPD = _v()
                                            _(lOD, aPD)
                                            if (_oz(z, 105, e, s, gg)) {
                                                aPD.wxVkey = 1
                                            }
                                            var tQD = _v()
                                            _(lOD, tQD)
                                            if (_oz(z, 106, e, s, gg)) {
                                                tQD.wxVkey = 1
                                            }
                                            aPD.wxXCkey = 1
                                            tQD.wxXCkey = 1
                                            _(oND, lOD)
                                        } else {
                                            oND.wxVkey = 2
                                        }
                                        oND.wxXCkey = 1
                                    }
                                    cMD.wxXCkey = 1
                                }
                                oLD.wxXCkey = 1
                            }
                            hKD.wxXCkey = 1
                        }
                        cJD.wxXCkey = 1
                    } else {
                        fID.wxVkey = 2
                    }
                    fID.wxXCkey = 1
                } else {
                    oHD.wxVkey = 2
                }
                oHD.wxXCkey = 1
                _(c9C, xGD)
                o0C.wxXCkey = 1
                lAD.wxXCkey = 1
                _(o4B, c9C)
                var eRD = _mz(z, 'skus', ['bind:__l', 107, 'class', 1, 'data-ref', 2, 'vueId', 3], [], e, s, gg)
                _(o4B, eRD)
                l5B.wxXCkey = 1
                l5B.wxXCkey = 3
                a6B.wxXCkey = 1
                t7B.wxXCkey = 1
                _(h1B, o4B)
            }
            var bSD = _mz(z, 'xk-share', ['bind:__l', 111, 'bind:selectItem', 1, 'class', 2, 'data-event-opts', 3, 'data-ref', 4, 'pushData', 5, 'shareInfo', 6, 'vueId', 7], [], e, s, gg)
            _(cZB, bSD)
            var oTD = _mz(z, 'xk-login', ['bind:__l', 119, 'bind:success', 1, 'class', 2, 'data-event-opts', 3, 'data-ref', 4, 'vueId', 5], [], e, s, gg)
            _(cZB, oTD)
            var xUD = _mz(z, 'payment', ['allowPayType', 125, 'assistantQrcode', 1, 'bind:__l', 2, 'bind:success', 3, 'class', 4, 'data-event-opts', 5, 'data-ref', 6, 'productId', 7, 'productType', 8, 'vueId', 9], [], e, s, gg)
            _(cZB, xUD)
            var oVD = _mz(z, 'open-modal', ['bind:__l', 135, 'class', 1, 'data-ref', 2, 'vueId', 3], [], e, s, gg)
            _(cZB, oVD)
            var o2B = _v()
            _(cZB, o2B)
            if (_oz(z, 139, e, s, gg)) {
                o2B.wxVkey = 1
                var fWD = _mz(z, 'view', ['bindtap', 140, 'class', 1, 'data-event-opts', 2], [], e, s, gg)
                var cXD = _mz(z, 'view', ['catchtap', 143, 'class', 1, 'data-event-opts', 2], [], e, s, gg)
                var hYD = _mz(z, 'add-group', ['bind:__l', 146, 'class', 1, 'goodsId', 2, 'goodsType', 3, 'vueId', 4], [], e, s, gg)
                _(cXD, hYD)
                _(fWD, cXD)
                _(o2B, fWD)
            }
            var oZD = _mz(z, 'gift-list', ['bind:__l', 151, 'class', 1, 'data-ref', 2, 'vueId', 3], [], e, s, gg)
            _(cZB, oZD)
            var c1D = _mz(z, 'mp-privacy', ['bind:__l', 155, 'class', 1, 'vueId', 2], [], e, s, gg)
            _(cZB, c1D)
            h1B.wxXCkey = 1
            h1B.wxXCkey = 3
            o2B.wxXCkey = 1
            o2B.wxXCkey = 3
            _(r, cZB)
            return r
        }
        e_[x[0]] = {
            f: m0,
            j: [],
            i: [],
            ti: [],
            ic: []
        }
        if (path && e_[path]) {
            return function(env, dd, global) {
                $gwxc = 0;
                var root = {
                    "tag": "wx-page"
                };
                root.children = [];
                g = "$gwx5_XC_4";
                var main = e_[path].f
                if (typeof global === "undefined") global = {};
                global.f = $gdc(f_[path], "", 1);
                try {
                    main(env, {}, root, global);
                    _tsd(root)
                } catch (err) {
                    console.log(err)
                };
                g = "";
                return root;
            }
        }
    }
}(__g.a, __g.b, __g.c, __g.d, __g.e, __g.f, __g.g, __g.h, __g.i, __g.j, __g.k, __g.l, __g.m, __g.n, __g.o, __g.p, __g.q, __g.r, __g.s, __g.t, __g.u, __g.v, __g.w, __g.x, __g.y, __g.z, __g.A, __g.B, __g.C, __g.D, __g.E, __g.F, __g.G, __g.H, __g.I, __g.J, __g.K, __g.L, __g.M, __g.N, __g.O, __g.P, __g.Q, __g.R, __g.S, __g.T, __g.U, __g.V, __g.W, __g.X, __g.Y, __g.Z, __g.aa);
if (__vd_version_info__.delayedGwx || false) $gwx5_XC_4();
if (__vd_version_info__.delayedGwx) __wxAppCode__['pages/qbank/detail.wxml'] = [$gwx5_XC_4, './pages/qbank/detail.wxml'];
else __wxAppCode__['pages/qbank/detail.wxml'] = $gwx5_XC_4('./pages/qbank/detail.wxml');;
__wxRoute = "pages/qbank/detail";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/qbank/detail.js";
define("pages/qbank/detail.js", function(require, module, exports, window, document, frames, self, location, navigator, localStorage, history, Caches, screen, alert, confirm, prompt, XMLHttpRequest, WebSocket, Reporter, webkit, WeixinJSCore) {
    (global.webpackJsonp = global.webpackJsonp || []).push([
        ["pages/qbank/detail"], {
            "528f": function(e, t, n) {
                "use strict";
                n.r(t);
                var i = n("f5b7"),
                    a = n.n(i);
                for (var o in i)["default"].indexOf(o) < 0 && function(e) {
                    n.d(t, e, (function() {
                        return i[e]
                    }))
                }(o);
                t.default = a.a
            },
            "96dd": function(e, t, n) {},
            abc3: function(e, t, n) {
                "use strict";
                n.r(t);
                var i = n("c0b9"),
                    a = n("528f");
                for (var o in a)["default"].indexOf(o) < 0 && function(e) {
                    n.d(t, e, (function() {
                        return a[e]
                    }))
                }(o);
                n("ecb0");
                var s = n("828b"),
                    r = Object(s.a)(a.default, i.b, i.c, !1, null, "5bf1ac2e", null, !1, i.a, void 0);
                t.default = r.exports
            },
            c0b9: function(e, t, n) {
                "use strict";
                n.d(t, "b", (function() {
                    return a
                })), n.d(t, "c", (function() {
                    return o
                })), n.d(t, "a", (function() {
                    return i
                }));
                var i = {
                        payment: function() {
                            return Promise.all([n.e("common/vendor"), n.e("components/payment/payment")]).then(n.bind(null, "5d4b"))
                        }
                    },
                    a = function() {
                        var e = this,
                            t = (e.$createElement, e._self._c, e.pageReady ? e.formatImageUrl(e.qbank.originPicture, 800, 450, 1) : null),
                            n = e.pageReady ? e.formatThePrice(e.qbank.price) : null,
                            i = e.pageReady ? !e.hiddenQi && e.skus.length > 1 : null,
                            a = e.pageReady && e.qbank.marketPrice ? e.formatThePrice(e.qbank.marketPrice, 3) : null,
                            o = e.pageReady && 1 == e.showUserSwitch && e.memberHasBeenLoaded && e.allMemberCount ? e.__map(e.memberPage1List, (function(t, n) {
                                return {
                                    $orig: e.__get_orig(t),
                                    m3: n < 3 ? e.formatImageUrl(t.userInfo.smallAvatar, 200, 200, 1) : null
                                }
                            })) : null,
                            s = e.pageReady && 1 == e.showUserSwitch && e.memberHasBeenLoaded && e.allMemberCount && e.memberLoading ? e.__map(e.memberList, (function(t, n) {
                                return {
                                    $orig: e.__get_orig(t),
                                    m4: n % 2 == 0 ? e.formatImageUrl(t.userInfo.smallAvatar, 200, 200, 1) : null,
                                    m5: n % 2 == 0 ? e.shortName(t.userInfo.nickname) : null,
                                    m6: n % 2 == 0 ? e.dateFormat(t.joinTime) : null,
                                    m7: n % 2 == 0 && e.memberList[n + 1] ? e.formatImageUrl(e.memberList[n + 1].userInfo.smallAvatar, 200, 200, 1) : null,
                                    m8: n % 2 == 0 && e.memberList[n + 1] ? e.shortName(e.memberList[n + 1].userInfo.nickname) : null,
                                    m9: n % 2 == 0 && e.memberList[n + 1] ? e.dateFormat(e.memberList[n + 1].joinTime) : null
                                }
                            })) : null,
                            r = e.pageReady ? e.giftList.length : null,
                            u = !(e.pageReady && !e.isHave && e.qbank.price && e.adminConfig && e.adminConfig.loaded) || e.hiddenPrice || e.sellOut || e.isCanUseDiscount || e.isHaveTuan || e.isHaveCut || e.useCart ? null : e.allowPayType.length;
                        e._isMounted || (e.e0 = function(t) {
                            e.isShowWechatGroup = !1
                        }), e.$mp.data = Object.assign({}, {
                            $root: {
                                m0: t,
                                m1: n,
                                g0: i,
                                m2: a,
                                l0: o,
                                l1: s,
                                g1: r,
                                g2: u
                            }
                        })
                    },
                    o = []
            },
            c8ec: function(e, t, n) {
                "use strict";
                (function(e, t) {
                    var i = n("47a9");
                    n("8f74"), i(n("3240"));
                    var a = i(n("abc3"));
                    e.__webpack_require_UNI_MP_PLUGIN__ = n, t(a.default)
                }).call(this, n("3223").default, n("df3c").createPage)
            },
            ecb0: function(e, t, n) {
                "use strict";
                var i = n("96dd");
                n.n(i).a
            },
            f5b7: function(e, t, n) {
                "use strict";
                (function(e) {
                    var i = n("47a9");
                    Object.defineProperty(t, "__esModule", {
                        value: !0
                    }), t.default = void 0;
                    var a = i(n("7eb4")),
                        o = i(n("ee10")),
                        s = n("2eff"),
                        r = (n("88d2"), n("0938")),
                        u = i(n("f462")),
                        c = n("62a4"),
                        h = {
                            components: {
                                xkShare: function() {
                                    Promise.all([n.e("common/vendor"), n.e("components/share/share")]).then(function() {
                                        return resolve(n("d066"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                payment: function() {
                                    Promise.all([n.e("common/vendor"), n.e("components/payment/payment")]).then(function() {
                                        return resolve(n("5d4b"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                pintuan: function() {
                                    n.e("components/common/pintuan").then(function() {
                                        return resolve(n("45b8"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                timeDiscount: function() {
                                    n.e("components/common/time-discount").then(function() {
                                        return resolve(n("394f"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                priceCut: function() {
                                    n.e("components/common/price-cut").then(function() {
                                        return resolve(n("4153"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                openModal: function() {
                                    n.e("components/common/modal-open").then(function() {
                                        return resolve(n("8ce3"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                addGroup: function() {
                                    n.e("components/common/add-group").then(function() {
                                        return resolve(n("e22f"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                skus: function() {
                                    Promise.all([n.e("common/vendor"), n.e("components/common/skus")]).then(function() {
                                        return resolve(n("b2e6"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                htmlParse: function() {
                                    n.e("components/htmlParse/index").then(function() {
                                        return resolve(n("cdbd"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                giftList: function() {
                                    Promise.all([n.e("common/vendor"), n.e("components/common/gift-list")]).then(function() {
                                        return resolve(n("4e22"))
                                    }.bind(null, n)).catch(n.oe)
                                }
                            },
                            data: function() {
                                return {
                                    navBarHeight: this.$customBar + this.$statusBar,
                                    safeAreaBottom: this.$safeAreaBottom,
                                    pageReady: !1,
                                    qbankId: 0,
                                    qbank: {},
                                    detail: "",
                                    adminConfig: {},
                                    showKefu: !1,
                                    token: (0, c.getToken)(),
                                    isHave: !1,
                                    sellOut: !1,
                                    readLocalConfig: this.$readLocalConfig,
                                    allowPayType: [],
                                    couponCount: 0,
                                    skus: [],
                                    isShowWechatGroup: !1,
                                    pageParams: {},
                                    allMembeData: {},
                                    allMemberCount: 0,
                                    memberPage: 1,
                                    memberList: [],
                                    memberPage1List: [],
                                    memberHasBeenLoaded: !1,
                                    memberLoading: !1,
                                    memberAutoplay: !0,
                                    shareMenuPushData: [],
                                    giftList: [],
                                    vipShake: !1
                                }
                            },
                            computed: {
                                cartGoodsNum: function() {
                                    return this.$store.state.cartGoods.length
                                },
                                useCart: function() {
                                    return 1 == this.$store.state.useCart && this.allowPayType.findIndex((function(e) {
                                        return "pay" == e
                                    })) > -1
                                },
                                hiddenPrice: function() {
                                    return "" === this.$api.formatThePrice(1, 3)
                                },
                                hiddenQi: function() {
                                    return "" === this.$api.formatThePrice(1, 3) || null === this.$api.formatThePrice(1, 3)
                                },
                                userInfo: function() {
                                    return this.$store.state.userInfo
                                },
                                shareInfo: function() {
                                    if (this.qbank.id) {
                                        var e = "/pages/qbank/detail?id=" + this.qbank.id;
                                        return this.userInfo.agentInfo && this.userInfo.agentInfo.userId ? e += "&agentId=".concat(this.userInfo.agentInfo.userId) : this.qbank.resellerId && (e += "&resellerId=".concat(this.qbank.resellerId)), {
                                            title: this.qbank.title,
                                            summary: this.qbank.description || "点击跟我一起学习",
                                            link: e,
                                            imageUrl: this.formatImageUrl(this.qbank.smallPicture || this.qbank.originPicture, 750, 600, 1)
                                        }
                                    }
                                    return {}
                                },
                                isHaveTuan: function() {
                                    var e = !1;
                                    if (this.qbank.activity && this.qbank.activity.joinbuy) {
                                        var t = +new Date,
                                            n = this.qbank.activity.joinbuy.endTime;
                                        n && t < n && (e = !0)
                                    }
                                    return e
                                },
                                isHaveDiscount: function() {
                                    var e = !1;
                                    if (this.qbank.activity && this.qbank.activity.discount) {
                                        var t = +new Date,
                                            n = (this.qbank.activity.discount.startTime, this.qbank.activity.discount.endTime),
                                            i = this.qbank.activity.discount.discountSetType || 1;
                                        (n && t < n || 2 == i) && (e = !0)
                                    }
                                    return e
                                },
                                isHaveXuDiscount: function() {
                                    var e = !1;
                                    if (this.qbank.activity && this.qbank.activity.discount) {
                                        new Date;
                                        var t = this.qbank.activity.discount.startTime,
                                            n = this.qbank.activity.discount.endTime,
                                            i = this.qbank.activity.discount.discountSetType || 1;
                                        0 == t && 0 == n && 2 == i && (e = !0)
                                    }
                                    return e
                                },
                                isHaveCut: function() {
                                    var e = !1;
                                    if (this.qbank.activity && this.qbank.activity.price_cut) {
                                        var t = +new Date,
                                            n = (this.qbank.activity.price_cut.startTime, this.qbank.activity.price_cut.endTime);
                                        n && t < n && (e = !0)
                                    }
                                    return e
                                },
                                isShowActivity: function() {
                                    return !!!(this.isHave || this.isAgentCoupon || this.hiddenPrice || this.sellOut) && (this.isHaveTuan || this.isHaveDiscount || this.isHaveCut)
                                },
                                isCanUseDiscount: function() {
                                    var e = !1;
                                    if (this.qbank.activity && this.qbank.activity.discount) {
                                        var t = +new Date,
                                            n = this.qbank.activity.discount,
                                            i = n.startTime,
                                            a = n.endTime,
                                            o = 0 == n.count || n.stock > 0,
                                            s = this.qbank.activity.discount.discountSetType || 1;
                                        (t > i && t < a || 2 == s) && o && (e = !0)
                                    }
                                    return e
                                },
                                isHaveWechatGroup: function() {
                                    var e = !1;
                                    if (this.qbank.optional && this.qbank.optional.hook) {
                                        var t = this.qbank.optional.hook.anchor || 0;
                                        e = 1 == t || 3 == t
                                    }
                                    return e
                                },
                                addWechatGroupHook: function() {
                                    var e = 0;
                                    return this.qbank.optional && this.qbank.optional.hook && (e = this.qbank.optional.hook.anchor || 0), e
                                },
                                beforeJoinShowWechatGroup: function() {
                                    var e = !1;
                                    return this.qbank.optional && this.qbank.optional.hook && (e = 1 == this.qbank.optional.hook.beforeJoinShow), e
                                },
                                showUserSwitch: function() {
                                    return "ios" == e.getSystemInfoSync().platform ? 0 : this.qbank.optional && this.qbank.optional.showUser && this.qbank.optional.showUser.switch || 0
                                },
                                showBuyVip: function() {
                                    return "ios" != e.getSystemInfoSync().platform && !(!this.qbank.vipInfo || !this.qbank.vipInfo.id || this.isHave)
                                },
                                assistantQrcode: function() {
                                    return this.qbank.optional && this.qbank.optional.assistantQrcode ? this.qbank.optional.assistantQrcode : ""
                                }
                            },
                            onLoad: function(e) {
                                if (!e.id) return this.$api.error("缺少参数id");
                                this.pageParams = e, this.qbankId = e.id, this.inviterId = e.inviterId || 0, this.getQbank()
                            },
                            onShow: function() {
                                this.getActivityInfo(), this.memberAutoplay = !0
                            },
                            onHide: function() {
                                this.memberAutoplay = !1
                            },
                            onShareAppMessage: function() {
                                return {
                                    title: this.shareInfo.title,
                                    path: this.shareInfo.link,
                                    imageUrl: this.shareInfo.imageUrl
                                }
                            },
                            onShareTimeline: function() {
                                return {
                                    title: this.shareInfo.title,
                                    query: this.shareInfo.link.split("?")[1] || "",
                                    imageUrl: this.shareInfo.imageUrl
                                }
                            },
                            filters: {
                                toFixed: function(e) {
                                    var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 2;
                                    return e.toFixed(t)
                                }
                            },
                            methods: {
                                addEventListener: function(e, t) {
                                    "paymented" == e && "questions" == t.type && this.paymentSuccess()
                                },
                                shortName: function(e) {
                                    return e ? e.replace(/^([\w\W])[\w\W]*([\w\W])$/, "$1****$2") : "******"
                                },
                                dateFormat: function(e) {
                                    return (0, r.dateFormat2)(e)
                                },
                                sharePage: function() {
                                    this.qbank.id && (this.qbank.price && this.qbank.resellerId && (this.shareMenuPushData = [{
                                        type: "shareToMoeny",
                                        imgUrl: "https://js.cdn.ixunke.com/v3/app/imgcdn/menu/money.png",
                                        text: "分享赚钱",
                                        badge: "推荐"
                                    }]), this.$refs.shareMenu.open())
                                },
                                clickShareMenuItem: function(e) {
                                    "shareToMoeny" == e && this.intercept({
                                        methods: "goShareCard"
                                    })
                                },
                                goShareCard: function() {
                                    e.setStorageSync("resellerShareProInfo", {
                                        thumb: this.qbank.originPicture,
                                        title: this.qbank.title
                                    }), this.$api.openWin({
                                        url: "/pages/reseller/card",
                                        params: {
                                            type: "product",
                                            goodsType: "qBank",
                                            goodsId: this.qbank.id
                                        }
                                    })
                                },
                                pintuanSuccessCallback: function() {
                                    this.pintuanSuccessHaveCallback || (this.pintuanSuccessHaveCallback = !0, this.getQbank())
                                },
                                recordInviterId: function() {
                                    this.token && this.inviterId && this.qbankId && this.$http({
                                        method: "POST",
                                        url: "/api/user/record_invite",
                                        data: {
                                            note: "questions",
                                            id: this.qbankId,
                                            userId: this.inviterId
                                        },
                                        token: 1
                                    })
                                },
                                getQbank: function() {
                                    var t = this;
                                    return (0, o.default)(a.default.mark((function n() {
                                        var i, o, u, h, d, l, f, m, p, g;
                                        return a.default.wrap((function(n) {
                                            for (;;) switch (n.prev = n.next) {
                                                case 0:
                                                    return t.token = (0, c.getToken)(), t.$api.showLoading(), n.next = 4, t.$http({
                                                        url: "/api/q_bank",
                                                        data: {
                                                            id: t.qbankId
                                                        }
                                                    });
                                                case 4:
                                                    if (i = n.sent, t.$api.hideLoading(), t.recordInviterId(), t.pageReady = !0, 0 !== i.errno) {
                                                        n.next = 39;
                                                        break
                                                    }
                                                    return i.data.goodsText = "题库", t.qbank = i.data, t.isHave = i.data.isAuthorized, t.pluginArr = t.qbank.pluginArr || [], t.detail = (0, r.formatRichText)(t.qbank.content, {
                                                        pBottom: 0,
                                                        fontSize: 15,
                                                        imgBlock: !0
                                                    }), o = i.data.qBankSku || [], u = Date.now(), (o = o.filter((function(e) {
                                                        return !(e.learnDeadline && e.learnDeadline < u)
                                                    }))).length ? t.readLocalConfig ? (o.forEach((function(e) {
                                                        0 != e.expiryDay && 0 == e.learnDeadline && (e.learnDeadline = Date.now() + 24 * e.expiryDay * 60 * 60 * 1e3)
                                                    })), 0 == (o = o.sort((function(e, t) {
                                                        return e.learnDeadline - t.learnDeadline
                                                    })))[0].learnDeadline ? t.skus = [o[0]] : t.skus = [o[o.length - 1]]) : t.skus = o.sort((function(e, t) {
                                                        return e.price - t.price
                                                    })) : t.skus = [{
                                                        expiryDay: 0,
                                                        price: i.data.price
                                                    }], t.qbank.price = t.skus[0].price, n.next = 21, (0, s._getAdminConfig)();
                                                case 21:
                                                    return t.adminConfig = n.sent, n.next = 24, (0, s._getAboutApp)();
                                                case 24:
                                                    h = n.sent, d = h.kefuConfig, t.showKefu = 1 == d.kefuSwitch && d.kefuArr.length, l = [], l = t.qbank.optional ? t.qbank.optional.allowPayType || [] : ["pay", "key", "password"], t.adminConfig.payThreeUrl && (l = ["pay"]), "ios" == e.getSystemInfoSync().platform && ((f = l.findIndex((function(e) {
                                                        return "pay" == e
                                                    }))) > -1 && l.splice(f, 1)), t.adminConfig.paySwitch || (m = l.findIndex((function(e) {
                                                        return "pay" == e
                                                    }))) > -1 && l.splice(m, 1), t.allowPayType = l, t.isHave && t.needCheckWechatGroupForJoin && (t.needCheckWechatGroupForJoin = !1, 2 != t.addWechatGroupHook && 3 != t.addWechatGroupHook || (t.isShowWechatGroup = !0)), t.qbank.marketStrategy && (p = t.qbank.marketStrategy.gift || [], g = t.qbank.marketStrategy.virtualGift || [], t.giftList = p.concat(g)), t.getActivityInfo(), t.getMembers(), n.next = 40;
                                                    break;
                                                case 39:
                                                    100143 === i.errno && (t.token = 0, t.goLogin());
                                                case 40:
                                                case "end":
                                                    return n.stop()
                                            }
                                        }), n)
                                    })))()
                                },
                                openGiftList: function(e) {
                                    this.$refs.giftList && this.$refs.giftList.open({
                                        list: e || this.giftList
                                    })
                                },
                                getMembers: function() {
                                    var e = this;
                                    if (0 != this.showUserSwitch && !this.qbank.isAuthorized) {
                                        if (this.memberLoading = !1, this.allMembeData["page" + this.memberPage]) return this.memberList = this.allMembeData["page" + this.memberPage], void(this.memberLoading = !0);
                                        this.$http({
                                            url: "/api/user/goods_user_list",
                                            data: {
                                                goodsId: this.qbank.id,
                                                goodsType: "qBank",
                                                page: this.memberPage,
                                                pageSize: 20
                                            }
                                        }).then((function(t) {
                                            if (0 == t.errno) {
                                                t.data.forEach((function(e) {
                                                    e.userInfo = e.userInfo || {}
                                                })), e.allMemberCount = t.count;
                                                var n = JSON.stringify(t.data);
                                                1 == t.currentPage && (e.memberPage1List = JSON.parse(n)), e.totalPages = t.totalPages, e.memberList = t.data, e.memberLoading = !0, e.memberHasBeenLoaded = !0, e.allMembeData["page" + e.memberPage] = JSON.parse(n)
                                            }
                                        }))
                                    }
                                },
                                memberSwiperChange: function(e) {
                                    0 == e.detail.current && 1 != this.totalPages && (this.memberPage == this.totalPages ? this.memberPage = 1 : this.memberPage++, this.getMembers())
                                },
                                goGoodsMember: function() {
                                    this.$api.openWin({
                                        url: "/pages/system/goods_member",
                                        params: {
                                            type: "qBank",
                                            id: this.qbank.id
                                        }
                                    })
                                },
                                getActivityInfo: function() {
                                    var e = this;
                                    this.isHaveTuan && this.$nextTick((function() {
                                        e.$refs.tuan && e.$refs.tuan.getInfo()
                                    })), this.isHaveCut && this.$nextTick((function() {
                                        e.$refs.priceCut && e.$refs.priceCut.getInfo()
                                    }))
                                },
                                goPinTuan: function() {
                                    this.$refs.tuan && this.$refs.tuan.clickButton()
                                },
                                goKanjia: function() {
                                    this.$refs.priceCut && this.$refs.priceCut.clickButton()
                                },
                                getUserSelectSku: function() {
                                    var e = this;
                                    return new Promise((function(t, n) {
                                        e.$refs.skus.open({
                                            skus: e.skus,
                                            thumb: e.qbank.originPicture,
                                            success: function(e) {
                                                t(e.id)
                                            }
                                        })
                                    }))
                                },
                                joinCart: function() {
                                    var e = this;
                                    return (0, o.default)(a.default.mark((function t() {
                                        var n;
                                        return a.default.wrap((function(t) {
                                            for (;;) switch (t.prev = t.next) {
                                                case 0:
                                                    if (n = {
                                                            goodsId: e.qbank.id,
                                                            goodsType: "q_bank",
                                                            num: 1
                                                        }, !(e.skus.length > 1)) {
                                                        t.next = 11;
                                                        break
                                                    }
                                                    if (!e.hiddenPrice) {
                                                        t.next = 6;
                                                        break
                                                    }
                                                    n.skuId = e.skus[0].id, t.next = 9;
                                                    break;
                                                case 6:
                                                    return t.next = 8, e.getUserSelectSku();
                                                case 8:
                                                    n.skuId = t.sent;
                                                case 9:
                                                    t.next = 12;
                                                    break;
                                                case 11:
                                                    1 == e.skus.length && e.skus[0].id && (n.skuId = e.skus[0].id);
                                                case 12:
                                                    e.$api.showLoading(), e.$http({
                                                        url: "/api/cart/push",
                                                        data: n,
                                                        method: "POST",
                                                        token: 1
                                                    }).then((function(t) {
                                                        e.$api.hideLoading(), 0 === t.errno && (e.$api.toast("已成功加入购物车"), e.$store.dispatch("getCartGoods"))
                                                    }));
                                                case 14:
                                                case "end":
                                                    return t.stop()
                                            }
                                        }), t)
                                    })))()
                                },
                                goKefu: function() {
                                    e.navigateTo({
                                        url: "/pages/user/kefu"
                                    })
                                },
                                goCart: function() {
                                    e.navigateTo({
                                        url: "/pages/sale/cart"
                                    })
                                },
                                goVip: function() {
                                    e.navigateTo({
                                        url: "/pages/user/vip?id=" + this.qbank.vipInfo.id
                                    })
                                },
                                goLogin: function() {
                                    var e = this;
                                    1154 != this.$store.state.launchOptions.scene ? (0, u.default)({
                                        component: !0,
                                        openComponent: function(t) {
                                            t.reload ? e.getQbank() : e.$refs.login.show()
                                        },
                                        success: function() {
                                            e.loginSuccess()
                                        }
                                    }) : this.$api.toast("请前往小程序使用完整服务")
                                },
                                loginSuccess: function() {
                                    this.getQbank()
                                },
                                intercept: function(e) {
                                    this.token ? "function" == typeof this[e.methods] ? this[e.methods](e) : console.error("错误：" + e.methods + "必须是function") : this.goLogin()
                                },
                                interceptPhone: function(t) {
                                    var n = this,
                                        i = (e.getStorageSync("userInfo") || {}).phone;
                                    this.adminConfig && this.adminConfig.needPhone && !i ? this.$refs.openModal.open({
                                        content: this.adminConfig.needPhoneTip,
                                        success: function(t) {
                                            t.confirm && (t.errMsg && t.errMsg.indexOf("getPhoneNumber") > -1 ? n.bindWechatPhone(t) : e.navigateTo({
                                                url: "/pages/user/phone"
                                            }))
                                        }
                                    }) : t()
                                },
                                addWechatGroup: function() {
                                    this.isHave || this.beforeJoinShowWechatGroup ? this.isShowWechatGroup = !0 : this.$api.toast("开通题库后才可以加群哦")
                                },
                                clickBottomButton: function() {
                                    var e = this,
                                        t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
                                    this.interceptPhone((function() {
                                        if (e.isHave && !t.renew) e.goQbankIndex();
                                        else if (e.needGoRenew = t.renew, e.qbank.price)
                                            if (e.sellOut) e.$api.toast("已售罄，暂无法购买");
                                            else {
                                                var n = e.allowPayType;
                                                0 == n.length ? e.showBuyVip ? setTimeout((function() {
                                                    e.vipShake = !0, e.vipShakeTimer || (e.vipShakeTimer = setTimeout((function() {
                                                        e.vipShake = !1, clearTimeout(e.vipShakeTimer), e.vipShakeTimer = null
                                                    }), 3e3))
                                                }), 10) : e.$api.toast(e.isMpios ? "暂不支持加入" : "暂不支持购买") : 1 == n.length ? "password" == n[0] ? e.$refs.payment.joinProductByPassword() : "key" == n[0] ? e.$refs.payment.joinProductByKey() : "assistant" == n[0] && e.assistantQrcode ? e.$refs.payment.joinProductByAssistant() : e.goBuy() : n.includes("pay") ? e.goBuy() : e.$refs.payment.open()
                                            } else e.joinByFace()
                                    }))
                                },
                                goQbankIndex: function() {
                                    var e = this.qbank.pId || this.qbank.id;
                                    this.isHave ? this.$api.openWin({
                                        type: "reLaunch",
                                        url: "/pages/index",
                                        params: {
                                            tabType: "qbank",
                                            qbankId: e
                                        }
                                    }) : this.$api.openWin({
                                        url: "/pages/qbank/index",
                                        params: {
                                            id: e
                                        }
                                    })
                                },
                                goBuy: function() {
                                    var e = {
                                        type: "questions",
                                        id: this.qbankId
                                    };
                                    this.needGoRenew && (e.renew = 1, this.needGoRenew = !1), this.pageParams.roomId && (e.roomId = this.pageParams.roomId), this.$api.openWin({
                                        url: "/pages/buy/index",
                                        params: e
                                    })
                                },
                                paymentSuccess: function() {
                                    this.needCheckWechatGroupForJoin = !0, this.getQbank()
                                },
                                joinByFace: function() {
                                    var e = this;
                                    this.$api.showLoading(), this.$http({
                                        methods: "POST",
                                        url: "/api/questions_member",
                                        data: {
                                            qBankId: this.qbank.id
                                        }
                                    }).then((function(t) {
                                        e.$api.hideLoading(), 0 === t.errno && (e.$api.toast("报名成功"), e.getQbank())
                                    }))
                                },
                                formatThePrice: function() {
                                    return this.$api.formatThePrice(arguments[0], arguments[1])
                                },
                                formatImageUrl: function(e, t, n, i) {
                                    return this.$api.formatImageUrl(e, t, n, i)
                                }
                            }
                        };
                    t.default = h
                }).call(this, n("df3c").default)
            }
        },
        [
            ["c8ec", "common/runtime", "common/vendor"]
        ]
    ]);
}, {
    isPage: true,
    isComponent: true,
    currentFile: 'pages/qbank/detail.js'
});
require("pages/qbank/detail.js");
$gwx5_XC_5 = function(_, _v, _n, _p, _s, _wp, _wl, $gwn, $gwl, $gwh, wh, $gstack, $gwrt, gra, grb, TestTest, wfor, _ca, _da, _r, _rz, _o, _oz, _1, _1z, _2, _2z, _m, _mz, nv_getDate, nv_getRegExp, nv_console, nv_parseInt, nv_parseFloat, nv_isNaN, nv_isFinite, nv_decodeURI, nv_decodeURIComponent, nv_encodeURI, nv_encodeURIComponent, $gdc, nv_JSON, _af, _gv, _ai, _grp, _gd, _gapi, $ixc, _ic, _w, _ev, _tsd) {
    return function(path, global) {
        if (typeof global === 'undefined') {
            if (typeof __GWX_GLOBAL__ === 'undefined') global = {};
            else global = __GWX_GLOBAL__;
        }
        if (typeof __WXML_GLOBAL__ === 'undefined') {
            __WXML_GLOBAL__ = {};
        }
        __WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
        var e_ = {}
        if (typeof(global.entrys) === 'undefined') global.entrys = {};
        e_ = global.entrys;
        var d_ = {}
        if (typeof(global.defines) === 'undefined') global.defines = {};
        d_ = global.defines;
        var f_ = {}
        if (typeof(global.modules) === 'undefined') global.modules = {};
        f_ = global.modules || {};
        var p_ = {}
        __WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
        __WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
        __WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
        var z = __WXML_GLOBAL__.ops_set.$gwx5_XC_5 || [];

        function gz$gwx5_XC_5_1() {
            if (__WXML_GLOBAL__.ops_cached.$gwx5_XC_5_1) return __WXML_GLOBAL__.ops_cached.$gwx5_XC_5_1
            __WXML_GLOBAL__.ops_cached.$gwx5_XC_5_1 = [];
            (function(z) {
                var a = 11;

                function Z(ops) {
                    z.push(ops)
                }
                Z([
                    [7],
                    [3, 'themeColor']
                ])
                Z([3, '__l'])
                Z([
                    [7],
                    [3, 'pageTitle']
                ])
                Z([3, 'b8dd77c6-1'])
                Z([
                    [7],
                    [3, 'dataHasBeenLoaded']
                ])
                Z([3, 'history-wrap'])
                Z([3, 'index'])
                Z([3, 'item'])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'l1']
                ])
                Z(z[6])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'item']
                    ],
                    [3, 'g0']
                ])
                Z([3, 'jndex'])
                Z([3, 'itemName'])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'item']
                    ],
                    [3, 'l0']
                ])
                Z(z[11])
                Z([3, 'ixunke_unitBox'])
                Z([
                    [2, '=='],
                    [
                        [6],
                        [
                            [6],
                            [
                                [7],
                                [3, 'itemName']
                            ],
                            [3, '$orig']
                        ],
                        [3, 'type']
                    ],
                    [1, 1]
                ])
                Z([3, '__e'])
                Z([3, 'ixunke_unit'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, 'tap']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [
                                                    [5],
                                                    [
                                                        [5],
                                                        [1, 'goHistory']
                                                    ],
                                                    [
                                                        [4],
                                                        [
                                                            [5],
                                                            [1, '$0']
                                                        ]
                                                    ]
                                                ],
                                                [
                                                    [4],
                                                    [
                                                        [5],
                                                        [
                                                            [4],
                                                            [
                                                                [5],
                                                                [
                                                                    [5],
                                                                    [
                                                                        [4],
                                                                        [
                                                                            [5],
                                                                            [
                                                                                [5],
                                                                                [
                                                                                    [5],
                                                                                    [1, 'historyInfo']
                                                                                ],
                                                                                [1, '']
                                                                            ],
                                                                            [
                                                                                [7],
                                                                                [3, 'index']
                                                                            ]
                                                                        ]
                                                                    ]
                                                                ],
                                                                [
                                                                    [4],
                                                                    [
                                                                        [5],
                                                                        [
                                                                            [5],
                                                                            [
                                                                                [5],
                                                                                [1, 'historys']
                                                                            ],
                                                                            [1, '']
                                                                        ],
                                                                        [
                                                                            [7],
                                                                            [3, 'jndex']
                                                                        ]
                                                                    ]
                                                                ]
                                                            ]
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([
                    [2, '>'],
                    [
                        [6],
                        [
                            [7],
                            [3, 'itemName']
                        ],
                        [3, 'g1']
                    ],
                    [1, 0]
                ])
                Z([
                    [2, '>'],
                    [
                        [6],
                        [
                            [7],
                            [3, 'itemName']
                        ],
                        [3, 'g2']
                    ],
                    [1, 1]
                ])
                Z([
                    [6],
                    [
                        [6],
                        [
                            [7],
                            [3, 'itemName']
                        ],
                        [3, '$orig']
                    ],
                    [3, 'questionCount']
                ])
                Z([
                    [2, '=='],
                    [
                        [6],
                        [
                            [6],
                            [
                                [7],
                                [3, 'itemName']
                            ],
                            [3, '$orig']
                        ],
                        [3, 'type']
                    ],
                    [1, 2]
                ])
                Z(z[17])
                Z(z[18])
                Z(z[19])
                Z([3, 'ixunke_unitInfo'])
                Z([
                    [2, '=='],
                    [
                        [6],
                        [
                            [6],
                            [
                                [7],
                                [3, 'itemName']
                            ],
                            [3, '$orig']
                        ],
                        [3, 'status']
                    ],
                    [1, 0]
                ])
                Z([
                    [2, '=='],
                    [
                        [6],
                        [
                            [6],
                            [
                                [7],
                                [3, 'itemName']
                            ],
                            [3, '$orig']
                        ],
                        [3, 'status']
                    ],
                    [1, 1]
                ])
                Z([
                    [2, '=='],
                    [
                        [6],
                        [
                            [6],
                            [
                                [7],
                                [3, 'itemName']
                            ],
                            [3, '$orig']
                        ],
                        [3, 'status']
                    ],
                    [1, 2]
                ])
                Z([
                    [2, '=='],
                    [
                        [7],
                        [3, 'length']
                    ],
                    [1, 0]
                ])
                Z(z[1])
                Z([3, '暂无练习历史'])
                Z([3, 'empty2'])
                Z([3, 'b8dd77c6-2'])
                Z(z[1])
                Z([3, 'vue-ref'])
                Z([3, 'paperModal'])
                Z([3, 'b8dd77c6-3'])
            })(__WXML_GLOBAL__.ops_cached.$gwx5_XC_5_1);
            return __WXML_GLOBAL__.ops_cached.$gwx5_XC_5_1
        }
        __WXML_GLOBAL__.ops_set.$gwx5_XC_5 = z;
        __WXML_GLOBAL__.ops_init.$gwx5_XC_5 = true;
        var x = ['./pages/qbank/history.wxml'];
        d_[x[0]] = {}
        var m0 = function(e, s, r, gg) {
            var z = gz$gwx5_XC_5_1()
            var l3D = _n('view')
            var a4D = _v()
            _(l3D, a4D)
            if (_oz(z, 0, e, s, gg)) {
                a4D.wxVkey = 1
                var e6D = _mz(z, 'nav-bar', ['bind:__l', 1, 'title', 1, 'vueId', 2], [], e, s, gg)
                _(a4D, e6D)
            }
            var t5D = _v()
            _(l3D, t5D)
            if (_oz(z, 4, e, s, gg)) {
                t5D.wxVkey = 1
                var b7D = _n('view')
                _rz(z, b7D, 'class', 5, e, s, gg)
                var x9D = _v()
                _(b7D, x9D)
                var o0D = function(cBE, fAE, hCE, gg) {
                    var cEE = _v()
                    _(hCE, cEE)
                    if (_oz(z, 10, cBE, fAE, gg)) {
                        cEE.wxVkey = 1
                        var oFE = _v()
                        _(cEE, oFE)
                        var lGE = function(tIE, aHE, eJE, gg) {
                            var oLE = _n('view')
                            _rz(z, oLE, 'class', 15, tIE, aHE, gg)
                            var xME = _v()
                            _(oLE, xME)
                            if (_oz(z, 16, tIE, aHE, gg)) {
                                xME.wxVkey = 1
                                var oNE = _mz(z, 'view', ['bindtap', 17, 'class', 1, 'data-event-opts', 2], [], tIE, aHE, gg)
                                var cPE = _n('view')
                                var hQE = _v()
                                _(cPE, hQE)
                                if (_oz(z, 20, tIE, aHE, gg)) {
                                    hQE.wxVkey = 1
                                }
                                var oRE = _v()
                                _(cPE, oRE)
                                if (_oz(z, 21, tIE, aHE, gg)) {
                                    oRE.wxVkey = 1
                                }
                                hQE.wxXCkey = 1
                                oRE.wxXCkey = 1
                                _(oNE, cPE)
                                var fOE = _v()
                                _(oNE, fOE)
                                if (_oz(z, 22, tIE, aHE, gg)) {
                                    fOE.wxVkey = 1
                                }
                                fOE.wxXCkey = 1
                                _(xME, oNE)
                            } else {
                                xME.wxVkey = 2
                                var cSE = _v()
                                _(xME, cSE)
                                if (_oz(z, 23, tIE, aHE, gg)) {
                                    cSE.wxVkey = 1
                                    var oTE = _mz(z, 'view', ['bindtap', 24, 'class', 1, 'data-event-opts', 2], [], tIE, aHE, gg)
                                    var lUE = _n('view')
                                    _rz(z, lUE, 'class', 27, tIE, aHE, gg)
                                    var aVE = _v()
                                    _(lUE, aVE)
                                    if (_oz(z, 28, tIE, aHE, gg)) {
                                        aVE.wxVkey = 1
                                    } else {
                                        aVE.wxVkey = 2
                                        var tWE = _v()
                                        _(aVE, tWE)
                                        if (_oz(z, 29, tIE, aHE, gg)) {
                                            tWE.wxVkey = 1
                                        } else {
                                            tWE.wxVkey = 2
                                            var eXE = _v()
                                            _(tWE, eXE)
                                            if (_oz(z, 30, tIE, aHE, gg)) {
                                                eXE.wxVkey = 1
                                            }
                                            eXE.wxXCkey = 1
                                        }
                                        tWE.wxXCkey = 1
                                    }
                                    aVE.wxXCkey = 1
                                    _(oTE, lUE)
                                    _(cSE, oTE)
                                }
                                cSE.wxXCkey = 1
                            }
                            xME.wxXCkey = 1
                            _(eJE, oLE)
                            return eJE
                        }
                        oFE.wxXCkey = 2
                        _2z(z, 13, lGE, cBE, fAE, gg, oFE, 'itemName', 'jndex', 'jndex')
                    }
                    cEE.wxXCkey = 1
                    return hCE
                }
                x9D.wxXCkey = 2
                _2z(z, 8, o0D, e, s, gg, x9D, 'item', 'index', 'index')
                var o8D = _v()
                _(b7D, o8D)
                if (_oz(z, 31, e, s, gg)) {
                    o8D.wxVkey = 1
                    var bYE = _mz(z, 'xk-empty', ['bind:__l', 32, 'text', 1, 'type', 2, 'vueId', 3], [], e, s, gg)
                    _(o8D, bYE)
                }
                var oZE = _mz(z, 'paper-modal', ['bind:__l', 36, 'class', 1, 'data-ref', 2, 'vueId', 3], [], e, s, gg)
                _(b7D, oZE)
                o8D.wxXCkey = 1
                o8D.wxXCkey = 3
                _(t5D, b7D)
            }
            a4D.wxXCkey = 1
            a4D.wxXCkey = 3
            t5D.wxXCkey = 1
            t5D.wxXCkey = 3
            _(r, l3D)
            return r
        }
        e_[x[0]] = {
            f: m0,
            j: [],
            i: [],
            ti: [],
            ic: []
        }
        if (path && e_[path]) {
            return function(env, dd, global) {
                $gwxc = 0;
                var root = {
                    "tag": "wx-page"
                };
                root.children = [];
                g = "$gwx5_XC_5";
                var main = e_[path].f
                if (typeof global === "undefined") global = {};
                global.f = $gdc(f_[path], "", 1);
                try {
                    main(env, {}, root, global);
                    _tsd(root)
                } catch (err) {
                    console.log(err)
                };
                g = "";
                return root;
            }
        }
    }
}(__g.a, __g.b, __g.c, __g.d, __g.e, __g.f, __g.g, __g.h, __g.i, __g.j, __g.k, __g.l, __g.m, __g.n, __g.o, __g.p, __g.q, __g.r, __g.s, __g.t, __g.u, __g.v, __g.w, __g.x, __g.y, __g.z, __g.A, __g.B, __g.C, __g.D, __g.E, __g.F, __g.G, __g.H, __g.I, __g.J, __g.K, __g.L, __g.M, __g.N, __g.O, __g.P, __g.Q, __g.R, __g.S, __g.T, __g.U, __g.V, __g.W, __g.X, __g.Y, __g.Z, __g.aa);
if (__vd_version_info__.delayedGwx || false) $gwx5_XC_5();
if (__vd_version_info__.delayedGwx) __wxAppCode__['pages/qbank/history.wxml'] = [$gwx5_XC_5, './pages/qbank/history.wxml'];
else __wxAppCode__['pages/qbank/history.wxml'] = $gwx5_XC_5('./pages/qbank/history.wxml');;
__wxRoute = "pages/qbank/history";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/qbank/history.js";
define("pages/qbank/history.js", function(require, module, exports, window, document, frames, self, location, navigator, localStorage, history, Caches, screen, alert, confirm, prompt, XMLHttpRequest, WebSocket, Reporter, webkit, WeixinJSCore) {
    (global.webpackJsonp = global.webpackJsonp || []).push([
        ["pages/qbank/history"], {
            "00be": function(t, e, n) {
                "use strict";
                var a = n("47a9");
                Object.defineProperty(e, "__esModule", {
                    value: !0
                }), e.default = void 0;
                var r = a(n("7eb4")),
                    o = a(n("7ca3")),
                    i = a(n("ee10")),
                    c = a(n("e6fb")),
                    u = n("0938"),
                    s = n("2eff");

                function f(t, e) {
                    var n = Object.keys(t);
                    if (Object.getOwnPropertySymbols) {
                        var a = Object.getOwnPropertySymbols(t);
                        e && (a = a.filter((function(e) {
                            return Object.getOwnPropertyDescriptor(t, e).enumerable
                        }))), n.push.apply(n, a)
                    }
                    return n
                }

                function d(t) {
                    for (var e = 1; e < arguments.length; e++) {
                        var n = null != arguments[e] ? arguments[e] : {};
                        e % 2 ? f(Object(n), !0).forEach((function(e) {
                            (0, o.default)(t, e, n[e])
                        })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(n)) : f(Object(n)).forEach((function(e) {
                            Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(n, e))
                        }))
                    }
                    return t
                }
                var p = {
                    components: {
                        paperModal: function() {
                            Promise.all([n.e("common/vendor"), n.e("components/qbank/paper-modal")]).then(function() {
                                return resolve(n("83b0"))
                            }.bind(null, n)).catch(n.oe)
                        }
                    },
                    data: function() {
                        return {
                            pageTitle: "",
                            dataHasBeenLoaded: !1,
                            length: 0,
                            historyInfo: [],
                            showEmpty: !1
                        }
                    },
                    onLoad: function() {
                        this.getHistoryInfo(), this.getPageTitle()
                    },
                    filters: {
                        dateFormat: function(t) {
                            return (0, u.dateFormat)(t, "yyyy-MM-dd hh:mm")
                        }
                    },
                    methods: {
                        addEventListener: function(t, e) {
                            "updateQbank" == t && this.getHistoryInfo()
                        },
                        getPageTitle: function() {
                            var t = this;
                            (0, s._getAppConfig)(1).then((function(e) {
                                if (0 === e.errno) {
                                    var n = (e.data.qbankLayout || []).find((function(t) {
                                        return "icon" == t.type
                                    })).data.find((function(t) {
                                        return "练习历史" == t.name
                                    }));
                                    t.pageTitle = n ? n.alias || n.name : "练习历史"
                                }
                            }))
                        },
                        getHistoryInfo: function() {
                            var t = this;
                            return (0, i.default)(r.default.mark((function e() {
                                var n, a, o;
                                return r.default.wrap((function(e) {
                                    for (;;) switch (e.prev = e.next) {
                                        case 0:
                                            return t.$api.showLoading(), e.next = 3, (0, c.default)({
                                                method: "GET",
                                                path: "/api/history",
                                                params: {},
                                                checkToken: 1
                                            });
                                        case 3:
                                            if (n = e.sent, t.$api.hideLoading(), t.dataHasBeenLoaded = !0, 0 !== n.errno) {
                                                e.next = 17;
                                                break
                                            }
                                            a = n.data, o = 0;
                                        case 9:
                                            if (!(o < a.length)) {
                                                e.next = 16;
                                                break
                                            }
                                            if (!(a[o].historys.length > 0)) {
                                                e.next = 13;
                                                break
                                            }
                                            return t.length = !0, e.abrupt("break", 16);
                                        case 13:
                                            o++, e.next = 9;
                                            break;
                                        case 16:
                                            t.historyInfo = a;
                                        case 17:
                                        case "end":
                                            return e.stop()
                                    }
                                }), e)
                            })))()
                        },
                        goHistory: function(t) {
                            1 == t.type ? this.goPracticeHistory(t) : 2 == t.type && this.goPaperHistory(t)
                        },
                        goPracticeHistory: function(t) {
                            this.$api.openWin({
                                url: "/pages/question/index",
                                params: {
                                    type: "practice",
                                    subType: "history",
                                    qid: t.qBankId,
                                    cid: t.chapterId,
                                    title: "查看练习记录"
                                }
                            })
                        },
                        goPaperHistory: function(t) {
                            "mockExam" == t.practiseType && 1 == t.showAnswer && 2 != t.status && (t.checkStatus = -1), this.$refs.paperModal.open({
                                paper: d(d({}, t), {}, {
                                    note: "common",
                                    name: t.paperName,
                                    checkStatus: t.status,
                                    id: t.paperId
                                })
                            })
                        }
                    }
                };
                e.default = p
            },
            "2a3f": function(t, e, n) {
                "use strict";
                n.r(e);
                var a = n("fdad"),
                    r = n("baa0");
                for (var o in r)["default"].indexOf(o) < 0 && function(t) {
                    n.d(e, t, (function() {
                        return r[t]
                    }))
                }(o);
                n("5a30");
                var i = n("828b"),
                    c = Object(i.a)(r.default, a.b, a.c, !1, null, null, null, !1, a.a, void 0);
                e.default = c.exports
            },
            3987: function(t, e, n) {
                "use strict";
                (function(t, e) {
                    var a = n("47a9");
                    n("8f74"), a(n("3240"));
                    var r = a(n("2a3f"));
                    t.__webpack_require_UNI_MP_PLUGIN__ = n, e(r.default)
                }).call(this, n("3223").default, n("df3c").createPage)
            },
            "5a30": function(t, e, n) {
                "use strict";
                var a = n("6f66");
                n.n(a).a
            },
            "6f66": function(t, e, n) {},
            baa0: function(t, e, n) {
                "use strict";
                n.r(e);
                var a = n("00be"),
                    r = n.n(a);
                for (var o in a)["default"].indexOf(o) < 0 && function(t) {
                    n.d(e, t, (function() {
                        return a[t]
                    }))
                }(o);
                e.default = r.a
            },
            fdad: function(t, e, n) {
                "use strict";
                n.d(e, "b", (function() {
                    return a
                })), n.d(e, "c", (function() {
                    return r
                })), n.d(e, "a", (function() {}));
                var a = function() {
                        var t = this,
                            e = (t.$createElement, t._self._c, t.dataHasBeenLoaded ? t.__map(t.historyInfo, (function(e, n) {
                                var a = t.__get_orig(e),
                                    r = e.historys.length;
                                return {
                                    $orig: a,
                                    g0: r,
                                    l0: r ? t.__map(e.historys, (function(e, n) {
                                        return {
                                            $orig: t.__get_orig(e),
                                            g1: 1 == e.type ? e.chapters.length : null,
                                            g2: 1 == e.type ? e.chapters.length : null,
                                            f0: 1 == e.type ? t._f("dateFormat")(e.addTime) : null,
                                            f1: 1 != e.type && 2 == e.type ? t._f("dateFormat")(e.addTime) : null
                                        }
                                    })) : null
                                }
                            })) : null);
                        t.$mp.data = Object.assign({}, {
                            $root: {
                                l1: e
                            }
                        })
                    },
                    r = []
            }
        },
        [
            ["3987", "common/runtime", "common/vendor"]
        ]
    ]);
}, {
    isPage: true,
    isComponent: true,
    currentFile: 'pages/qbank/history.js'
});
require("pages/qbank/history.js");
$gwx5_XC_6 = function(_, _v, _n, _p, _s, _wp, _wl, $gwn, $gwl, $gwh, wh, $gstack, $gwrt, gra, grb, TestTest, wfor, _ca, _da, _r, _rz, _o, _oz, _1, _1z, _2, _2z, _m, _mz, nv_getDate, nv_getRegExp, nv_console, nv_parseInt, nv_parseFloat, nv_isNaN, nv_isFinite, nv_decodeURI, nv_decodeURIComponent, nv_encodeURI, nv_encodeURIComponent, $gdc, nv_JSON, _af, _gv, _ai, _grp, _gd, _gapi, $ixc, _ic, _w, _ev, _tsd) {
    return function(path, global) {
        if (typeof global === 'undefined') {
            if (typeof __GWX_GLOBAL__ === 'undefined') global = {};
            else global = __GWX_GLOBAL__;
        }
        if (typeof __WXML_GLOBAL__ === 'undefined') {
            __WXML_GLOBAL__ = {};
        }
        __WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
        var e_ = {}
        if (typeof(global.entrys) === 'undefined') global.entrys = {};
        e_ = global.entrys;
        var d_ = {}
        if (typeof(global.defines) === 'undefined') global.defines = {};
        d_ = global.defines;
        var f_ = {}
        if (typeof(global.modules) === 'undefined') global.modules = {};
        f_ = global.modules || {};
        var p_ = {}
        __WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
        __WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
        __WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
        var z = __WXML_GLOBAL__.ops_set.$gwx5_XC_6 || [];

        function gz$gwx5_XC_6_1() {
            if (__WXML_GLOBAL__.ops_cached.$gwx5_XC_6_1) return __WXML_GLOBAL__.ops_cached.$gwx5_XC_6_1
            __WXML_GLOBAL__.ops_cached.$gwx5_XC_6_1 = [];
            (function(z) {
                var a = 11;

                function Z(ops) {
                    z.push(ops)
                }
                Z([
                    [7],
                    [3, 'pageReady']
                ])
                Z([
                    [7],
                    [3, 'adminConfig']
                ])
                Z([
                    [7],
                    [3, 'agentId']
                ])
                Z([3, '__l'])
                Z([3, '__e'])
                Z([3, 'vue-ref'])
                Z([
                    [7],
                    [3, 'themeColor']
                ])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^scroll']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'qbankPageScroll']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'qbank'])
                Z([1, true])
                Z([
                    [7],
                    [3, 'qbankLayout']
                ])
                Z([
                    [7],
                    [3, 'multipleQbank']
                ])
                Z([
                    [7],
                    [3, 'qbankId']
                ])
                Z([3, '2f526942-1'])
                Z(z[3])
                Z(z[5])
                Z([3, 'login'])
                Z([3, '2f526942-2'])
            })(__WXML_GLOBAL__.ops_cached.$gwx5_XC_6_1);
            return __WXML_GLOBAL__.ops_cached.$gwx5_XC_6_1
        }
        __WXML_GLOBAL__.ops_set.$gwx5_XC_6 = z;
        __WXML_GLOBAL__.ops_init.$gwx5_XC_6 = true;
        var x = ['./pages/qbank/index.wxml'];
        d_[x[0]] = {}
        var m0 = function(e, s, r, gg) {
            var z = gz$gwx5_XC_6_1()
            var o2E = _n('view')
            var f3E = _v()
            _(o2E, f3E)
            if (_oz(z, 0, e, s, gg)) {
                f3E.wxVkey = 1
                var c4E = _mz(z, 'tab-qbank', ['adminConfig', 1, 'agentId', 1, 'bind:__l', 2, 'bind:scroll', 3, 'class', 4, 'color', 5, 'data-event-opts', 6, 'data-ref', 7, 'fromIndex', 8, 'layout', 9, 'multipleQbank', 10, 'urlQbankId', 11, 'vueId', 12], [], e, s, gg)
                _(f3E, c4E)
            }
            var h5E = _mz(z, 'xk-login', ['bind:__l', 14, 'class', 1, 'data-ref', 2, 'vueId', 3], [], e, s, gg)
            _(o2E, h5E)
            f3E.wxXCkey = 1
            f3E.wxXCkey = 3
            _(r, o2E)
            return r
        }
        e_[x[0]] = {
            f: m0,
            j: [],
            i: [],
            ti: [],
            ic: []
        }
        if (path && e_[path]) {
            return function(env, dd, global) {
                $gwxc = 0;
                var root = {
                    "tag": "wx-page"
                };
                root.children = [];
                g = "$gwx5_XC_6";
                var main = e_[path].f
                if (typeof global === "undefined") global = {};
                global.f = $gdc(f_[path], "", 1);
                try {
                    main(env, {}, root, global);
                    _tsd(root)
                } catch (err) {
                    console.log(err)
                };
                g = "";
                return root;
            }
        }
    }
}(__g.a, __g.b, __g.c, __g.d, __g.e, __g.f, __g.g, __g.h, __g.i, __g.j, __g.k, __g.l, __g.m, __g.n, __g.o, __g.p, __g.q, __g.r, __g.s, __g.t, __g.u, __g.v, __g.w, __g.x, __g.y, __g.z, __g.A, __g.B, __g.C, __g.D, __g.E, __g.F, __g.G, __g.H, __g.I, __g.J, __g.K, __g.L, __g.M, __g.N, __g.O, __g.P, __g.Q, __g.R, __g.S, __g.T, __g.U, __g.V, __g.W, __g.X, __g.Y, __g.Z, __g.aa);
if (__vd_version_info__.delayedGwx || false) $gwx5_XC_6();
if (__vd_version_info__.delayedGwx) __wxAppCode__['pages/qbank/index.wxml'] = [$gwx5_XC_6, './pages/qbank/index.wxml'];
else __wxAppCode__['pages/qbank/index.wxml'] = $gwx5_XC_6('./pages/qbank/index.wxml');;
__wxRoute = "pages/qbank/index";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/qbank/index.js";
define("pages/qbank/index.js", function(require, module, exports, window, document, frames, self, location, navigator, localStorage, history, Caches, screen, alert, confirm, prompt, XMLHttpRequest, WebSocket, Reporter, webkit, WeixinJSCore) {
    (global.webpackJsonp = global.webpackJsonp || []).push([
        ["pages/qbank/index"], {
            "5c61": function(n, e, t) {
                "use strict";
                t.d(e, "b", (function() {
                    return a
                })), t.d(e, "c", (function() {
                    return i
                })), t.d(e, "a", (function() {}));
                var a = function() {
                        this.$createElement;
                        this._self._c
                    },
                    i = []
            },
            "776e": function(n, e, t) {
                "use strict";
                (function(n, e) {
                    var a = t("47a9");
                    t("8f74"), a(t("3240"));
                    var i = a(t("a008"));
                    n.__webpack_require_UNI_MP_PLUGIN__ = t, e(i.default)
                }).call(this, t("3223").default, t("df3c").createPage)
            },
            "887a": function(n, e, t) {
                "use strict";
                var a = t("cb86");
                t.n(a).a
            },
            a008: function(n, e, t) {
                "use strict";
                t.r(e);
                var a = t("5c61"),
                    i = t("cf3a");
                for (var r in i)["default"].indexOf(r) < 0 && function(n) {
                    t.d(e, n, (function() {
                        return i[n]
                    }))
                }(r);
                t("887a");
                var o = t("828b"),
                    u = Object(o.a)(i.default, a.b, a.c, !1, null, null, null, !1, a.a, void 0);
                e.default = u.exports
            },
            cb86: function(n, e, t) {},
            cf3a: function(n, e, t) {
                "use strict";
                t.r(e);
                var a = t("f07c"),
                    i = t.n(a);
                for (var r in a)["default"].indexOf(r) < 0 && function(n) {
                    t.d(e, n, (function() {
                        return a[n]
                    }))
                }(r);
                e.default = i.a
            },
            f07c: function(n, e, t) {
                "use strict";
                var a = t("47a9");
                Object.defineProperty(e, "__esModule", {
                    value: !0
                }), e.default = void 0;
                var i = a(t("7eb4")),
                    r = a(t("ee10")),
                    o = a(t("f462")),
                    u = t("2eff"),
                    s = {
                        components: {
                            tabQbank: function() {
                                Promise.all([t.e("common/vendor"), t.e("components/nav/qbank")]).then(function() {
                                    return resolve(t("3a2d"))
                                }.bind(null, t)).catch(t.oe)
                            }
                        },
                        data: function() {
                            return {
                                isFixedNavBar: !1,
                                navBarHeight: this.$statusBar + this.$customBar,
                                pageReady: !1,
                                qbankId: 0,
                                adminConfig: {},
                                qbankLayout: [],
                                agentId: 0,
                                multipleQbank: {},
                                pageTitle: "在线题库",
                                shareInfo: {}
                            }
                        },
                        onLoad: function(n) {
                            this.qbankId = n.id, this.agentId = n.agentId || 0, this.getAppConfig()
                        },
                        onShareAppMessage: function() {
                            return {
                                title: this.shareInfo.title,
                                path: this.shareInfo.link,
                                imageUrl: this.shareInfo.imageUrl
                            }
                        },
                        onShareTimeline: function() {
                            return {
                                title: this.shareInfo.title,
                                query: this.shareInfo.link.split("?")[1] || "",
                                imageUrl: this.shareInfo.imageUrl
                            }
                        },
                        methods: {
                            qbankPageScroll: function(n) {
                                this.multipleQbank.switch || (this.isFixedNavBar = n)
                            },
                            formatImageUrl: function(n, e, t, a) {
                                return this.$api.formatImageUrl(n, e, t, a)
                            },
                            getAppConfig: function() {
                                var n = this;
                                return (0, r.default)(i.default.mark((function e() {
                                    var t, a, r, o;
                                    return i.default.wrap((function(e) {
                                        for (;;) switch (e.prev = e.next) {
                                            case 0:
                                                return n.$api.showLoading(), e.next = 3, (0, u._getAppConfig)(1);
                                            case 3:
                                                if (t = e.sent, n.$api.hideLoading(), 0 !== t.errno) {
                                                    e.next = 15;
                                                    break
                                                }
                                                return t = t.data, n.qbankLayout = t.qbankLayout, a = t.tabBar.list, (r = a.find((function(n) {
                                                    return "qbank" == n.type
                                                }))) && (r.title && (n.pageTitle = r.title), n.multipleQbank = r.multipleQbank || {}, n.multipleQbank.switch && (n.isFixedNavBar = !0), o = (o = r.shareImage) ? n.formatImageUrl(o, 350, 280, 1) : "", n.shareInfo = {
                                                    title: r.shareTitle || "分享标题请在后台设置",
                                                    summary: r.shareSummary || "分享摘要请在后台设置",
                                                    link: "/pages/index?tabType=qbank&qbankId=" + n.qbankId,
                                                    imageUrl: o
                                                }), n.pageReady = !0, e.next = 14, (0, u._getAdminConfig)();
                                            case 14:
                                                n.adminConfig = e.sent;
                                            case 15:
                                            case "end":
                                                return e.stop()
                                        }
                                    }), e)
                                })))()
                            },
                            login: function() {
                                var n = this;
                                (0, o.default)({
                                    component: !0,
                                    openComponent: function(e) {
                                        n.$refs.login.show()
                                    },
                                    success: function() {
                                        n.loginSuccess()
                                    }
                                })
                            },
                            goLogin: function() {
                                this.login()
                            },
                            loginSuccess: function() {}
                        }
                    };
                e.default = s
            }
        },
        [
            ["776e", "common/runtime", "common/vendor"]
        ]
    ]);
}, {
    isPage: true,
    isComponent: true,
    currentFile: 'pages/qbank/index.js'
});
require("pages/qbank/index.js");
$gwx5_XC_7 = function(_, _v, _n, _p, _s, _wp, _wl, $gwn, $gwl, $gwh, wh, $gstack, $gwrt, gra, grb, TestTest, wfor, _ca, _da, _r, _rz, _o, _oz, _1, _1z, _2, _2z, _m, _mz, nv_getDate, nv_getRegExp, nv_console, nv_parseInt, nv_parseFloat, nv_isNaN, nv_isFinite, nv_decodeURI, nv_decodeURIComponent, nv_encodeURI, nv_encodeURIComponent, $gdc, nv_JSON, _af, _gv, _ai, _grp, _gd, _gapi, $ixc, _ic, _w, _ev, _tsd) {
    return function(path, global) {
        if (typeof global === 'undefined') {
            if (typeof __GWX_GLOBAL__ === 'undefined') global = {};
            else global = __GWX_GLOBAL__;
        }
        if (typeof __WXML_GLOBAL__ === 'undefined') {
            __WXML_GLOBAL__ = {};
        }
        __WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
        var e_ = {}
        if (typeof(global.entrys) === 'undefined') global.entrys = {};
        e_ = global.entrys;
        var d_ = {}
        if (typeof(global.defines) === 'undefined') global.defines = {};
        d_ = global.defines;
        var f_ = {}
        if (typeof(global.modules) === 'undefined') global.modules = {};
        f_ = global.modules || {};
        var p_ = {}
        __WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
        __WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
        __WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
        var z = __WXML_GLOBAL__.ops_set.$gwx5_XC_7 || [];

        function gz$gwx5_XC_7_1() {
            if (__WXML_GLOBAL__.ops_cached.$gwx5_XC_7_1) return __WXML_GLOBAL__.ops_cached.$gwx5_XC_7_1
            __WXML_GLOBAL__.ops_cached.$gwx5_XC_7_1 = [];
            (function(z) {
                var a = 11;

                function Z(ops) {
                    z.push(ops)
                }
                Z([3, 'page'])
                Z([
                    [2, '||'],
                    [
                        [7],
                        [3, 'themeColor']
                    ],
                    [1, '#fff']
                ])
                Z([3, '__l'])
                Z([3, '__e'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^onRightActionClick']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'sharePage']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([
                    [2, '?:'],
                    [
                        [7],
                        [3, 'showFilter']
                    ],
                    [1, 'xk-share'],
                    [1, '']
                ])
                Z([
                    [2, '||'],
                    [
                        [7],
                        [3, 'pageTitle']
                    ],
                    [1, '选择题库']
                ])
                Z([3, '48f2f0a6-1'])
                Z([
                    [4],
                    [
                        [5],
                        [1, 'default']
                    ]
                ])
                Z([
                    [7],
                    [3, 'showFilter']
                ])
                Z([3, 'product-filter'])
                Z([3, 'bottom'])
                Z(z[3])
                Z([3, 'product-filter-type left-1px'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, 'tap']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [
                                                    [5],
                                                    [1, 'changeListType']
                                                ],
                                                [
                                                    [4],
                                                    [
                                                        [5],
                                                        [1, '$event']
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([
                    [2, '=='],
                    [
                        [7],
                        [3, 'listType']
                    ],
                    [1, 'list']
                ])
                Z([
                    [2, '=='],
                    [
                        [7],
                        [3, 'listType']
                    ],
                    [1, 'card']
                ])
                Z([
                    [7],
                    [3, 'dataHasBeenLoaded']
                ])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [5],
                            [1, 'page-content']
                        ],
                        [
                            [2, '?:'],
                            [
                                [2, '!'],
                                [
                                    [7],
                                    [3, 'showFilter']
                                ]
                            ],
                            [1, 'notop'],
                            [1, '']
                        ]
                    ]
                ])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'g0']
                ])
                Z([3, 'index'])
                Z([3, 'item'])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'l1']
                ])
                Z(z[20])
                Z(z[3])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [5],
                            [1, 'product']
                        ],
                        [
                            [2, '+'],
                            [1, 'style-'],
                            [
                                [7],
                                [3, 'listThumbStyle']
                            ]
                        ]
                    ]
                ])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, 'tap']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [
                                                    [5],
                                                    [
                                                        [5],
                                                        [1, 'selectQbank']
                                                    ],
                                                    [
                                                        [4],
                                                        [
                                                            [5],
                                                            [1, '$0']
                                                        ]
                                                    ]
                                                ],
                                                [
                                                    [4],
                                                    [
                                                        [5],
                                                        [
                                                            [4],
                                                            [
                                                                [5],
                                                                [
                                                                    [4],
                                                                    [
                                                                        [5],
                                                                        [
                                                                            [5],
                                                                            [
                                                                                [5],
                                                                                [1, 'products']
                                                                            ],
                                                                            [1, '']
                                                                        ],
                                                                        [
                                                                            [7],
                                                                            [3, 'index']
                                                                        ]
                                                                    ]
                                                                ]
                                                            ]
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([
                    [2, '||'],
                    [
                        [2, '=='],
                        [
                            [7],
                            [3, 'listThumbStyle']
                        ],
                        [1, 1]
                    ],
                    [
                        [2, '=='],
                        [
                            [7],
                            [3, 'listThumbStyle']
                        ],
                        [1, 2]
                    ]
                ])
                Z([3, 'product-content-left pull-left'])
                Z([
                    [2, '=='],
                    [
                        [7],
                        [3, 'listThumbStyle']
                    ],
                    [1, 1]
                ])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'item']
                    ],
                    [3, 'g1']
                ])
                Z([
                    [2, '=='],
                    [
                        [7],
                        [3, 'listThumbStyle']
                    ],
                    [1, 2]
                ])
                Z([3, 'product-price'])
                Z([
                    [2, '!'],
                    [
                        [2, '&&'],
                        [
                            [2, '&&'],
                            [
                                [6],
                                [
                                    [6],
                                    [
                                        [7],
                                        [3, 'item']
                                    ],
                                    [3, '$orig']
                                ],
                                [3, 'price']
                            ],
                            [
                                [6],
                                [
                                    [7],
                                    [3, 'adminConfig']
                                ],
                                [3, 'qbankSettings']
                            ]
                        ],
                        [
                            [2, '=='],
                            [
                                [6],
                                [
                                    [6],
                                    [
                                        [7],
                                        [3, 'adminConfig']
                                    ],
                                    [3, 'qbankSettings']
                                ],
                                [3, 'qBankListPriceShow']
                            ],
                            [1, 0]
                        ]
                    ]
                ])
                Z([
                    [6],
                    [
                        [6],
                        [
                            [7],
                            [3, 'item']
                        ],
                        [3, '$orig']
                    ],
                    [3, 'marketPrice']
                ])
                Z([
                    [2, '&&'],
                    [
                        [6],
                        [
                            [6],
                            [
                                [7],
                                [3, 'item']
                            ],
                            [3, '$orig']
                        ],
                        [3, 'showMember']
                    ],
                    [
                        [6],
                        [
                            [6],
                            [
                                [7],
                                [3, 'item']
                            ],
                            [3, '$orig']
                        ],
                        [3, 'sumMember']
                    ]
                ])
                Z(z[2])
                Z([3, '该分类下暂无题库'])
                Z([3, 'empty3'])
                Z([3, '48f2f0a6-2'])
                Z(z[2])
                Z([
                    [2, '!'],
                    [
                        [7],
                        [3, 'loadMoreStatus']
                    ]
                ])
                Z([
                    [7],
                    [3, 'loadMoreStatus']
                ])
                Z([3, '48f2f0a6-3'])
                Z(z[2])
                Z([3, 'vue-ref'])
                Z([3, 'shareMenu'])
                Z([
                    [7],
                    [3, 'shareInfo']
                ])
                Z([3, '48f2f0a6-4'])
            })(__WXML_GLOBAL__.ops_cached.$gwx5_XC_7_1);
            return __WXML_GLOBAL__.ops_cached.$gwx5_XC_7_1
        }
        __WXML_GLOBAL__.ops_set.$gwx5_XC_7 = z;
        __WXML_GLOBAL__.ops_init.$gwx5_XC_7 = true;
        var x = ['./pages/qbank/list.wxml'];
        d_[x[0]] = {}
        var m0 = function(e, s, r, gg) {
            var z = gz$gwx5_XC_7_1()
            var c7E = _n('view')
            _rz(z, c7E, 'class', 0, e, s, gg)
            var l9E = _mz(z, 'nav-bar', ['bgColor', 1, 'bind:__l', 1, 'bind:onRightActionClick', 2, 'data-event-opts', 3, 'rightIconClass', 4, 'title', 5, 'vueId', 6, 'vueSlots', 7], [], e, s, gg)
            var a0E = _v()
            _(l9E, a0E)
            if (_oz(z, 9, e, s, gg)) {
                a0E.wxVkey = 1
                var tAF = _mz(z, 'view', ['class', 10, 'slot', 1], [], e, s, gg)
                var eBF = _mz(z, 'view', ['bindtap', 12, 'class', 1, 'data-event-opts', 2], [], e, s, gg)
                var bCF = _v()
                _(eBF, bCF)
                if (_oz(z, 15, e, s, gg)) {
                    bCF.wxVkey = 1
                } else {
                    bCF.wxVkey = 2
                    var oDF = _v()
                    _(bCF, oDF)
                    if (_oz(z, 16, e, s, gg)) {
                        oDF.wxVkey = 1
                    }
                    oDF.wxXCkey = 1
                }
                bCF.wxXCkey = 1
                _(tAF, eBF)
                _(a0E, tAF)
            }
            a0E.wxXCkey = 1
            _(c7E, l9E)
            var o8E = _v()
            _(c7E, o8E)
            if (_oz(z, 17, e, s, gg)) {
                o8E.wxVkey = 1
                var xEF = _n('view')
                _rz(z, xEF, 'class', 18, e, s, gg)
                var oFF = _v()
                _(xEF, oFF)
                if (_oz(z, 19, e, s, gg)) {
                    oFF.wxVkey = 1
                    var fGF = _v()
                    _(oFF, fGF)
                    var cHF = function(oJF, hIF, cKF, gg) {
                        var lMF = _mz(z, 'view', ['bindtap', 24, 'class', 1, 'data-event-opts', 2], [], oJF, hIF, gg)
                        var aNF = _v()
                        _(lMF, aNF)
                        if (_oz(z, 27, oJF, hIF, gg)) {
                            aNF.wxVkey = 1
                            var tOF = _n('view')
                            _rz(z, tOF, 'class', 28, oJF, hIF, gg)
                            var ePF = _v()
                            _(tOF, ePF)
                            if (_oz(z, 29, oJF, hIF, gg)) {
                                ePF.wxVkey = 1
                                var bQF = _v()
                                _(ePF, bQF)
                                if (_oz(z, 30, oJF, hIF, gg)) {
                                    bQF.wxVkey = 1
                                }
                                bQF.wxXCkey = 1
                            } else {
                                ePF.wxVkey = 2
                                var oRF = _v()
                                _(ePF, oRF)
                                if (_oz(z, 31, oJF, hIF, gg)) {
                                    oRF.wxVkey = 1
                                }
                                oRF.wxXCkey = 1
                            }
                            ePF.wxXCkey = 1
                            _(aNF, tOF)
                        }
                        var xSF = _n('view')
                        _rz(z, xSF, 'class', 32, oJF, hIF, gg)
                        var oTF = _v()
                        _(xSF, oTF)
                        if (_oz(z, 33, oJF, hIF, gg)) {
                            oTF.wxVkey = 1
                            var cVF = _v()
                            _(oTF, cVF)
                            if (_oz(z, 34, oJF, hIF, gg)) {
                                cVF.wxVkey = 1
                            }
                            cVF.wxXCkey = 1
                        }
                        var fUF = _v()
                        _(xSF, fUF)
                        if (_oz(z, 35, oJF, hIF, gg)) {
                            fUF.wxVkey = 1
                        }
                        oTF.wxXCkey = 1
                        fUF.wxXCkey = 1
                        _(lMF, xSF)
                        aNF.wxXCkey = 1
                        _(cKF, lMF)
                        return cKF
                    }
                    fGF.wxXCkey = 2
                    _2z(z, 22, cHF, e, s, gg, fGF, 'item', 'index', 'index')
                } else {
                    oFF.wxVkey = 2
                    var hWF = _mz(z, 'xk-empty', ['bind:__l', 36, 'text', 1, 'type', 2, 'vueId', 3], [], e, s, gg)
                    _(oFF, hWF)
                }
                var oXF = _mz(z, 'xk-load-more', ['bind:__l', 40, 'data-custom-hidden', 1, 'status', 2, 'vueId', 3], [], e, s, gg)
                _(xEF, oXF)
                oFF.wxXCkey = 1
                oFF.wxXCkey = 3
                _(o8E, xEF)
            }
            var cYF = _mz(z, 'xk-share', ['bind:__l', 44, 'class', 1, 'data-ref', 2, 'shareInfo', 3, 'vueId', 4], [], e, s, gg)
            _(c7E, cYF)
            o8E.wxXCkey = 1
            o8E.wxXCkey = 3
            _(r, c7E)
            return r
        }
        e_[x[0]] = {
            f: m0,
            j: [],
            i: [],
            ti: [],
            ic: []
        }
        if (path && e_[path]) {
            return function(env, dd, global) {
                $gwxc = 0;
                var root = {
                    "tag": "wx-page"
                };
                root.children = [];
                g = "$gwx5_XC_7";
                var main = e_[path].f
                if (typeof global === "undefined") global = {};
                global.f = $gdc(f_[path], "", 1);
                try {
                    main(env, {}, root, global);
                    _tsd(root)
                } catch (err) {
                    console.log(err)
                };
                g = "";
                return root;
            }
        }
    }
}(__g.a, __g.b, __g.c, __g.d, __g.e, __g.f, __g.g, __g.h, __g.i, __g.j, __g.k, __g.l, __g.m, __g.n, __g.o, __g.p, __g.q, __g.r, __g.s, __g.t, __g.u, __g.v, __g.w, __g.x, __g.y, __g.z, __g.A, __g.B, __g.C, __g.D, __g.E, __g.F, __g.G, __g.H, __g.I, __g.J, __g.K, __g.L, __g.M, __g.N, __g.O, __g.P, __g.Q, __g.R, __g.S, __g.T, __g.U, __g.V, __g.W, __g.X, __g.Y, __g.Z, __g.aa);
if (__vd_version_info__.delayedGwx || false) $gwx5_XC_7();
if (__vd_version_info__.delayedGwx) __wxAppCode__['pages/qbank/list.wxml'] = [$gwx5_XC_7, './pages/qbank/list.wxml'];
else __wxAppCode__['pages/qbank/list.wxml'] = $gwx5_XC_7('./pages/qbank/list.wxml');;
__wxRoute = "pages/qbank/list";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/qbank/list.js";
define("pages/qbank/list.js", function(require, module, exports, window, document, frames, self, location, navigator, localStorage, history, Caches, screen, alert, confirm, prompt, XMLHttpRequest, WebSocket, Reporter, webkit, WeixinJSCore) {
    (global.webpackJsonp = global.webpackJsonp || []).push([
        ["pages/qbank/list"], {
            "2fda": function(t, e, i) {
                "use strict";
                var r = i("e3e4");
                i.n(r).a
            },
            3308: function(t, e, i) {
                "use strict";
                i.r(e);
                var r = i("dba3"),
                    n = i("355e");
                for (var a in n)["default"].indexOf(a) < 0 && function(t) {
                    i.d(e, t, (function() {
                        return n[t]
                    }))
                }(a);
                i("2fda");
                var s = i("828b"),
                    c = Object(s.a)(n.default, r.b, r.c, !1, null, null, null, !1, r.a, void 0);
                e.default = c.exports
            },
            "355e": function(t, e, i) {
                "use strict";
                i.r(e);
                var r = i("6541"),
                    n = i.n(r);
                for (var a in r)["default"].indexOf(a) < 0 && function(t) {
                    i.d(e, t, (function() {
                        return r[t]
                    }))
                }(a);
                e.default = n.a
            },
            6541: function(t, e, i) {
                "use strict";
                (function(t) {
                    var r = i("47a9");
                    Object.defineProperty(e, "__esModule", {
                        value: !0
                    }), e.default = void 0;
                    var n = r(i("7eb4")),
                        a = r(i("ee10")),
                        s = r(i("7ca3")),
                        c = r(i("e6fb")),
                        o = i("2eff"),
                        u = i("88d2");

                    function l(t, e) {
                        var i = Object.keys(t);
                        if (Object.getOwnPropertySymbols) {
                            var r = Object.getOwnPropertySymbols(t);
                            e && (r = r.filter((function(e) {
                                return Object.getOwnPropertyDescriptor(t, e).enumerable
                            }))), i.push.apply(i, r)
                        }
                        return i
                    }

                    function d(t) {
                        for (var e = 1; e < arguments.length; e++) {
                            var i = null != arguments[e] ? arguments[e] : {};
                            e % 2 ? l(Object(i), !0).forEach((function(e) {
                                (0, s.default)(t, e, i[e])
                            })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(i)) : l(Object(i)).forEach((function(e) {
                                Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(i, e))
                            }))
                        }
                        return t
                    }
                    var f = {
                        components: {
                            xkLoadMore: function() {
                                i.e("components/common/load-more").then(function() {
                                    return resolve(i("3dc2"))
                                }.bind(null, i)).catch(i.oe)
                            },
                            xkShare: function() {
                                Promise.all([i.e("common/vendor"), i.e("components/share/share")]).then(function() {
                                    return resolve(i("d066"))
                                }.bind(null, i)).catch(i.oe)
                            }
                        },
                        data: function() {
                            return {
                                adminConfig: {},
                                pageTitle: "",
                                dataHasBeenLoaded: !1,
                                loadMoreStatus: 0,
                                filterList: [{
                                    text: "综合排序",
                                    type: "default"
                                }],
                                priceOrder: "",
                                activeFilter: 0,
                                listType: "list",
                                cid: 0,
                                keyword: "",
                                products: [],
                                count: 0,
                                pageSize: this.$isIpad ? 30 : 12,
                                currentPage: 1,
                                flag: !1,
                                showFilter: !0
                            }
                        },
                        computed: {
                            shareInfo: function() {
                                var t = "https://js.cdn.ixunke.com/v3/app/imgcdn/share/tk300.png",
                                    e = {
                                        title: "{pageTitle}题库，刷题神器一次考过！",
                                        imageUrl: t
                                    };
                                this.adminConfig.qbankSettings && this.adminConfig.qbankSettings.qbankListShareInfo && (e = d(d({}, e), this.adminConfig.qbankSettings.qbankListShareInfo)), e.imageUrl && e.imageUrl != t && (e.imageUrl = this.formatImageUrl(e.imageUrl, 750, 600, 1));
                                try {
                                    e.title = e.title.replace(/{pageTitle}/g, this.pageTitle || "精选")
                                } catch (t) {}
                                return {
                                    title: e.title,
                                    summary: "名师精选，覆盖重难点，答案解析清晰，刷题轻松考过！",
                                    link: "/pages/qbank/list?cid=" + this.cid,
                                    imageUrl: e.imageUrl
                                }
                            },
                            listThumbStyle: function() {
                                return this.adminConfig.listThumbStyle
                            }
                        },
                        onLoad: function(t) {
                            t.from && (this.pageFrom = t.from, this.listType = "card"), this.goDetail = t.goDetail, this.cid = 0 == t.cid ? 0 : t.cid, this.keyword = t.keyword || 0, t.title && (this.pageTitle = t.title), t.idArr && (this.idArr = JSON.stringify(t.idArr.split("-")), this.showFilter = !1), this.getAdminConfig(), this.getProducts()
                        },
                        onReady: function() {
                            var t = this;
                            this.cid ? (0, c.default)({
                                method: "GET",
                                path: "/api/category",
                                params: {
                                    id: this.cid
                                },
                                checkToken: 0
                            }).then((function(e) {
                                0 === e.errno && e.data.length && (t.pageTitle = e.data[0].category)
                            })) : this.keyword && (this.pageTitle = "搜索:" + this.keyword)
                        },
                        onShow: function() {},
                        onShareAppMessage: function() {
                            return {
                                title: this.shareInfo.title,
                                path: this.shareInfo.link,
                                imageUrl: this.shareInfo.imageUrl
                            }
                        },
                        onReachBottom: function() {
                            this.flag && this.getProducts(this.currentPage + 1)
                        },
                        methods: {
                            sharePage: function() {
                                this.$refs.shareMenu.open()
                            },
                            getAdminConfig: function() {
                                var t = this;
                                (0, o._getAdminConfig)().then((function(e) {
                                    t.adminConfig = e, t.adminConfig.paySwitch ? t.filterList = [{
                                        text: "综合排序",
                                        type: "default"
                                    }, {
                                        text: "销量优先",
                                        type: "member"
                                    }, {
                                        text: "最新题库",
                                        type: "publishTime"
                                    }, {
                                        text: "价格",
                                        type: "price"
                                    }] : t.filterList = [{
                                        text: "综合排序",
                                        type: "default"
                                    }, {
                                        text: "最新题库",
                                        type: "publishTime"
                                    }]
                                }))
                            },
                            changeFilter: function(t) {
                                "price" == this.filterList[t].type ? (this.activeFilter = t, "desc" == this.priceOrder ? this.priceOrder = "asc" : this.priceOrder = "desc", this.changeFilterSuccess()) : (this.priceOrder = "", this.activeFilter != t && (this.activeFilter = t, this.changeFilterSuccess()))
                            },
                            changeFilterSuccess: function() {
                                this.dataHasBeenLoaded = !1, this.products = [], this.getProducts()
                            },
                            changeListType: function() {
                                "card" == this.listType ? this.listType = "list" : "list" == this.listType && (this.listType = "card")
                            },
                            getProducts: function(e) {
                                var i = this;
                                return (0, a.default)(n.default.mark((function r() {
                                    var a, s;
                                    return n.default.wrap((function(r) {
                                        for (;;) switch (r.prev = r.next) {
                                            case 0:
                                                return e && 1 != e || i.$api.showLoading(), i.loadMoreStatus = 2, a = {
                                                    page: e || 1,
                                                    pageSize: i.pageSize,
                                                    status: 1
                                                }, r.next = 5, t.getSystemInfoSync().platform;
                                            case 5:
                                                return a.systemType = r.sent, i.cid ? a.categoryId = i.cid : i.keyword && (a.keyword = i.keyword), "member" == i.filterList[i.activeFilter].type ? a.order = "-sumMember" : "price" == i.filterList[i.activeFilter].type ? "desc" == i.priceOrder ? a.order = "-price" : "asc" == i.priceOrder && (a.order = encodeURIComponent("+price")) : "publishTime" == i.filterList[i.activeFilter].type ? a.order = "-publishTime" : a.order = "-recommend", i.idArr && (delete a.order, a.idArr = i.idArr), r.next = 11, (0, u._getQbankList)(a);
                                            case 11:
                                                s = r.sent, i.$api.hideLoading(), i.dataHasBeenLoaded = !0, 0 === s.errno && (i.count = s.count, i.currentPage = s.currentPage, s.data.forEach((function(t) {
                                                    if (t.activity && t.activity.discount) {
                                                        var e = +new Date,
                                                            i = t.activity.discount.startTime,
                                                            r = t.activity.discount.endTime;
                                                        if (e > i && e < r) {
                                                            var n = t.activity.discount.discount,
                                                                a = t.activity.discount.reduce,
                                                                s = t.price;
                                                            t.marketPrice = t.price, n ? s = s * n / 10 : a && (s = Math.max(s - a, 0)), t.price = s
                                                        }
                                                    }
                                                })), i.products = i.products.concat(s.data), s.currentPage >= s.totalPages ? (i.flag = !1, s.count <= 5 ? i.loadMoreStatus = 0 : i.loadMoreStatus = 3) : (i.flag = !0, i.loadMoreStatus = 1));
                                            case 15:
                                            case "end":
                                                return r.stop()
                                        }
                                    }), r)
                                })))()
                            },
                            selectQbank: function(e) {
                                if ("im" != this.pageFrom) {
                                    if ("community" == this.pageFrom) {
                                        var i = {
                                            id: e.id,
                                            thumb: e.originPicture,
                                            title: e.title,
                                            price: e.price,
                                            type: "q_bank"
                                        };
                                        return this.$api.sendEvent({
                                            name: "selectProductForCreatePost",
                                            extra: i
                                        }), void t.navigateBack()
                                    }
                                    1 == this.goDetail ? this.$api.openWin({
                                        url: "/pages/qbank/detail",
                                        params: {
                                            id: e.id
                                        }
                                    }) : this.$api.openWin({
                                        type: "reLaunch",
                                        url: "/pages/index",
                                        params: {
                                            tabType: "qbank",
                                            qbankId: e.id
                                        }
                                    })
                                }
                            },
                            formatThePrice: function() {
                                return this.$api.formatThePrice(arguments[0], arguments[1])
                            },
                            productTags: function(t) {
                                var e = [];
                                if (t.activity) {
                                    if (t.activity.joinbuy) {
                                        var i = +new Date,
                                            r = t.activity.joinbuy.startTime,
                                            n = t.activity.joinbuy.endTime;
                                        i < r ? e.push({
                                            type: "tuan",
                                            text: "即将开团"
                                        }) : i < n && e.push({
                                            type: "tuan",
                                            text: "拼团中"
                                        })
                                    }
                                    if (t.activity.discount) {
                                        var a = t.activity.discount,
                                            s = +new Date,
                                            c = (a.startTime, a.endTime);
                                        if (c && s < c)
                                            if (a.skus) {
                                                var o = JSON.parse(JSON.stringify(a.skus));
                                                (o = o.sort((function(t, e) {
                                                    return t.price - e.price
                                                })).filter((function(t) {
                                                    return t.discount || t.reduce
                                                }))).length ? o[0].discount ? e.push({
                                                    type: "discount",
                                                    text: "限时折扣"
                                                }) : o[0].reduce && e.push({
                                                    type: "discount",
                                                    text: "限时立减"
                                                }) : a.discount ? e.push({
                                                    type: "discount",
                                                    text: "限时折扣"
                                                }) : a.reduce && e.push({
                                                    type: "discount",
                                                    text: "限时立减"
                                                })
                                            } else a.discount ? e.push({
                                                type: "discount",
                                                text: "限时折扣"
                                            }) : a.reduce && e.push({
                                                type: "discount",
                                                text: "限时立减"
                                            })
                                    }
                                }
                                return e
                            },
                            formatImageUrl: function(t, e, i, r) {
                                return this.$api.formatImageUrl(t, e, i, r)
                            }
                        }
                    };
                    e.default = f
                }).call(this, i("df3c").default)
            },
            dba3: function(t, e, i) {
                "use strict";
                i.d(e, "b", (function() {
                    return r
                })), i.d(e, "c", (function() {
                    return n
                })), i.d(e, "a", (function() {}));
                var r = function() {
                        var t = this,
                            e = (t.$createElement, t._self._c, t.dataHasBeenLoaded ? t.products.length : null),
                            i = t.dataHasBeenLoaded && e ? t.__map(t.products, (function(e, i) {
                                var r = t.__get_orig(e),
                                    n = 1 != t.listThumbStyle && 2 != t.listThumbStyle || 1 != t.listThumbStyle ? null : t.formatImageUrl(e.originPicture, 800, 450, 1),
                                    a = 1 != t.listThumbStyle && 2 != t.listThumbStyle || 1 != t.listThumbStyle ? null : t.productTags(e).length;
                                return {
                                    $orig: r,
                                    m0: n,
                                    g1: a,
                                    l0: 1 != t.listThumbStyle && 2 != t.listThumbStyle || 1 != t.listThumbStyle || !a ? null : t.productTags(e),
                                    m1: 1 != t.listThumbStyle && 2 != t.listThumbStyle || 1 == t.listThumbStyle || 2 != t.listThumbStyle ? null : t.formatImageUrl(e.smallPicture || e.originPicture, 480, 480, 1),
                                    m2: e.price && t.adminConfig.qbankSettings && 0 == t.adminConfig.qbankSettings.qBankListPriceShow ? null : t.formatThePrice(e.price),
                                    m3: e.price && t.adminConfig.qbankSettings && 0 == t.adminConfig.qbankSettings.qBankListPriceShow || !e.marketPrice ? null : t.formatThePrice(e.marketPrice, 3)
                                }
                            })) : null;
                        t.$mp.data = Object.assign({}, {
                            $root: {
                                g0: e,
                                l1: i
                            }
                        })
                    },
                    n = []
            },
            e3e4: function(t, e, i) {},
            f392: function(t, e, i) {
                "use strict";
                (function(t, e) {
                    var r = i("47a9");
                    i("8f74"), r(i("3240"));
                    var n = r(i("3308"));
                    t.__webpack_require_UNI_MP_PLUGIN__ = i, e(n.default)
                }).call(this, i("3223").default, i("df3c").createPage)
            }
        },
        [
            ["f392", "common/runtime", "common/vendor"]
        ]
    ]);
}, {
    isPage: true,
    isComponent: true,
    currentFile: 'pages/qbank/list.js'
});
require("pages/qbank/list.js");
$gwx5_XC_8 = function(_, _v, _n, _p, _s, _wp, _wl, $gwn, $gwl, $gwh, wh, $gstack, $gwrt, gra, grb, TestTest, wfor, _ca, _da, _r, _rz, _o, _oz, _1, _1z, _2, _2z, _m, _mz, nv_getDate, nv_getRegExp, nv_console, nv_parseInt, nv_parseFloat, nv_isNaN, nv_isFinite, nv_decodeURI, nv_decodeURIComponent, nv_encodeURI, nv_encodeURIComponent, $gdc, nv_JSON, _af, _gv, _ai, _grp, _gd, _gapi, $ixc, _ic, _w, _ev, _tsd) {
    return function(path, global) {
        if (typeof global === 'undefined') {
            if (typeof __GWX_GLOBAL__ === 'undefined') global = {};
            else global = __GWX_GLOBAL__;
        }
        if (typeof __WXML_GLOBAL__ === 'undefined') {
            __WXML_GLOBAL__ = {};
        }
        __WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
        var e_ = {}
        if (typeof(global.entrys) === 'undefined') global.entrys = {};
        e_ = global.entrys;
        var d_ = {}
        if (typeof(global.defines) === 'undefined') global.defines = {};
        d_ = global.defines;
        var f_ = {}
        if (typeof(global.modules) === 'undefined') global.modules = {};
        f_ = global.modules || {};
        var p_ = {}
        __WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
        __WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
        __WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
        var z = __WXML_GLOBAL__.ops_set.$gwx5_XC_8 || [];

        function gz$gwx5_XC_8_1() {
            if (__WXML_GLOBAL__.ops_cached.$gwx5_XC_8_1) return __WXML_GLOBAL__.ops_cached.$gwx5_XC_8_1
            __WXML_GLOBAL__.ops_cached.$gwx5_XC_8_1 = [];
            (function(z) {
                var a = 11;

                function Z(ops) {
                    z.push(ops)
                }
                Z([3, 'data-v-e05fa786'])
                Z([3, '__l'])
                Z(z[0])
                Z([3, '我的笔记'])
                Z([3, '90d5fa60-1'])
                Z([
                    [7],
                    [3, 'dataHasBeenLoaded']
                ])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'g0']
                ])
                Z([3, 'note-list data-v-e05fa786'])
                Z([3, 'index'])
                Z([3, 'item'])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'l0']
                ])
                Z(z[8])
                Z([3, 'comment-main data-v-e05fa786'])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'item']
                    ],
                    [3, 'g1']
                ])
                Z([3, 'checking data-v-e05fa786'])
                Z([
                    [2, '+'],
                    [
                        [2, '+'],
                        [
                            [2, '+'],
                            [1, 'border-color:'],
                            [
                                [7],
                                [3, 'singleColor']
                            ]
                        ],
                        [1, ';']
                    ],
                    [
                        [2, '+'],
                        [
                            [2, '+'],
                            [1, 'color:'],
                            [
                                [7],
                                [3, 'singleColor']
                            ]
                        ],
                        [1, ';']
                    ]
                ])
                Z([
                    [2, '=='],
                    [
                        [6],
                        [
                            [6],
                            [
                                [7],
                                [3, 'item']
                            ],
                            [3, '$orig']
                        ],
                        [3, 'type']
                    ],
                    [1, 1]
                ])
                Z([
                    [7],
                    [3, 'noteAlias']
                ])
                Z(z[1])
                Z(z[0])
                Z([
                    [2, '!'],
                    [
                        [7],
                        [3, 'loadMoreState']
                    ]
                ])
                Z([
                    [7],
                    [3, 'loadMoreState']
                ])
                Z([3, '90d5fa60-2'])
                Z(z[1])
                Z(z[0])
                Z([3, '暂无笔记'])
                Z([3, 'empty2'])
                Z([3, '90d5fa60-3'])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'g2']
                ])
            })(__WXML_GLOBAL__.ops_cached.$gwx5_XC_8_1);
            return __WXML_GLOBAL__.ops_cached.$gwx5_XC_8_1
        }
        __WXML_GLOBAL__.ops_set.$gwx5_XC_8 = z;
        __WXML_GLOBAL__.ops_init.$gwx5_XC_8 = true;
        var x = ['./pages/qbank/notes.wxml'];
        d_[x[0]] = {}
        var m0 = function(e, s, r, gg) {
            var z = gz$gwx5_XC_8_1()
            var l1F = _n('view')
            _rz(z, l1F, 'class', 0, e, s, gg)
            var t3F = _mz(z, 'nav-bar', ['bind:__l', 1, 'class', 1, 'title', 2, 'vueId', 3], [], e, s, gg)
            _(l1F, t3F)
            var a2F = _v()
            _(l1F, a2F)
            if (_oz(z, 5, e, s, gg)) {
                a2F.wxVkey = 1
                var e4F = _v()
                _(a2F, e4F)
                if (_oz(z, 6, e, s, gg)) {
                    e4F.wxVkey = 1
                    var o6F = _n('view')
                    _rz(z, o6F, 'class', 7, e, s, gg)
                    var x7F = _v()
                    _(o6F, x7F)
                    var o8F = function(c0F, f9F, hAG, gg) {
                        var cCG = _n('view')
                        _rz(z, cCG, 'class', 12, c0F, f9F, gg)
                        var oDG = _v()
                        _(cCG, oDG)
                        if (_oz(z, 13, c0F, f9F, gg)) {
                            oDG.wxVkey = 1
                        }
                        var lEG = _mz(z, 'view', ['class', 14, 'style', 1], [], c0F, f9F, gg)
                        var aFG = _v()
                        _(lEG, aFG)
                        if (_oz(z, 16, c0F, f9F, gg)) {
                            aFG.wxVkey = 1
                        } else {
                            aFG.wxVkey = 2
                            var tGG = _v()
                            _(aFG, tGG)
                            if (_oz(z, 17, c0F, f9F, gg)) {
                                tGG.wxVkey = 1
                            }
                            tGG.wxXCkey = 1
                        }
                        aFG.wxXCkey = 1
                        _(cCG, lEG)
                        oDG.wxXCkey = 1
                        _(hAG, cCG)
                        return hAG
                    }
                    x7F.wxXCkey = 2
                    _2z(z, 10, o8F, e, s, gg, x7F, 'item', 'index', 'index')
                    var eHG = _mz(z, 'xk-load-more', ['bind:__l', 18, 'class', 1, 'data-custom-hidden', 2, 'status', 3, 'vueId', 4], [], e, s, gg)
                    _(o6F, eHG)
                    _(e4F, o6F)
                } else {
                    e4F.wxVkey = 2
                    var bIG = _mz(z, 'xk-empty', ['bind:__l', 23, 'class', 1, 'text', 2, 'type', 3, 'vueId', 4], [], e, s, gg)
                    _(e4F, bIG)
                }
                var b5F = _v()
                _(a2F, b5F)
                if (_oz(z, 28, e, s, gg)) {
                    b5F.wxVkey = 1
                }
                e4F.wxXCkey = 1
                e4F.wxXCkey = 3
                e4F.wxXCkey = 3
                b5F.wxXCkey = 1
            }
            a2F.wxXCkey = 1
            a2F.wxXCkey = 3
            _(r, l1F)
            return r
        }
        e_[x[0]] = {
            f: m0,
            j: [],
            i: [],
            ti: [],
            ic: []
        }
        if (path && e_[path]) {
            return function(env, dd, global) {
                $gwxc = 0;
                var root = {
                    "tag": "wx-page"
                };
                root.children = [];
                g = "$gwx5_XC_8";
                var main = e_[path].f
                if (typeof global === "undefined") global = {};
                global.f = $gdc(f_[path], "", 1);
                try {
                    main(env, {}, root, global);
                    _tsd(root)
                } catch (err) {
                    console.log(err)
                };
                g = "";
                return root;
            }
        }
    }
}(__g.a, __g.b, __g.c, __g.d, __g.e, __g.f, __g.g, __g.h, __g.i, __g.j, __g.k, __g.l, __g.m, __g.n, __g.o, __g.p, __g.q, __g.r, __g.s, __g.t, __g.u, __g.v, __g.w, __g.x, __g.y, __g.z, __g.A, __g.B, __g.C, __g.D, __g.E, __g.F, __g.G, __g.H, __g.I, __g.J, __g.K, __g.L, __g.M, __g.N, __g.O, __g.P, __g.Q, __g.R, __g.S, __g.T, __g.U, __g.V, __g.W, __g.X, __g.Y, __g.Z, __g.aa);
if (__vd_version_info__.delayedGwx || false) $gwx5_XC_8();
if (__vd_version_info__.delayedGwx) __wxAppCode__['pages/qbank/notes.wxml'] = [$gwx5_XC_8, './pages/qbank/notes.wxml'];
else __wxAppCode__['pages/qbank/notes.wxml'] = $gwx5_XC_8('./pages/qbank/notes.wxml');;
__wxRoute = "pages/qbank/notes";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/qbank/notes.js";
define("pages/qbank/notes.js", function(require, module, exports, window, document, frames, self, location, navigator, localStorage, history, Caches, screen, alert, confirm, prompt, XMLHttpRequest, WebSocket, Reporter, webkit, WeixinJSCore) {
    (global.webpackJsonp = global.webpackJsonp || []).push([
        ["pages/qbank/notes"], {
            "08a8": function(t, e, a) {},
            2722: function(t, e, a) {
                "use strict";
                (function(t, e) {
                    var n = a("47a9");
                    a("8f74"), n(a("3240"));
                    var o = n(a("31ca"));
                    t.__webpack_require_UNI_MP_PLUGIN__ = a, e(o.default)
                }).call(this, a("3223").default, a("df3c").createPage)
            },
            "2e50": function(t, e, a) {
                "use strict";
                a.d(e, "b", (function() {
                    return n
                })), a.d(e, "c", (function() {
                    return o
                })), a.d(e, "a", (function() {}));
                var n = function() {
                        var t = this,
                            e = (t.$createElement, t._self._c, t.dataHasBeenLoaded ? t.list.length : null),
                            a = t.dataHasBeenLoaded && e ? t.__map(t.list, (function(e, a) {
                                return {
                                    $orig: t.__get_orig(e),
                                    m0: t.formatImageUrl(e.smallAvatar),
                                    m1: t.formatRichText(e.content),
                                    g1: e.picture && e.picture.length,
                                    f0: t._f("dateFormat")(e.createdTime)
                                }
                            })) : null,
                            n = t.dataHasBeenLoaded ? t.list.length : null;
                        t.$mp.data = Object.assign({}, {
                            $root: {
                                g0: e,
                                l0: a,
                                g2: n
                            }
                        })
                    },
                    o = []
            },
            "31ca": function(t, e, a) {
                "use strict";
                a.r(e);
                var n = a("2e50"),
                    o = a("e7ea");
                for (var r in o)["default"].indexOf(r) < 0 && function(t) {
                    a.d(e, t, (function() {
                        return o[t]
                    }))
                }(r);
                a("40aa"), a("a605");
                var i = a("828b"),
                    c = Object(i.a)(o.default, n.b, n.c, !1, null, "e05fa786", null, !1, n.a, void 0);
                e.default = c.exports
            },
            "40aa": function(t, e, a) {
                "use strict";
                var n = a("08a8");
                a.n(n).a
            },
            5847: function(t, e, a) {
                "use strict";
                var n = a("47a9");
                Object.defineProperty(e, "__esModule", {
                    value: !0
                }), e.default = void 0;
                var o = n(a("7ca3")),
                    r = a("0938");

                function i(t, e) {
                    var a = Object.keys(t);
                    if (Object.getOwnPropertySymbols) {
                        var n = Object.getOwnPropertySymbols(t);
                        e && (n = n.filter((function(e) {
                            return Object.getOwnPropertyDescriptor(t, e).enumerable
                        }))), a.push.apply(a, n)
                    }
                    return a
                }

                function c(t) {
                    for (var e = 1; e < arguments.length; e++) {
                        var a = null != arguments[e] ? arguments[e] : {};
                        e % 2 ? i(Object(a), !0).forEach((function(e) {
                            (0, o.default)(t, e, a[e])
                        })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(a)) : i(Object(a)).forEach((function(e) {
                            Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(a, e))
                        }))
                    }
                    return t
                }
                var u = {
                    components: {
                        xkLoadMore: function() {
                            a.e("components/common/load-more").then(function() {
                                return resolve(a("3dc2"))
                            }.bind(null, a)).catch(a.oe)
                        }
                    },
                    data: function() {
                        return {
                            dataHasBeenLoaded: !1,
                            list: [],
                            currentPage: 1,
                            loadMoreFlag: !0,
                            loadMoreState: 0,
                            count: 0
                        }
                    },
                    computed: {
                        noteAlias: function() {
                            try {
                                return this.$store.state.adminConfig.qbankSettings.noteAlias || "公开笔记"
                            } catch (t) {
                                return ""
                            }
                        }
                    },
                    onLoad: function(t) {
                        this.qBankId = t.qid - 0, this.chapterId = t.cid - 0, this.getList()
                    },
                    onReachBottom: function() {
                        this.loadMoreFlag && (this.currentPage++, this.getList())
                    },
                    filters: {
                        dateFormat: function(t, e) {
                            return (0, r.dateFormat)(t, e || "yyyy-MM-dd hh:mm")
                        }
                    },
                    methods: {
                        formatImageUrl: function(t, e, a, n) {
                            return this.$api.formatImageUrl(t, e, a, n)
                        },
                        getList: function() {
                            var t = this;
                            this.$api.showLoading(), this.loadMoreState = 2;
                            var e = {};
                            this.qBankId && (e.qBankId = this.qBankId), this.chapterId && (e.chapterId = this.chapterId), this.$http({
                                url: "/api/question_comment/reply",
                                data: c(c({}, e), {}, {
                                    myNoteList: !0,
                                    page: this.currentPage || 1,
                                    pageSize: 20
                                }),
                                token: 1
                            }).then((function(e) {
                                t.dataHasBeenLoaded = !0, t.$api.hideLoading(), t.loadMoreState = 0, 0 === e.errno && (t.count = e.count, t.list = t.list.concat(e.data), e.currentPage >= e.totalPages ? (t.loadMoreFlag = !1, e.count <= 5 ? t.loadMoreState = 0 : t.loadMoreState = 3) : (t.loadMoreFlag = !0, t.loadMoreState = 1))
                            }))
                        },
                        deleteNote: function() {},
                        goQuestion: function() {
                            if (console.log(this.count), 0 != this.count) {
                                var t = {
                                    type: "practice",
                                    subType: "note",
                                    qid: this.qBankId,
                                    pageSize: this.count,
                                    title: this.pageTitle
                                };
                                this.chapterId && (t.cid = this.chapterId), this.$api.openWin({
                                    url: "/pages/question/index",
                                    params: t
                                })
                            }
                        },
                        formatRichText: function(t) {
                            return t.replace(/↵|\n/g, "<br>")
                        }
                    }
                };
                e.default = u
            },
            a605: function(t, e, a) {
                "use strict";
                var n = a("ce0a");
                a.n(n).a
            },
            ce0a: function(t, e, a) {},
            e7ea: function(t, e, a) {
                "use strict";
                a.r(e);
                var n = a("5847"),
                    o = a.n(n);
                for (var r in n)["default"].indexOf(r) < 0 && function(t) {
                    a.d(e, t, (function() {
                        return n[t]
                    }))
                }(r);
                e.default = o.a
            }
        },
        [
            ["2722", "common/runtime", "common/vendor"]
        ]
    ]);
}, {
    isPage: true,
    isComponent: true,
    currentFile: 'pages/qbank/notes.js'
});
require("pages/qbank/notes.js");
$gwx5_XC_9 = function(_, _v, _n, _p, _s, _wp, _wl, $gwn, $gwl, $gwh, wh, $gstack, $gwrt, gra, grb, TestTest, wfor, _ca, _da, _r, _rz, _o, _oz, _1, _1z, _2, _2z, _m, _mz, nv_getDate, nv_getRegExp, nv_console, nv_parseInt, nv_parseFloat, nv_isNaN, nv_isFinite, nv_decodeURI, nv_decodeURIComponent, nv_encodeURI, nv_encodeURIComponent, $gdc, nv_JSON, _af, _gv, _ai, _grp, _gd, _gapi, $ixc, _ic, _w, _ev, _tsd) {
    return function(path, global) {
        if (typeof global === 'undefined') {
            if (typeof __GWX_GLOBAL__ === 'undefined') global = {};
            else global = __GWX_GLOBAL__;
        }
        if (typeof __WXML_GLOBAL__ === 'undefined') {
            __WXML_GLOBAL__ = {};
        }
        __WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
        var e_ = {}
        if (typeof(global.entrys) === 'undefined') global.entrys = {};
        e_ = global.entrys;
        var d_ = {}
        if (typeof(global.defines) === 'undefined') global.defines = {};
        d_ = global.defines;
        var f_ = {}
        if (typeof(global.modules) === 'undefined') global.modules = {};
        f_ = global.modules || {};
        var p_ = {}
        __WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
        __WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
        __WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
        var z = __WXML_GLOBAL__.ops_set.$gwx5_XC_9 || [];

        function gz$gwx5_XC_9_1() {
            if (__WXML_GLOBAL__.ops_cached.$gwx5_XC_9_1) return __WXML_GLOBAL__.ops_cached.$gwx5_XC_9_1
            __WXML_GLOBAL__.ops_cached.$gwx5_XC_9_1 = [];
            (function(z) {
                var a = 11;

                function Z(ops) {
                    z.push(ops)
                }
                Z([
                    [2, '+'],
                    [
                        [2, '+'],
                        [1, 'background:'],
                        [
                            [7],
                            [3, 'themeColor']
                        ]
                    ],
                    [1, ';']
                ])
                Z([
                    [7],
                    [3, 'themeColor']
                ])
                Z([3, '__l'])
                Z([3, '刷题排行榜'])
                Z([3, '********-1'])
                Z([
                    [7],
                    [3, 'pageBool']
                ])
                Z([3, 'list-box'])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'g3']
                ])
                Z([3, 'index'])
                Z([3, 'item'])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'l0']
                ])
                Z(z[8])
                Z([3, 'list-item'])
                Z([
                    [2, '>'],
                    [
                        [7],
                        [3, 'index']
                    ],
                    [1, 2]
                ])
                Z([
                    [2, '=='],
                    [
                        [7],
                        [3, 'index']
                    ],
                    [1, 0]
                ])
                Z([
                    [2, '=='],
                    [
                        [7],
                        [3, 'index']
                    ],
                    [1, 1]
                ])
                Z([
                    [2, '=='],
                    [
                        [7],
                        [3, 'index']
                    ],
                    [1, 2]
                ])
                Z([
                    [7],
                    [3, 'pointVisible']
                ])
                Z(z[2])
                Z([3, '排行榜暂无排名'])
                Z([3, '********-2'])
                Z(z[2])
                Z([3, '__e'])
                Z([3, 'vue-ref'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^success']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'loginSuccess']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'login'])
                Z([3, '********-3'])
            })(__WXML_GLOBAL__.ops_cached.$gwx5_XC_9_1);
            return __WXML_GLOBAL__.ops_cached.$gwx5_XC_9_1
        }
        __WXML_GLOBAL__.ops_set.$gwx5_XC_9 = z;
        __WXML_GLOBAL__.ops_init.$gwx5_XC_9 = true;
        var x = ['./pages/qbank/rank.wxml'];
        d_[x[0]] = {}
        var m0 = function(e, s, r, gg) {
            var z = gz$gwx5_XC_9_1()
            var xKG = _n('view')
            _rz(z, xKG, 'style', 0, e, s, gg)
            var fMG = _mz(z, 'nav-bar', ['bgColor', 1, 'bind:__l', 1, 'title', 2, 'vueId', 3], [], e, s, gg)
            _(xKG, fMG)
            var oLG = _v()
            _(xKG, oLG)
            if (_oz(z, 5, e, s, gg)) {
                oLG.wxVkey = 1
                var cNG = _n('view')
                _rz(z, cNG, 'class', 6, e, s, gg)
                var hOG = _v()
                _(cNG, hOG)
                if (_oz(z, 7, e, s, gg)) {
                    hOG.wxVkey = 1
                    var oPG = _v()
                    _(hOG, oPG)
                    var cQG = function(lSG, oRG, aTG, gg) {
                        var eVG = _n('view')
                        _rz(z, eVG, 'class', 12, lSG, oRG, gg)
                        var bWG = _v()
                        _(eVG, bWG)
                        if (_oz(z, 13, lSG, oRG, gg)) {
                            bWG.wxVkey = 1
                        } else {
                            bWG.wxVkey = 2
                            var xYG = _v()
                            _(bWG, xYG)
                            if (_oz(z, 14, lSG, oRG, gg)) {
                                xYG.wxVkey = 1
                            }
                            var oZG = _v()
                            _(bWG, oZG)
                            if (_oz(z, 15, lSG, oRG, gg)) {
                                oZG.wxVkey = 1
                            }
                            var f1G = _v()
                            _(bWG, f1G)
                            if (_oz(z, 16, lSG, oRG, gg)) {
                                f1G.wxVkey = 1
                            }
                            xYG.wxXCkey = 1
                            oZG.wxXCkey = 1
                            f1G.wxXCkey = 1
                        }
                        var oXG = _v()
                        _(eVG, oXG)
                        if (_oz(z, 17, lSG, oRG, gg)) {
                            oXG.wxVkey = 1
                        }
                        bWG.wxXCkey = 1
                        oXG.wxXCkey = 1
                        _(aTG, eVG)
                        return aTG
                    }
                    oPG.wxXCkey = 2
                    _2z(z, 10, cQG, e, s, gg, oPG, 'item', 'index', 'index')
                } else {
                    hOG.wxVkey = 2
                    var c2G = _mz(z, 'xk-empty-inline', ['bind:__l', 18, 'text', 1, 'vueId', 2], [], e, s, gg)
                    _(hOG, c2G)
                }
                hOG.wxXCkey = 1
                hOG.wxXCkey = 3
                _(oLG, cNG)
            }
            var h3G = _mz(z, 'xk-login', ['bind:__l', 21, 'bind:success', 1, 'class', 2, 'data-event-opts', 3, 'data-ref', 4, 'vueId', 5], [], e, s, gg)
            _(xKG, h3G)
            oLG.wxXCkey = 1
            oLG.wxXCkey = 3
            _(r, xKG)
            return r
        }
        e_[x[0]] = {
            f: m0,
            j: [],
            i: [],
            ti: [],
            ic: []
        }
        if (path && e_[path]) {
            return function(env, dd, global) {
                $gwxc = 0;
                var root = {
                    "tag": "wx-page"
                };
                root.children = [];
                g = "$gwx5_XC_9";
                var main = e_[path].f
                if (typeof global === "undefined") global = {};
                global.f = $gdc(f_[path], "", 1);
                try {
                    main(env, {}, root, global);
                    _tsd(root)
                } catch (err) {
                    console.log(err)
                };
                g = "";
                return root;
            }
        }
    }
}(__g.a, __g.b, __g.c, __g.d, __g.e, __g.f, __g.g, __g.h, __g.i, __g.j, __g.k, __g.l, __g.m, __g.n, __g.o, __g.p, __g.q, __g.r, __g.s, __g.t, __g.u, __g.v, __g.w, __g.x, __g.y, __g.z, __g.A, __g.B, __g.C, __g.D, __g.E, __g.F, __g.G, __g.H, __g.I, __g.J, __g.K, __g.L, __g.M, __g.N, __g.O, __g.P, __g.Q, __g.R, __g.S, __g.T, __g.U, __g.V, __g.W, __g.X, __g.Y, __g.Z, __g.aa);
if (__vd_version_info__.delayedGwx || false) $gwx5_XC_9();
if (__vd_version_info__.delayedGwx) __wxAppCode__['pages/qbank/rank.wxml'] = [$gwx5_XC_9, './pages/qbank/rank.wxml'];
else __wxAppCode__['pages/qbank/rank.wxml'] = $gwx5_XC_9('./pages/qbank/rank.wxml');;
__wxRoute = "pages/qbank/rank";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/qbank/rank.js";
define("pages/qbank/rank.js", function(require, module, exports, window, document, frames, self, location, navigator, localStorage, history, Caches, screen, alert, confirm, prompt, XMLHttpRequest, WebSocket, Reporter, webkit, WeixinJSCore) {
    (global.webpackJsonp = global.webpackJsonp || []).push([
        ["pages/qbank/rank"], {
            "1c33": function(n, t, a) {},
            "2c17": function(n, t, a) {
                "use strict";
                var e = a("1c33");
                a.n(e).a
            },
            "553d": function(n, t, a) {
                "use strict";
                a.d(t, "b", (function() {
                    return e
                })), a.d(t, "c", (function() {
                    return i
                })), a.d(t, "a", (function() {}));
                var e = function() {
                        var n = this,
                            t = (n.$createElement, n._self._c, n.pointVisible && "week" == n.rankInterval ? n.pointSwitch.addqBankRankPointRule.length || 0 : null),
                            a = n.pointVisible && "week" != n.rankInterval && "month" == n.rankInterval ? n.pointSwitch.addqBankRankPointRule.length || 0 : null,
                            e = n.pointVisible && "week" != n.rankInterval && "month" != n.rankInterval ? n.pointSwitch.addqBankRankPointRule.length || 0 : null,
                            i = n.pageBool ? n.rankList.length : null,
                            o = n.pageBool && i ? n.formatImageUrl(n.userInfo.smallAvatar, 200, 200, 1) || "https://js.cdn.ixunke.com/v3/app/imgcdn/user/avatar.jpg" : null,
                            r = n.pageBool && i ? n.__map(n.rankList, (function(t, a) {
                                return {
                                    $orig: n.__get_orig(t),
                                    m1: n.formatImageUrl(t.userInfo.smallAvatar, 200, 200, 1) || "https://js.cdn.ixunke.com/v3/app/imgcdn/user/avatar.jpg"
                                }
                            })) : null;
                        n.$mp.data = Object.assign({}, {
                            $root: {
                                g0: t,
                                g1: a,
                                g2: e,
                                g3: i,
                                m0: o,
                                l0: r
                            }
                        })
                    },
                    i = []
            },
            5616: function(n, t, a) {
                "use strict";
                (function(n) {
                    var e = a("47a9");
                    Object.defineProperty(t, "__esModule", {
                        value: !0
                    }), t.default = void 0;
                    var i = e(a("e6fb")),
                        o = a("0938"),
                        r = e(a("f462")),
                        c = a("62a4"),
                        s = {
                            data: function() {
                                return {
                                    pageBool: !1,
                                    userInfo: {},
                                    qBankId: 0,
                                    myRank: {
                                        rank: 0,
                                        weekRightCount: 0
                                    },
                                    pointSwitch: {},
                                    rankList: [],
                                    lastWeekRank: !1,
                                    rankInterval: "week"
                                }
                            },
                            onLoad: function(t) {
                                this.qBankId = t.qid || n.getStorageSync("qbankId"), this.getSwitch(), this.getRankList()
                            },
                            computed: {
                                pointVisible: function() {
                                    var n;
                                    return n = !("rankGoodsSet" in this.pointSwitch) || !("qbankRankIds" in this.pointSwitch) || "all" == this.pointSwitch.rankGoodsSet || !!this.pointSwitch.qbankRankIds.includes(this.qBankId), this.pointSwitch.switch && this.pointSwitch.addqBankRankSwitch && n
                                }
                            },
                            filters: {
                                dateFormat: function(n) {
                                    return (0, o.dateFormat)(n, "yyyy-MM-dd hh:mm")
                                }
                            },
                            methods: {
                                getToken: c.getToken,
                                getSwitch: function() {
                                    var n = this;
                                    (0, i.default)({
                                        method: "GET",
                                        path: "/api/config/point_switch",
                                        params: {},
                                        checkToken: 1
                                    }).then((function(t) {
                                        0 == t.errno && (n.pointSwitch = t.data.context)
                                    }))
                                },
                                getRankList: function() {
                                    var t = this;
                                    this.userInfo = n.getStorageSync("userInfo"), this.$api.showLoading("请稍候...");
                                    var a = {
                                        qBankId: this.qBankId
                                    };
                                    this.lastWeekRank && (a.lastWeekRank = !0), (0, i.default)({
                                        method: "GET",
                                        path: "/api/v1/qbank_member/qbank_learn_rank",
                                        params: a,
                                        checkToken: (0, c.getToken)() ? 1 : 0
                                    }).then((function(n) {
                                        t.$api.hideLoading(), t.pageBool = !0, 0 === n.errno ? (t.rankList = n.data.rankArr, n.data.rankInterval && (t.rankInterval = n.data.rankInterval), t.$set(t.myRank, "rank", n.data.rank), t.$set(t.myRank, "weekRightCount", n.data.weekRightCount)) : 100143 === n.errno && t.$nextTick((function() {
                                            t.goLogin("reload")
                                        }))
                                    }))
                                },
                                changeType: function(n) {
                                    this.lastWeekRank = n, this.getRankList()
                                },
                                goLogin: function(n) {
                                    var t = this;
                                    (0, r.default)({
                                        component: !0,
                                        openComponent: function(a) {
                                            a.reload && n ? t.getRankList() : t.$refs.login.show()
                                        },
                                        success: function() {
                                            t.loginSuccess()
                                        }
                                    })
                                },
                                loginSuccess: function() {
                                    this.getRankList()
                                },
                                formatImageUrl: function(n, t, a, e) {
                                    return this.$api.formatImageUrl(n, t, a, e)
                                }
                            }
                        };
                    t.default = s
                }).call(this, a("df3c").default)
            },
            "7ad3": function(n, t, a) {
                "use strict";
                a.r(t);
                var e = a("5616"),
                    i = a.n(e);
                for (var o in e)["default"].indexOf(o) < 0 && function(n) {
                    a.d(t, n, (function() {
                        return e[n]
                    }))
                }(o);
                t.default = i.a
            },
            "7c71e": function(n, t, a) {
                "use strict";
                (function(n, t) {
                    var e = a("47a9");
                    a("8f74"), e(a("3240"));
                    var i = e(a("cdcd"));
                    n.__webpack_require_UNI_MP_PLUGIN__ = a, t(i.default)
                }).call(this, a("3223").default, a("df3c").createPage)
            },
            cdcd: function(n, t, a) {
                "use strict";
                a.r(t);
                var e = a("553d"),
                    i = a("7ad3");
                for (var o in i)["default"].indexOf(o) < 0 && function(n) {
                    a.d(t, n, (function() {
                        return i[n]
                    }))
                }(o);
                a("2c17");
                var r = a("828b"),
                    c = Object(r.a)(i.default, e.b, e.c, !1, null, null, null, !1, e.a, void 0);
                t.default = c.exports
            }
        },
        [
            ["7c71e", "common/runtime", "common/vendor"]
        ]
    ]);
}, {
    isPage: true,
    isComponent: true,
    currentFile: 'pages/qbank/rank.js'
});
require("pages/qbank/rank.js");