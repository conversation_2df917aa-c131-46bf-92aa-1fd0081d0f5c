$gwx5_XC_1 = function(_, _v, _n, _p, _s, _wp, _wl, $gwn, $gwl, $gwh, wh, $gstack, $gwrt, gra, grb, TestTest, wfor, _ca, _da, _r, _rz, _o, _oz, _1, _1z, _2, _2z, _m, _mz, nv_getDate, nv_getRegExp, nv_console, nv_parseInt, nv_parseFloat, nv_isNaN, nv_isFinite, nv_decodeURI, nv_decodeURIComponent, nv_encodeURI, nv_encodeURIComponent, $gdc, nv_JSON, _af, _gv, _ai, _grp, _gd, _gapi, $ixc, _ic, _w, _ev, _tsd) {
    return function(path, global) {
        if (typeof global === 'undefined') {
            if (typeof __GWX_GLOBAL__ === 'undefined') global = {};
            else global = __GWX_GLOBAL__;
        }
        if (typeof __WXML_GLOBAL__ === 'undefined') {
            __WXML_GLOBAL__ = {};
        }
        __WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
        var e_ = {}
        if (typeof(global.entrys) === 'undefined') global.entrys = {};
        e_ = global.entrys;
        var d_ = {}
        if (typeof(global.defines) === 'undefined') global.defines = {};
        d_ = global.defines;
        var f_ = {}
        if (typeof(global.modules) === 'undefined') global.modules = {};
        f_ = global.modules || {};
        var p_ = {}
        __WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
        __WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
        __WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
        var z = __WXML_GLOBAL__.ops_set.$gwx5_XC_1 || [];

        function gz$gwx5_XC_1_1() {
            if (__WXML_GLOBAL__.ops_cached.$gwx5_XC_1_1) return __WXML_GLOBAL__.ops_cached.$gwx5_XC_1_1
            __WXML_GLOBAL__.ops_cached.$gwx5_XC_1_1 = [];
            (function(z) {
                var a = 11;

                function Z(ops) {
                    z.push(ops)
                }
                Z([3, 'data-v-0f067932'])
                Z([3, '__l'])
                Z(z[0])
                Z([
                    [7],
                    [3, 'pageTitle']
                ])
                Z([3, '0c941d55-1'])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'qbank']
                    ],
                    [3, 'id']
                ])
                Z([3, 'page-content data-v-0f067932'])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'g0']
                ])
                Z([
                    [7],
                    [3, 'adminConfig']
                ])
                Z(z[1])
                Z([3, '__e'])
                Z([
                    [7],
                    [3, 'chapters']
                ])
                Z(z[0])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^select']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'selectChapter']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([
                    [7],
                    [3, 'examPointChapters']
                ])
                Z([
                    [7],
                    [3, 'chapterHasBeenLoaded']
                ])
                Z([
                    [7],
                    [3, 'qbank']
                ])
                Z([
                    [9],
                    [
                        [8], 'fullScreen', [1, true]
                    ],
                    [
                        [8], 'radius', [1, 10]
                    ]
                ])
                Z([3, '0c941d55-2'])
                Z([
                    [2, '=='],
                    [
                        [6],
                        [
                            [7],
                            [3, 'qbank']
                        ],
                        [3, 'actionType']
                    ],
                    [1, 'paper']
                ])
                Z([
                    [7],
                    [3, 'paperHasBeenLoaded']
                ])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'g1']
                ])
                Z([3, 'paper-list data-v-0f067932'])
                Z([3, 'index'])
                Z([3, 'item'])
                Z([
                    [7],
                    [3, 'paperList']
                ])
                Z(z[23])
                Z(z[1])
                Z(z[0])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'qbank']
                    ],
                    [3, 'isAuthorized']
                ])
                Z([
                    [7],
                    [3, 'item']
                ])
                Z([
                    [2, '+'],
                    [1, '0c941d55-3-'],
                    [
                        [7],
                        [3, 'index']
                    ]
                ])
                Z(z[1])
                Z(z[0])
                Z([3, '暂无相关试卷'])
                Z([3, '30%'])
                Z([3, 'order'])
                Z([3, '0c941d55-4'])
                Z(z[1])
                Z(z[0])
                Z([
                    [2, '!'],
                    [
                        [7],
                        [3, 'loadMoreState']
                    ]
                ])
                Z([
                    [7],
                    [3, 'loadMoreState']
                ])
                Z([3, '0c941d55-5'])
                Z(z[5])
                Z([
                    [2, '!'],
                    [
                        [6],
                        [
                            [7],
                            [3, 'qbank']
                        ],
                        [3, 'isAuthorized']
                    ]
                ])
                Z([3, 'qbank-button-box data-v-0f067932'])
                Z([
                    [6],
                    [
                        [7],
                        [3, 'qbank']
                    ],
                    [3, 'price']
                ])
                Z(z[10])
                Z([3, 'qbank-button data-v-0f067932'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, 'tap']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [
                                                    [5],
                                                    [1, 'joinQbank']
                                                ],
                                                [
                                                    [4],
                                                    [
                                                        [5],
                                                        [1, 'hideTip']
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'hover'])
                Z([
                    [2, '+'],
                    [
                        [2, '+'],
                        [1, 'background:'],
                        [
                            [7],
                            [3, 'themeColor']
                        ]
                    ],
                    [1, ';']
                ])
                Z([
                    [7],
                    [3, 'hiddenPrice']
                ])
                Z(z[0])
                Z([3, '立即开通'])
                Z([3, 'price-unit data-v-0f067932'])
                Z([a, [
                    [7],
                    [3, 'BI']
                ]])
                Z(z[0])
                Z([a, [
                    [2, '+'],
                    [
                        [6],
                        [
                            [7],
                            [3, 'qbank']
                        ],
                        [3, 'price']
                    ],
                    [1, '立即开通']
                ]])
                Z(z[10])
                Z(z[48])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, 'tap']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [
                                                    [5],
                                                    [1, 'join']
                                                ],
                                                [
                                                    [4],
                                                    [
                                                        [5],
                                                        [1, '$event']
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z(z[50])
                Z(z[51])
                Z(z[0])
                Z([3, '免费开通'])
                Z(z[1])
                Z([3, 'data-v-0f067932 vue-ref'])
                Z([3, 'paperModal'])
                Z([3, '0c941d55-6'])
                Z(z[1])
                Z(z[67])
                Z([3, 'actionSheet'])
                Z([3, '0c941d55-7'])
                Z(z[1])
                Z(z[10])
                Z(z[67])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^success']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'loginSuccess']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'login'])
                Z([3, '0c941d55-8'])
                Z([
                    [7],
                    [3, 'allowPayType']
                ])
                Z([
                    [7],
                    [3, 'assistantQrcode']
                ])
                Z(z[1])
                Z(z[10])
                Z(z[67])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^success']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'paymentSuccess']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'payment'])
                Z(z[5])
                Z([3, 'questions'])
                Z([3, '0c941d55-9'])
                Z(z[1])
                Z(z[67])
                Z([3, 'openModal'])
                Z([3, '0c941d55-10'])
            })(__WXML_GLOBAL__.ops_cached.$gwx5_XC_1_1);
            return __WXML_GLOBAL__.ops_cached.$gwx5_XC_1_1
        }
        __WXML_GLOBAL__.ops_set.$gwx5_XC_1 = z;
        __WXML_GLOBAL__.ops_init.$gwx5_XC_1 = true;
        var x = ['./pages/qbank/child.wxml'];
        d_[x[0]] = {}
        var m0 = function(e, s, r, gg) {
            var z = gz$gwx5_XC_1_1()
            var fID = _n('view')
            _rz(z, fID, 'class', 0, e, s, gg)
            var oLD = _mz(z, 'nav-bar', ['bind:__l', 1, 'class', 1, 'title', 2, 'vueId', 3], [], e, s, gg)
            _(fID, oLD)
            var cJD = _v()
            _(fID, cJD)
            if (_oz(z, 5, e, s, gg)) {
                cJD.wxVkey = 1
                var cMD = _n('view')
                _rz(z, cMD, 'class', 6, e, s, gg)
                var oND = _v()
                _(cMD, oND)
                if (_oz(z, 7, e, s, gg)) {
                    oND.wxVkey = 1
                    var lOD = _mz(z, 'xk-qbank-chapter', ['adminConfig', 8, 'bind:__l', 1, 'bind:select', 2, 'chapters', 3, 'class', 4, 'data-event-opts', 5, 'examPointChapters', 6, 'loaded', 7, 'qbank', 8, 'styles', 9, 'vueId', 10], [], e, s, gg)
                    _(oND, lOD)
                } else {
                    oND.wxVkey = 2
                    var aPD = _v()
                    _(oND, aPD)
                    if (_oz(z, 19, e, s, gg)) {
                        aPD.wxVkey = 1
                        var tQD = _v()
                        _(aPD, tQD)
                        if (_oz(z, 20, e, s, gg)) {
                            tQD.wxVkey = 1
                            var eRD = _v()
                            _(tQD, eRD)
                            if (_oz(z, 21, e, s, gg)) {
                                eRD.wxVkey = 1
                                var bSD = _n('view')
                                _rz(z, bSD, 'class', 22, e, s, gg)
                                var oTD = _v()
                                _(bSD, oTD)
                                var xUD = function(fWD, oVD, cXD, gg) {
                                    var oZD = _mz(z, 'paper-item', ['bind:__l', 27, 'class', 1, 'isAuthorized', 2, 'item', 3, 'vueId', 4], [], fWD, oVD, gg)
                                    _(cXD, oZD)
                                    return cXD
                                }
                                oTD.wxXCkey = 4
                                _2z(z, 25, xUD, e, s, gg, oTD, 'item', 'index', 'index')
                                _(eRD, bSD)
                            } else {
                                eRD.wxVkey = 2
                                var c1D = _mz(z, 'xk-empty', ['bind:__l', 32, 'class', 1, 'text', 2, 'top', 3, 'type', 4, 'vueId', 5], [], e, s, gg)
                                _(eRD, c1D)
                            }
                            eRD.wxXCkey = 1
                            eRD.wxXCkey = 3
                            eRD.wxXCkey = 3
                        }
                        var o2D = _mz(z, 'load-more', ['bind:__l', 38, 'class', 1, 'data-custom-hidden', 2, 'status', 3, 'vueId', 4], [], e, s, gg)
                        _(aPD, o2D)
                        tQD.wxXCkey = 1
                        tQD.wxXCkey = 3
                    }
                    aPD.wxXCkey = 1
                    aPD.wxXCkey = 3
                }
                oND.wxXCkey = 1
                oND.wxXCkey = 3
                oND.wxXCkey = 3
                _(cJD, cMD)
            }
            var hKD = _v()
            _(fID, hKD)
            if (_oz(z, 43, e, s, gg)) {
                hKD.wxVkey = 1
                var l3D = _v()
                _(hKD, l3D)
                if (_oz(z, 44, e, s, gg)) {
                    l3D.wxVkey = 1
                    var a4D = _n('view')
                    _rz(z, a4D, 'class', 45, e, s, gg)
                    var t5D = _v()
                    _(a4D, t5D)
                    if (_oz(z, 46, e, s, gg)) {
                        t5D.wxVkey = 1
                        var e6D = _mz(z, 'view', ['bindtap', 47, 'class', 1, 'data-event-opts', 2, 'hoverClass', 3, 'style', 4], [], e, s, gg)
                        var b7D = _v()
                        _(e6D, b7D)
                        if (_oz(z, 52, e, s, gg)) {
                            b7D.wxVkey = 1
                            var o8D = _n('text')
                            _rz(z, o8D, 'class', 53, e, s, gg)
                            var x9D = _oz(z, 54, e, s, gg)
                            _(o8D, x9D)
                            _(b7D, o8D)
                        } else {
                            b7D.wxVkey = 2
                            var o0D = _n('text')
                            _rz(z, o0D, 'class', 55, e, s, gg)
                            var fAE = _oz(z, 56, e, s, gg)
                            _(o0D, fAE)
                            _(b7D, o0D)
                            var cBE = _n('text')
                            _rz(z, cBE, 'class', 57, e, s, gg)
                            var hCE = _oz(z, 58, e, s, gg)
                            _(cBE, hCE)
                            _(b7D, cBE)
                        }
                        b7D.wxXCkey = 1
                        _(t5D, e6D)
                    } else {
                        t5D.wxVkey = 2
                        var oDE = _mz(z, 'view', ['bindtap', 59, 'class', 1, 'data-event-opts', 2, 'hoverClass', 3, 'style', 4], [], e, s, gg)
                        var cEE = _n('text')
                        _rz(z, cEE, 'class', 64, e, s, gg)
                        var oFE = _oz(z, 65, e, s, gg)
                        _(cEE, oFE)
                        _(oDE, cEE)
                        _(t5D, oDE)
                    }
                    t5D.wxXCkey = 1
                    _(l3D, a4D)
                }
                l3D.wxXCkey = 1
            }
            var lGE = _mz(z, 'paper-modal', ['bind:__l', 66, 'class', 1, 'data-ref', 2, 'vueId', 3], [], e, s, gg)
            _(fID, lGE)
            var aHE = _mz(z, 'xk-action-sheet', ['bind:__l', 70, 'class', 1, 'data-ref', 2, 'vueId', 3], [], e, s, gg)
            _(fID, aHE)
            var tIE = _mz(z, 'xk-login', ['bind:__l', 74, 'bind:success', 1, 'class', 2, 'data-event-opts', 3, 'data-ref', 4, 'vueId', 5], [], e, s, gg)
            _(fID, tIE)
            var eJE = _mz(z, 'payment', ['allowPayType', 80, 'assistantQrcode', 1, 'bind:__l', 2, 'bind:success', 3, 'class', 4, 'data-event-opts', 5, 'data-ref', 6, 'productId', 7, 'productType', 8, 'vueId', 9], [], e, s, gg)
            _(fID, eJE)
            var bKE = _mz(z, 'open-modal', ['bind:__l', 90, 'class', 1, 'data-ref', 2, 'vueId', 3], [], e, s, gg)
            _(fID, bKE)
            cJD.wxXCkey = 1
            cJD.wxXCkey = 3
            hKD.wxXCkey = 1
            _(r, fID)
            return r
        }
        e_[x[0]] = {
            f: m0,
            j: [],
            i: [],
            ti: [],
            ic: []
        }
        if (path && e_[path]) {
            outerGlobal.__wxml_comp_version__ = 0.02
            return function(env, dd, global) {
                $gwxc = 0;
                var root = {
                    "tag": "wx-page"
                };
                root.children = [];
                g = "$gwx5_XC_1";
                var main = e_[path].f
                if (typeof global === "undefined") global = {};
                global.f = $gdc(f_[path], "", 1);
                if (typeof(outerGlobal.__webview_engine_version__) != 'undefined' && outerGlobal.__webview_engine_version__ + 1e-6 >= 0.02 + 1e-6 && outerGlobal.__mergeData__) {
                    env = outerGlobal.__mergeData__(env, dd);
                }
                try {
                    main(env, {}, root, global);
                    _tsd(root)
                    if (typeof(outerGlobal.__webview_engine_version__) == 'undefined' || outerGlobal.__webview_engine_version__ + 1e-6 < 0.01 + 1e-6) {
                        return _ev(root);
                    }
                } catch (err) {
                    console.log(err)
                };
                g = "";
                return root;
            }
        }
    }
}(__g.a, __g.b, __g.c, __g.d, __g.e, __g.f, __g.g, __g.h, __g.i, __g.j, __g.k, __g.l, __g.m, __g.n, __g.o, __g.p, __g.q, __g.r, __g.s, __g.t, __g.u, __g.v, __g.w, __g.x, __g.y, __g.z, __g.A, __g.B, __g.C, __g.D, __g.E, __g.F, __g.G, __g.H, __g.I, __g.J, __g.K, __g.L, __g.M, __g.N, __g.O, __g.P, __g.Q, __g.R, __g.S, __g.T, __g.U, __g.V, __g.W, __g.X, __g.Y, __g.Z, __g.aa);
if (__vd_version_info__.delayedGwx || false) $gwx5_XC_1();
if (__vd_version_info__.delayedGwx) __wxAppCode__['pages/qbank/child.wxml'] = [$gwx5_XC_1, './pages/qbank/child.wxml'];
else __wxAppCode__['pages/qbank/child.wxml'] = $gwx5_XC_1('./pages/qbank/child.wxml');

var noCss = typeof __vd_version_info__ !== 'undefined' && __vd_version_info__.noCss === true;
if (!noCss) {
    __wxAppCode__['pages/qbank/child.wxss'] = setCssToHead([".", [1], "qbank-thumb.", [1], "data-v-0f067932{height:0;padding-bottom:56.25%;position:relative;width:100%}\n.", [1], "qbank-thumb .", [1], "qbank-thumb-image.", [1], "data-v-0f067932{height:100%;position:absolute;width:100%}\n.", [1], "page-content.", [1], "data-v-0f067932{margin:12px 15px;overflow:hidden;padding-bottom:100px;position:relative}\n.", [1], "qbank-button-box.", [1], "data-v-0f067932{background-color:#fff;bottom:0;left:0;padding:8px 15px calc(8px + env(safe-area-inset-bottom));position:fixed;right:0;z-index:99}\n.", [1], "qbank-button-box .", [1], "qbank-button.", [1], "data-v-0f067932{border-radius:2px;color:#fff;font-size:14px;height:36px;letter-spacing:.6px;line-height:36px;text-align:center}\n.", [1], "qbank-button-box .", [1], "qbank-button.", [1], "hover.", [1], "data-v-0f067932{opacity:.85}\n.", [1], "qbank-button-box .", [1], "qbank-button .", [1], "price-unit.", [1], "data-v-0f067932{display:inline-block;margin-right:3px;-webkit-transform:scale(.8);transform:scale(.8)}\n", ], undefined, {
        path: "./pages/qbank/child.wxss"
    });
}