$gwx5_XC_0 = function(_, _v, _n, _p, _s, _wp, _wl, $gwn, $gwl, $gwh, wh, $gstack, $gwrt, gra, grb, TestTest, wfor, _ca, _da, _r, _rz, _o, _oz, _1, _1z, _2, _2z, _m, _mz, nv_getDate, nv_getRegExp, nv_console, nv_parseInt, nv_parseFloat, nv_isNaN, nv_isFinite, nv_decodeURI, nv_decodeURIComponent, nv_encodeURI, nv_encodeURIComponent, $gdc, nv_JSON, _af, _gv, _ai, _grp, _gd, _gapi, $ixc, _ic, _w, _ev, _tsd) {
    return function(path, global) {
        if (typeof global === 'undefined') {
            if (typeof __GWX_GLOBAL__ === 'undefined') global = {};
            else global = __GWX_GLOBAL__;
        }
        if (typeof __WXML_GLOBAL__ === 'undefined') {
            __WXML_GLOBAL__ = {};
        }
        __WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
        var e_ = {}
        if (typeof(global.entrys) === 'undefined') global.entrys = {};
        e_ = global.entrys;
        var d_ = {}
        if (typeof(global.defines) === 'undefined') global.defines = {};
        d_ = global.defines;
        var f_ = {}
        if (typeof(global.modules) === 'undefined') global.modules = {};
        f_ = global.modules || {};
        var p_ = {}
        __WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
        __WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
        __WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
        var z = __WXML_GLOBAL__.ops_set.$gwx5_XC_0 || [];

        function gz$gwx5_XC_0_1() {
            if (__WXML_GLOBAL__.ops_cached.$gwx5_XC_0_1) return __WXML_GLOBAL__.ops_cached.$gwx5_XC_0_1
            __WXML_GLOBAL__.ops_cached.$gwx5_XC_0_1 = [];
            (function(z) {
                var a = 11;

                function Z(ops) {
                    z.push(ops)
                }
                Z([3, 'data-v-f15109f2'])
                Z([
                    [2, '+'],
                    [
                        [2, '+'],
                        [1, '--single:'],
                        [
                            [7],
                            [3, 'singleColor']
                        ]
                    ],
                    [1, ';']
                ])
                Z([3, '__l'])
                Z(z[0])
                Z([
                    [7],
                    [3, 'pageTitle']
                ])
                Z([3, '9488cff8-1'])
                Z([
                    [7],
                    [3, 'dataHasBeenLoaded']
                ])
                Z([
                    [2, '>'],
                    [
                        [6],
                        [
                            [7],
                            [3, '$root']
                        ],
                        [3, 'g0']
                    ],
                    [1, 1]
                ])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'g1']
                ])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [5],
                            [
                                [5],
                                [1, 'chapter-list']
                            ],
                            [1, 'data-v-f15109f2']
                        ],
                        [
                            [7],
                            [3, 'type']
                        ]
                    ]
                ])
                Z([3, 'i'])
                Z([3, 'chapter'])
                Z([
                    [6],
                    [
                        [7],
                        [3, '$root']
                    ],
                    [3, 'l0']
                ])
                Z(z[10])
                Z([3, '__e'])
                Z([3, 'chapter-item bottom-1px data-v-f15109f2'])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, 'tap']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [
                                                    [5],
                                                    [
                                                        [5],
                                                        [1, 'selectItem']
                                                    ],
                                                    [
                                                        [4],
                                                        [
                                                            [5],
                                                            [1, '$0']
                                                        ]
                                                    ]
                                                ],
                                                [
                                                    [4],
                                                    [
                                                        [5],
                                                        [
                                                            [4],
                                                            [
                                                                [5],
                                                                [
                                                                    [4],
                                                                    [
                                                                        [5],
                                                                        [
                                                                            [5],
                                                                            [
                                                                                [5],
                                                                                [1, 'list']
                                                                            ],
                                                                            [1, '']
                                                                        ],
                                                                        [
                                                                            [7],
                                                                            [3, 'i']
                                                                        ]
                                                                    ]
                                                                ]
                                                            ]
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([
                    [2, '=='],
                    [
                        [6],
                        [
                            [6],
                            [
                                [7],
                                [3, 'chapter']
                            ],
                            [3, '$orig']
                        ],
                        [3, 'type']
                    ],
                    [1, 'classify']
                ])
                Z([
                    [2, '=='],
                    [
                        [7],
                        [3, 'type']
                    ],
                    [1, 'chapter']
                ])
                Z([3, 'chapter-right data-v-f15109f2'])
                Z(z[18])
                Z([
                    [2, '!'],
                    [
                        [7],
                        [3, 'isAuthorized']
                    ]
                ])
                Z([
                    [2, '=='],
                    [
                        [7],
                        [3, 'type']
                    ],
                    [1, 'favorite']
                ])
                Z([
                    [2, '=='],
                    [
                        [7],
                        [3, 'type']
                    ],
                    [1, 'wrong']
                ])
                Z(z[23])
                Z([
                    [2, '&&'],
                    [
                        [2, '=='],
                        [
                            [6],
                            [
                                [7],
                                [3, 'wrongQAutoRemove']
                            ],
                            [3, 'switch']
                        ],
                        [1, 1]
                    ],
                    [
                        [2, '>'],
                        [
                            [6],
                            [
                                [7],
                                [3, 'wrongQAutoRemove']
                            ],
                            [3, 'consecRightCount']
                        ],
                        [1, 0]
                    ]
                ])
                Z(z[2])
                Z(z[0])
                Z([3, '暂无相关内容'])
                Z([3, 'empty2'])
                Z([3, '9488cff8-2'])
                Z(z[2])
                Z([3, 'data-v-f15109f2 vue-ref'])
                Z([3, 'wrongModal'])
                Z([3, '9488cff8-3'])
                Z([
                    [4],
                    [
                        [5],
                        [1, 'default']
                    ]
                ])
                Z(z[2])
                Z(z[32])
                Z([3, 'actionSheet'])
                Z([3, '9488cff8-4'])
                Z(z[2])
                Z(z[14])
                Z(z[32])
                Z([
                    [4],
                    [
                        [5],
                        [
                            [4],
                            [
                                [5],
                                [
                                    [5],
                                    [1, '^success']
                                ],
                                [
                                    [4],
                                    [
                                        [5],
                                        [
                                            [4],
                                            [
                                                [5],
                                                [1, 'loginSuccess']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ])
                Z([3, 'login'])
                Z([3, '9488cff8-5'])
            })(__WXML_GLOBAL__.ops_cached.$gwx5_XC_0_1);
            return __WXML_GLOBAL__.ops_cached.$gwx5_XC_0_1
        }
        __WXML_GLOBAL__.ops_set.$gwx5_XC_0 = z;
        __WXML_GLOBAL__.ops_init.$gwx5_XC_0 = true;
        var x = ['./pages/qbank/chapter.wxml'];
        d_[x[0]] = {}
        var m0 = function(e, s, r, gg) {
            var z = gz$gwx5_XC_0_1()
            var oB = _mz(z, 'view', ['class', 0, 'style', 1], [], e, s, gg)
            var oD = _mz(z, 'nav-bar', ['bind:__l', 2, 'class', 1, 'title', 2, 'vueId', 3], [], e, s, gg)
            _(oB, oD)
            var xC = _v()
            _(oB, xC)
            if (_oz(z, 6, e, s, gg)) {
                xC.wxVkey = 1
                var fE = _v()
                _(xC, fE)
                if (_oz(z, 7, e, s, gg)) {
                    fE.wxVkey = 1
                }
                var cF = _v()
                _(xC, cF)
                if (_oz(z, 8, e, s, gg)) {
                    cF.wxVkey = 1
                    var hG = _n('view')
                    _rz(z, hG, 'class', 9, e, s, gg)
                    var cI = _v()
                    _(hG, cI)
                    var oJ = function(aL, lK, tM, gg) {
                        var bO = _mz(z, 'view', ['bindtap', 14, 'class', 1, 'data-event-opts', 2], [], aL, lK, gg)
                        var oP = _v()
                        _(bO, oP)
                        if (_oz(z, 17, aL, lK, gg)) {
                            oP.wxVkey = 1
                        } else {
                            oP.wxVkey = 2
                            var xQ = _v()
                            _(oP, xQ)
                            if (_oz(z, 18, aL, lK, gg)) {
                                xQ.wxVkey = 1
                            }
                            var cT = _n('view')
                            _rz(z, cT, 'class', 19, aL, lK, gg)
                            var hU = _v()
                            _(cT, hU)
                            if (_oz(z, 20, aL, lK, gg)) {
                                hU.wxVkey = 1
                                var oV = _v()
                                _(hU, oV)
                                if (_oz(z, 21, aL, lK, gg)) {
                                    oV.wxVkey = 1
                                }
                                oV.wxXCkey = 1
                            } else {
                                hU.wxVkey = 2
                            }
                            hU.wxXCkey = 1
                            _(oP, cT)
                            var oR = _v()
                            _(oP, oR)
                            if (_oz(z, 22, aL, lK, gg)) {
                                oR.wxVkey = 1
                            }
                            var fS = _v()
                            _(oP, fS)
                            if (_oz(z, 23, aL, lK, gg)) {
                                fS.wxVkey = 1
                            }
                            xQ.wxXCkey = 1
                            oR.wxXCkey = 1
                            fS.wxXCkey = 1
                        }
                        oP.wxXCkey = 1
                        _(tM, bO)
                        return tM
                    }
                    cI.wxXCkey = 2
                    _2z(z, 12, oJ, e, s, gg, cI, 'chapter', 'i', 'i')
                    var oH = _v()
                    _(hG, oH)
                    if (_oz(z, 24, e, s, gg)) {
                        oH.wxVkey = 1
                        var cW = _v()
                        _(oH, cW)
                        if (_oz(z, 25, e, s, gg)) {
                            cW.wxVkey = 1
                        }
                        cW.wxXCkey = 1
                    }
                    oH.wxXCkey = 1
                    _(cF, hG)
                } else {
                    cF.wxVkey = 2
                    var oX = _mz(z, 'xk-empty', ['bind:__l', 26, 'class', 1, 'text', 2, 'type', 3, 'vueId', 4], [], e, s, gg)
                    _(cF, oX)
                }
                fE.wxXCkey = 1
                cF.wxXCkey = 1
                cF.wxXCkey = 3
            }
            var lY = _mz(z, 'modal', ['bind:__l', 31, 'class', 1, 'data-ref', 2, 'vueId', 3, 'vueSlots', 4], [], e, s, gg)
            _(oB, lY)
            var aZ = _mz(z, 'xk-action-sheet', ['bind:__l', 36, 'class', 1, 'data-ref', 2, 'vueId', 3], [], e, s, gg)
            _(oB, aZ)
            var t1 = _mz(z, 'xk-login', ['bind:__l', 40, 'bind:success', 1, 'class', 2, 'data-event-opts', 3, 'data-ref', 4, 'vueId', 5], [], e, s, gg)
            _(oB, t1)
            xC.wxXCkey = 1
            xC.wxXCkey = 3
            _(r, oB)
            return r
        }
        e_[x[0]] = {
            f: m0,
            j: [],
            i: [],
            ti: [],
            ic: []
        }
        if (path && e_[path]) {
            return function(env, dd, global) {
                $gwxc = 0;
                var root = {
                    "tag": "wx-page"
                };
                root.children = [];
                g = "$gwx5_XC_0";
                var main = e_[path].f
                if (typeof global === "undefined") global = {};
                global.f = $gdc(f_[path], "", 1);
                try {
                    main(env, {}, root, global);
                    _tsd(root)
                } catch (err) {
                    console.log(err)
                };
                g = "";
                return root;
            }
        }
    }
}(__g.a, __g.b, __g.c, __g.d, __g.e, __g.f, __g.g, __g.h, __g.i, __g.j, __g.k, __g.l, __g.m, __g.n, __g.o, __g.p, __g.q, __g.r, __g.s, __g.t, __g.u, __g.v, __g.w, __g.x, __g.y, __g.z, __g.A, __g.B, __g.C, __g.D, __g.E, __g.F, __g.G, __g.H, __g.I, __g.J, __g.K, __g.L, __g.M, __g.N, __g.O, __g.P, __g.Q, __g.R, __g.S, __g.T, __g.U, __g.V, __g.W, __g.X, __g.Y, __g.Z, __g.aa);
if (__vd_version_info__.delayedGwx || false) $gwx5_XC_0();
if (__vd_version_info__.delayedGwx) __wxAppCode__['pages/qbank/chapter.wxml'] = [$gwx5_XC_0, './pages/qbank/chapter.wxml'];
else __wxAppCode__['pages/qbank/chapter.wxml'] = $gwx5_XC_0('./pages/qbank/chapter.wxml');;
__wxRoute = "pages/qbank/chapter";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/qbank/chapter.js";
define("pages/qbank/chapter.js", function(require, module, exports, window, document, frames, self, location, navigator, localStorage, history, Caches, screen, alert, confirm, prompt, XMLHttpRequest, WebSocket, Reporter, webkit, WeixinJSCore) {
    (global.webpackJsonp = global.webpackJsonp || []).push([
        ["pages/qbank/chapter"], {
            1135: function(t, e, n) {
                "use strict";
                var i = n("e179");
                n.n(i).a
            },
            "280e": function(t, e, n) {
                "use strict";
                var i = n("3e07");
                n.n(i).a
            },
            3287: function(t, e, n) {
                "use strict";
                (function(t) {
                    var i = n("47a9");
                    Object.defineProperty(e, "__esModule", {
                        value: !0
                    }), e.default = void 0;
                    var a = i(n("7eb4")),
                        o = i(n("ee10")),
                        s = i(n("af34")),
                        r = n("88d2"),
                        c = i(n("f462")),
                        u = n("62a4"),
                        h = n("7203"),
                        p = n("0938"),
                        d = n("2eff"),
                        f = {
                            components: {
                                xkActionSheet: function() {
                                    n.e("components/common/action-sheet").then(function() {
                                        return resolve(n("4fe3"))
                                    }.bind(null, n)).catch(n.oe)
                                },
                                modal: function() {
                                    n.e("components/common/modal-pro").then(function() {
                                        return resolve(n("76ee"))
                                    }.bind(null, n)).catch(n.oe)
                                }
                            },
                            data: function() {
                                return {
                                    navBarHeight: this.$statusBar + this.$customBar - 1,
                                    dataHasBeenLoaded: !1,
                                    scrollLeft: 0,
                                    qbankId: 0,
                                    chapterId: 0,
                                    chapters: [],
                                    type: "",
                                    tabs: [],
                                    activeTab: 0,
                                    statistics: {},
                                    isAuthorized: !0,
                                    parentFree: 0,
                                    wrongCount: 1,
                                    wrongRange: [
                                        ["100道/页", "200道/页", "300道/页", "400道/页", "500道/页"],
                                        [1]
                                    ],
                                    wrongChecked: !1,
                                    wrongPageSize: 100,
                                    wrongPage: 1,
                                    wrongTitle: "",
                                    exportType: "xlsx",
                                    iconList: []
                                }
                            },
                            computed: {
                                pageTitle: function() {
                                    if ("wrong" == this.type) {
                                        var t = this.iconList.find((function(t) {
                                            return "错题集" == t.name
                                        }));
                                        return t ? t.alias || t.name : "错题集"
                                    }
                                    if ("favorite" == this.type) {
                                        var e = this.iconList.find((function(t) {
                                            return "我的收藏" == t.name
                                        }));
                                        return e ? e.alias || e.name : "我的收藏"
                                    }
                                    if ("note" == this.type) {
                                        var n = this.iconList.find((function(t) {
                                            return "我的笔记" == t.name
                                        }));
                                        return n ? n.alias || n.name : "我的笔记"
                                    }
                                    if ("chapter" == this.type) {
                                        var i = this.iconList.find((function(t) {
                                            return "章节练习" == t.name
                                        }));
                                        return i ? i.alias || i.name : "章节练习"
                                    }
                                    return ""
                                },
                                allChapterTitle: function() {
                                    return "wrong" == this.type ? "全部错题" : "favorite" == this.type ? "全部收藏" : "note" == this.type ? "全部笔记" : ""
                                },
                                list: function() {
                                    if ("chapter" == this.type) {
                                        for (var t = this.chapters, e = 0; e < t.length; e++) "classify" == t[e].type && t[e].children && t[e].children.length && t.splice.apply(t, [e + 1, 0].concat((0, s.default)(t[e].children)));
                                        return t
                                    }
                                    var n = {
                                            id: 0,
                                            title: this.allChapterTitle
                                        },
                                        i = "";
                                    return "wrong" == this.type ? i = this.statistics.wrong_question || 0 : "favorite" == this.type ? i = this.statistics.favorite || 0 : "note" == this.type && (i = this.statistics.notes || 0), n[this.type + "Count"] = i, [n].concat(this.chapters)
                                },
                                currentQid: function() {
                                    return this.tabs.length ? this.tabs[this.activeTab].id : this.qbankId
                                },
                                wrongQAutoRemove: function() {
                                    return this.$store.state.adminConfig && this.$store.state.adminConfig.qbankSettings && this.$store.state.adminConfig.qbankSettings.wrongQAutoRemove || {}
                                },
                                exportStudentBool: function() {
                                    return this.$store.state.adminConfig && this.$store.state.adminConfig.qbankSettings && this.$store.state.adminConfig.qbankSettings.exportStudentBool || 0
                                },
                                wrongAllPage: function() {
                                    return Math.ceil(this.wrongCount / this.wrongPageSize)
                                },
                                qbankHomeStatType: function() {
                                    try {
                                        return this.$store.state.adminConfig.qbankSettings.qbankHomeStatType || 1
                                    } catch (t) {
                                        return 1
                                    }
                                }
                            },
                            filters: {
                                wrongChecked: function(t) {
                                    return t ? "打开" : "关闭"
                                }
                            },
                            onLoad: function(t) {
                                this.qbankId = t.qid, this.chapterId = t.cid, this.type = t.type, this.getTabs(), this.getChapter(), this.getPageTitle()
                            },
                            onShow: function() {
                                var t = this;
                                this.dataHasBeenLoaded && (this.$api.showLoading(), setTimeout((function() {
                                    t.getChapter()
                                }), 500))
                            },
                            methods: {
                                percent: function(t, e, n) {
                                    return (0, p.formartPercent)(t, e, n)
                                },
                                getPageTitle: function() {
                                    var t = this;
                                    (0, d._getAppConfig)(1).then((function(e) {
                                        if (0 === e.errno) {
                                            var n = (e.data.qbankLayout || []).find((function(t) {
                                                return "icon" == t.type
                                            }));
                                            t.iconList = n.data
                                        }
                                    }))
                                },
                                getChapter: function(t) {
                                    var e = this;
                                    this.$api.showLoading();
                                    var n = {
                                        qBankId: this.currentQid
                                    };
                                    "wrong" == this.type ? n.wrongQChapter = !0 : "favorite" == this.type ? n.favoriteChapter = !0 : "note" == this.type ? n.noteChapter = !0 : "chapter" == this.type && this.chapterId && (n.id = this.chapterId), this.$http({
                                        url: "/api/chapter",
                                        data: n,
                                        token: t || 1
                                    }).then((function(t) {
                                        e.dataHasBeenLoaded = !0, e.$api.hideLoading(), e.getStatistics(e.currentQid), 0 === t.errno ? "chapter" == e.type && e.chapterId && t.data[0] ? (e.parentFree = t.data[0].free, e.chapters = t.data[0].children) : e.chapters = t.data : 100143 === t.errno && ("chapter" == e.type ? e.getChapter(-1) : e.goLogin())
                                    }))
                                },
                                goLogin: function(t) {
                                    var e = this;
                                    (0, c.default)({
                                        component: !0,
                                        openComponent: function(t) {
                                            e.$refs.login.show()
                                        },
                                        success: function() {
                                            e.loginSuccess()
                                        }
                                    })
                                },
                                loginSuccess: function() {
                                    this.getTabs(), this.getChapter()
                                },
                                getTabs: function() {
                                    var t = this;
                                    this.qbankId && this.$http({
                                        url: "/api/q_bank",
                                        data: {
                                            id: this.qbankId
                                        }
                                    }).then(function() {
                                        var e = (0, o.default)(a.default.mark((function e(n) {
                                            var i, o;
                                            return a.default.wrap((function(e) {
                                                for (;;) switch (e.prev = e.next) {
                                                    case 0:
                                                        if (0 !== n.errno) {
                                                            e.next = 8;
                                                            break
                                                        }
                                                        if (t.isAuthorized = n.data.isAuthorized, "chapter" != t.type) {
                                                            e.next = 4;
                                                            break
                                                        }
                                                        return e.abrupt("return");
                                                    case 4:
                                                        i = n.data, o = [{
                                                            id: i.id,
                                                            title: "当前题库",
                                                            actionType: "common"
                                                        }], i.children && i.children.length && i.children.forEach((function(t) {
                                                            o.push({
                                                                id: t.id,
                                                                title: t.title,
                                                                actionType: t.actionType
                                                            })
                                                        })), t.tabs = o;
                                                    case 8:
                                                    case "end":
                                                        return e.stop()
                                                }
                                            }), e)
                                        })));
                                        return function(t) {
                                            return e.apply(this, arguments)
                                        }
                                    }())
                                },
                                changeTab: function(t) {
                                    this.activeTab = t, this.getChapter()
                                },
                                getStatistics: function(t) {
                                    var e = this;
                                    (0, r._getStatistics)(t).then((function(t) {
                                        0 === t.errno && (e.statistics = t.data)
                                    }))
                                },
                                selectItem: function(t) {
                                    if ("classify" != t.type && "wrong" != this.type && "favorite" != this.type)
                                        if ("chapter" != this.type)
                                            if ("note" != this.type) this.goQuestion(t);
                                            else {
                                                if (0 == t.noteCount) return;
                                                this.$api.openWin({
                                                    url: "/pages/qbank/notes",
                                                    params: {
                                                        qid: this.currentQid,
                                                        cid: t.id
                                                    }
                                                })
                                            } else this.goChapter(t)
                                },
                                goChapter: function(e) {
                                    var n = this;
                                    if (e)
                                        if (e.children && e.children.length) this.$api.openWin({
                                            url: "/pages/qbank/chapter",
                                            params: {
                                                qid: this.qbankId,
                                                cid: e.id,
                                                type: "chapter"
                                            }
                                        });
                                        else if ((0, u.getToken)())
                                        if (e.free || this.parentFree || this.isAuthorized) {
                                            if (0 == e.questionCount) return void setTimeout((function() {
                                                n.$api.toast("题目正在准备中")
                                            }), 200);
                                            this.goQuestion(e)
                                        } else this.$api.confirm({
                                            title: "请先开通题库",
                                            content: "该章节需要开通题库后才可以学习哦",
                                            confirmText: "开通题库",
                                            success: function(e) {
                                                e.confirm && (n.$store.commit("setData", {
                                                    type: "replace",
                                                    key: "tipBuyQbank",
                                                    data: !0
                                                }), t.reLaunch({
                                                    url: "/pages/index?tabType=qbank&qbankId=" + n.qbankId
                                                }))
                                            }
                                        });
                                    else this.goLogin()
                                },
                                doFavorite: function(t) {
                                    if ("favorite" != this.type || "examPoint" != this.tabs[this.activeTab].actionType) this.goQuestion(t);
                                    else {
                                        if (0 == t.favoriteCount) return;
                                        this.$api.openWin({
                                            url: "/pages/knowledge/index",
                                            params: {
                                                qid: this.currentQid,
                                                cid: t.id,
                                                isfavorite: 1
                                            }
                                        })
                                    }
                                },
                                viewWrong: function(t) {
                                    t.subsubType = "viewWrong", this.goQuestion(t)
                                },
                                doWrong: function(t) {
                                    t.subsubType = "doWrong", this.goQuestion(t)
                                },
                                goQuestion: function(e) {
                                    var n = this;
                                    if (0 != e[this.type + "Count"]) {
                                        var i = {
                                            type: "practice",
                                            subType: this.type,
                                            qid: this.currentQid,
                                            pageSize: e[this.type + "Count"],
                                            title: this.pageTitle
                                        };
                                        if (e.id && (i.cid = e.id), "wrong" == this.type) {
                                            i.subsubType = e.subsubType, "doWrong" == i.subsubType ? i.title = "错题重练" : "viewWrong" == i.subsubType && (i.title = "查看错题");
                                            var a = t.getStorageSync("wrongQuestionPageSize") || 100,
                                                o = e.wrongCount;
                                            if (o > a) {
                                                i.pageSize = a;
                                                for (var s = Math.ceil(o / a), r = [], c = 0; c < s; c++) r.push(a * c + 1 + "-" + (a * (c + 1) > o ? o : a * (c + 1)));
                                                this.$refs.actionSheet.open({
                                                    title: "题目过多，选择下要练习的范围吧！",
                                                    itemList: r,
                                                    itemSize: "13px",
                                                    success: function(t) {
                                                        var e = t.tapIndex + 1;
                                                        console.log("用户选择了第" + e + "页"), console.log("前往答题页"), i = Object.assign(i, {
                                                            page: e
                                                        }), n.$api.openWin({
                                                            url: "/pages/question/index",
                                                            params: i
                                                        })
                                                    }
                                                })
                                            } else this.$api.openWin({
                                                url: "/pages/question/index",
                                                params: i
                                            })
                                        } else this.$api.openWin({
                                            url: "/pages/question/index",
                                            params: i
                                        })
                                    }
                                },
                                showMoreAction: function(t, e) {
                                    var n = this;
                                    if ("fav" == e) {
                                        this.chapterId = t.id;
                                        var i;
                                        i = 0 == this.chapterId ? ["清空收藏题目"] : ["清空本章收藏题目"], this.$refs.actionSheet.open({
                                            itemList: i,
                                            success: function(e) {
                                                if (0 != t[n.type + "Count"] && (n.$api.showLoading(), 0 == e.tapIndex)) {
                                                    console.log(n.currentQid);
                                                    var i = {
                                                        type: 1
                                                    };
                                                    0 == n.chapterId ? i.qBankId = n.currentQid : i.chapterId = n.chapterId, n.$http({
                                                        method: "DELETE",
                                                        url: "/api/favorite",
                                                        data: i,
                                                        token: 1
                                                    }).then((function(t) {
                                                        n.$api.hideLoading(), 0 === t.errno && (n.$api.toast("该章节收藏题目已清空"), n.$api.sendEvent({
                                                            name: "updateQbank"
                                                        }), setTimeout((function() {
                                                            n.getChapter()
                                                        }), 300))
                                                    }))
                                                }
                                            }
                                        })
                                    } else {
                                        this.chapterId = t.id;
                                        var a = [];
                                        0 == this.chapterId ? this.exportStudentBool && (a = ["导出错题"]) : a = this.exportStudentBool ? ["导出本章错题", "清空本章错题"] : ["清空本章错题"], this.$refs.actionSheet.open({
                                            itemList: a,
                                            success: function(e) {
                                                if (0 == e.tapIndex)
                                                    if (n.exportStudentBool) {
                                                        console.log("导出"), n.wrongCount = t.wrongCount, n.wrongTitle = t.title;
                                                        for (var i = [], a = 1; a <= Math.ceil(n.wrongCount / n.wrongPageSize); a++) i.push(a);
                                                        n.wrongRange[1] = i, n.$refs.wrongModal.open({
                                                            type: "other"
                                                        })
                                                    } else o();
                                                else 1 == e.tapIndex && o()
                                            }
                                        });
                                        var o = function() {
                                            console.log("清空"), n.$api.showLoading(), n.$http({
                                                method: "DELETE",
                                                url: "/api/wrong_question",
                                                data: {
                                                    chapterId: n.chapterId
                                                },
                                                token: 1
                                            }).then((function(t) {
                                                n.$api.hideLoading(), 0 === t.errno && (n.$api.toast("该章节错题已清空"), n.$api.sendEvent({
                                                    name: "updateQbank"
                                                }), n.getChapter())
                                            }))
                                        }
                                    }
                                },
                                wrongColumnchange: function(t) {
                                    if (0 == t.detail.column) {
                                        var e = [];
                                        this.wrongPageSize = 100 * (t.detail.value + 1);
                                        for (var n = 1; n <= Math.ceil(this.wrongCount / this.wrongPageSize); n++) e.push(n);
                                        this.wrongRange[1] = e
                                    }
                                },
                                wrongChange: function(t) {
                                    this.wrongPageSize = 100 * (t.detail.value[0] + 1), this.wrongPage = t.detail.value[1] + 1
                                },
                                radioChange: function(t) {
                                    this.exportType = t.detail.value
                                },
                                changeSwitch: function(t) {
                                    this.wrongChecked = t.detail.value
                                },
                                exportWrongQuestion: function() {
                                    var e = this,
                                        n = {
                                            qBankId: this.qbankId,
                                            chapterId: this.chapterId,
                                            page: this.wrongPage,
                                            pageSize: this.wrongPageSize,
                                            exportType: this.exportType,
                                            app: !0,
                                            token: (0, u.encodeToken)()
                                        };
                                    this.wrongChecked && (n.correctRate = this.wrongChecked);
                                    var i = "".concat(this.HOST, "/api/v1/question/wrong_export_question?").concat((0, h.parsePageParams)(n));
                                    i = i.replace("http://", "https://"), t.downloadFile({
                                        url: i,
                                        success: function(n) {
                                            if (200 === n.statusCode) {
                                                var a = function() {
                                                    t.setClipboardData({
                                                        data: i,
                                                        success: function() {
                                                            e.$api.showModal({
                                                                title: "导出成功",
                                                                content: "文件路径已复制，请打开浏览器粘贴下载",
                                                                showCancel: !1,
                                                                success: function() {}
                                                            })
                                                        }
                                                    })
                                                };
                                                "windows" == t.getSystemInfoSync().platform ? a() : t.openDocument({
                                                    filePath: n.tempFilePath,
                                                    showMenu: !0,
                                                    success: function(t) {
                                                        console.log("打开文档成功")
                                                    },
                                                    fail: function() {
                                                        a()
                                                    }
                                                })
                                            } else t.showToast({
                                                title: "导出失败，请稍后重试",
                                                icon: "none"
                                            })
                                        },
                                        fail: function(e) {
                                            t.showToast({
                                                title: "导出失败，请稍后重试",
                                                icon: "none"
                                            })
                                        }
                                    })
                                },
                                deleteWrongQuestions: function() {
                                    var e = this;
                                    this.$api.confirm({
                                        title: "温馨提示",
                                        content: "是否要清空当前题库下的所有错题？",
                                        success: function(n) {
                                            n.confirm && (e.$api.showLoading(), e.$http({
                                                method: "DELETE",
                                                url: "/api/wrong_question",
                                                data: {
                                                    qBankId: e.qbankId
                                                },
                                                token: 1
                                            }).then((function(n) {
                                                e.$api.showLoading(), 0 === n.errno && (e.$api.toast("已清空，请稍候"), e.$api.sendEvent({
                                                    name: "updateQbank"
                                                }), setTimeout((function() {
                                                    t.navigateBack()
                                                }), 1e3))
                                            })))
                                        }
                                    })
                                }
                            }
                        };
                    e.default = f
                }).call(this, n("df3c").default)
            },
            "3e07": function(t, e, n) {},
            a6f0: function(t, e, n) {
                "use strict";
                n.d(e, "b", (function() {
                    return i
                })), n.d(e, "c", (function() {
                    return a
                })), n.d(e, "a", (function() {}));
                var i = function() {
                        var t = this,
                            e = (t.$createElement, t._self._c, t.dataHasBeenLoaded ? t.tabs.length : null),
                            n = t.dataHasBeenLoaded ? t.list.length : null,
                            i = t.dataHasBeenLoaded && n ? t.__map(t.list, (function(e, n) {
                                return {
                                    $orig: t.__get_orig(e),
                                    m0: "classify" != e.type && "chapter" == t.type && 1 != t.qbankHomeStatType ? t.percent(e.rightCount, e.didCount, 2) : null,
                                    m1: "classify" != e.type && "chapter" == t.type && 1 == t.qbankHomeStatType ? t.percent(e.didCount, e.questionCount, 2) : null
                                }
                            })) : null;
                        t.$mp.data = Object.assign({}, {
                            $root: {
                                g0: e,
                                g1: n,
                                l0: i
                            }
                        })
                    },
                    a = []
            },
            b271: function(t, e, n) {
                "use strict";
                n.r(e);
                var i = n("a6f0"),
                    a = n("c822");
                for (var o in a)["default"].indexOf(o) < 0 && function(t) {
                    n.d(e, t, (function() {
                        return a[t]
                    }))
                }(o);
                n("1135"), n("280e");
                var s = n("828b"),
                    r = Object(s.a)(a.default, i.b, i.c, !1, null, "f15109f2", null, !1, i.a, void 0);
                e.default = r.exports
            },
            c822: function(t, e, n) {
                "use strict";
                n.r(e);
                var i = n("3287"),
                    a = n.n(i);
                for (var o in i)["default"].indexOf(o) < 0 && function(t) {
                    n.d(e, t, (function() {
                        return i[t]
                    }))
                }(o);
                e.default = a.a
            },
            d9c4: function(t, e, n) {
                "use strict";
                (function(t, e) {
                    var i = n("47a9");
                    n("8f74"), i(n("3240"));
                    var a = i(n("b271"));
                    t.__webpack_require_UNI_MP_PLUGIN__ = n, e(a.default)
                }).call(this, n("3223").default, n("df3c").createPage)
            },
            e179: function(t, e, n) {}
        },
        [
            ["d9c4", "common/runtime", "common/vendor"]
        ]
    ]);
}, {
    isPage: true,
    isComponent: true,
    currentFile: 'pages/qbank/chapter.js'
});
require("pages/qbank/chapter.js");